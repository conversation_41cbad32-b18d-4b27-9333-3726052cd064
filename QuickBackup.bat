@echo off
chcp 65001 >nul
title Quick Backup - Aredoo POS

echo.
echo ===============================================
echo    Quick Backup - Aredoo POS System
echo ===============================================
echo.

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo Error: PowerShell not available
    pause
    exit /b 1
)

REM Run backup script silently
echo Creating backup...
echo.

powershell -ExecutionPolicy Bypass -File "CreateBackup.ps1" -BackupPath ".\Backups"

if errorlevel 1 (
    echo.
    echo Error: Backup failed
    pause
    exit /b 1
)

echo.
echo Backup completed successfully!
timeout /t 3 >nul
