@echo off
chcp 65001 > nul
title Aredoo POS System - Java

echo ═══════════════════════════════════════════════════════
echo   أريدوو - Aredoo POS System (Java Version)
echo   نظام إدارة المبيعات والمخزون
echo ═══════════════════════════════════════════════════════
echo   🚀 بدء تشغيل التطبيق...
echo ═══════════════════════════════════════════════════════

cd /d "%~dp0"

if not exist "build\libs\aredoo-pos-1.0.0.jar" (
    echo 📦 بناء التطبيق لأول مرة...
    call gradlew.bat build -x test
    if errorlevel 1 (
        echo ❌ فشل في بناء التطبيق
        pause
        exit /b 1
    )
)

echo 🌐 تشغيل الخادم على http://localhost:5000
echo 👤 المستخدم الافتراضي: admin
echo 🔑 كلمة المرور: 1234
echo ═══════════════════════════════════════════════════════
echo   اضغط Ctrl+C للإيقاف
echo ═══════════════════════════════════════════════════════

java -jar build\libs\aredoo-pos-1.0.0.jar

pause
