async function loadCustomersPage() {
    const page = document.getElementById('customersPage');
    page.innerHTML = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-people"></i> إدارة العملاء</h5>
                <button class="btn btn-primary" onclick="showCustomerModal()" data-permission-module="Customers" data-permission-action="add">
                    <i class="bi bi-plus-circle"></i> عميل جديد
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>الاسم</th>
                                <th>الهاتف</th>
                                <th>العنوان</th>
                                <th>البريد الإلكتروني</th>
                                <th>الرصيد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="customersTable"></tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Modal إضافة/تعديل عميل -->
        <div class="modal fade" id="customerModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="customerModalTitle">
                            <i class="bi bi-person-plus"></i> عميل جديد
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="customerForm">
                            <input type="hidden" id="customerId">

                            <div class="row">
                                <!-- معلومات أساسية -->
                                <div class="col-12">
                                    <h6 class="border-bottom pb-2 mb-3">
                                        <i class="bi bi-info-circle"></i> المعلومات الأساسية
                                    </h6>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-upc"></i> كود العميل *
                                    </label>
                                    <input type="text" class="form-control" id="customerCode" required
                                           placeholder="مثال: CUST001">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-person"></i> اسم العميل *
                                    </label>
                                    <input type="text" class="form-control" id="customerName" required
                                           placeholder="مثال: أحمد محمد">
                                </div>

                                <!-- معلومات الاتصال -->
                                <div class="col-12 mt-3">
                                    <h6 class="border-bottom pb-2 mb-3">
                                        <i class="bi bi-telephone"></i> معلومات الاتصال
                                    </h6>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-phone"></i> رقم الهاتف
                                    </label>
                                    <input type="tel" class="form-control" id="customerPhone"
                                           placeholder="مثال: 07701234567">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-envelope"></i> البريد الإلكتروني
                                    </label>
                                    <input type="email" class="form-control" id="customerEmail"
                                           placeholder="مثال: <EMAIL>">
                                </div>

                                <div class="col-12 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-geo-alt"></i> العنوان
                                    </label>
                                    <textarea class="form-control" id="customerAddress" rows="2"
                                              placeholder="مثال: بغداد - الكرادة"></textarea>
                                </div>

                                <!-- معلومات مالية -->
                                <div class="col-12 mt-3">
                                    <h6 class="border-bottom pb-2 mb-3">
                                        <i class="bi bi-cash-stack"></i> المعلومات المالية
                                    </h6>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-wallet2"></i> الرصيد الحالي
                                    </label>
                                    <input type="number" class="form-control" id="customerBalance"
                                           value="0" step="0.01" readonly>
                                    <small class="text-muted">يتم تحديث الرصيد تلقائياً من الفواتير</small>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-credit-card"></i> حد الائتمان
                                    </label>
                                    <input type="number" class="form-control" id="customerCreditLimit"
                                           value="0" step="0.01" placeholder="0">
                                    <small class="text-muted">الحد الأقصى للدين المسموح</small>
                                </div>

                                <!-- ملاحظات -->
                                <div class="col-12 mt-3">
                                    <h6 class="border-bottom pb-2 mb-3">
                                        <i class="bi bi-journal-text"></i> ملاحظات إضافية
                                    </h6>
                                </div>

                                <div class="col-12 mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-sticky"></i> ملاحظات
                                    </label>
                                    <textarea class="form-control" id="customerNotes" rows="3"
                                              placeholder="أي ملاحظات إضافية عن العميل..."></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle"></i> إلغاء
                        </button>
                        <button type="button" class="btn btn-primary" onclick="saveCustomer()">
                            <i class="bi bi-check-circle"></i> حفظ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    loadCustomersList();
}

async function loadCustomersList() {
    try {
        const response = await fetch(`${API_URL}/customers`);
        const customers = await response.json();

        const tbody = document.getElementById('customersTable');
        tbody.innerHTML = customers.map(c => `
            <tr>
                <td><strong>${c.code}</strong></td>
                <td>
                    <i class="bi bi-person-circle text-primary"></i> ${c.name}
                </td>
                <td>
                    ${c.phone ? `<i class="bi bi-phone"></i> ${c.phone}` : '<span class="text-muted">-</span>'}
                </td>
                <td>
                    ${c.address ? `<i class="bi bi-geo-alt"></i> ${c.address}` : '<span class="text-muted">-</span>'}
                </td>
                <td>
                    ${c.email ? `<i class="bi bi-envelope"></i> ${c.email}` : '<span class="text-muted">-</span>'}
                </td>
                <td>
                    <span class="badge ${c.balance > 0 ? 'bg-danger' : c.balance < 0 ? 'bg-success' : 'bg-secondary'}">
                        ${formatCurrency(c.balance)}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="editCustomer(${c.id})" title="تعديل" data-permission-module="Customers" data-permission-action="edit">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteCustomer(${c.id})" title="حذف" data-permission-module="Customers" data-permission-action="delete">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    } catch (error) {
        console.error('Error loading customers:', error);
    }
}

function showCustomerModal(customer = null) {
    const modal = new bootstrap.Modal(document.getElementById('customerModal'));
    const form = document.getElementById('customerForm');
    form.reset();

    document.getElementById('customerModalTitle').innerHTML = customer ?
        '<i class="bi bi-pencil"></i> تعديل عميل' :
        '<i class="bi bi-person-plus"></i> عميل جديد';

    if (customer) {
        document.getElementById('customerId').value = customer.id;
        document.getElementById('customerCode').value = customer.code || '';
        document.getElementById('customerName').value = customer.name || '';
        document.getElementById('customerPhone').value = customer.phone || '';
        document.getElementById('customerEmail').value = customer.email || '';
        document.getElementById('customerAddress').value = customer.address || '';
        document.getElementById('customerBalance').value = customer.balance || 0;
        document.getElementById('customerCreditLimit').value = customer.creditLimit || 0;
        document.getElementById('customerNotes').value = customer.notes || '';
    }

    modal.show();
}

async function saveCustomer() {
    const form = document.getElementById('customerForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const customerId = document.getElementById('customerId').value;
    const customerData = {
        code: document.getElementById('customerCode').value,
        name: document.getElementById('customerName').value,
        phone: document.getElementById('customerPhone').value || null,
        email: document.getElementById('customerEmail').value || null,
        address: document.getElementById('customerAddress').value || null,
        balance: parseFloat(document.getElementById('customerBalance').value) || 0,
        creditLimit: parseFloat(document.getElementById('customerCreditLimit').value) || 0,
        notes: document.getElementById('customerNotes').value || null
    };

    if (customerId) {
        await updateCustomer(customerId, customerData);
    } else {
        await createCustomer(customerData);
    }

    bootstrap.Modal.getInstance(document.getElementById('customerModal')).hide();
}

async function createCustomer(customer) {
    try {
        const response = await fetch(`${API_URL}/customers`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(customer)
        });
        
        if (response.ok) {
            showToast('تم إضافة العميل بنجاح', 'success');
            loadCustomersList();
        }
    } catch (error) {
        console.error('Error creating customer:', error);
    }
}

async function updateCustomer(id, customer) {
    try {
        customer.id = id;
        const response = await fetch(`${API_URL}/customers/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(customer)
        });
        
        if (response.ok) {
            showToast('تم تحديث العميل بنجاح', 'success');
            loadCustomersList();
        }
    } catch (error) {
        console.error('Error updating customer:', error);
    }
}

async function editCustomer(id) {
    try {
        const response = await fetch(`${API_URL}/customers/${id}`);
        const customer = await response.json();
        showCustomerModal(customer);
    } catch (error) {
        console.error('Error loading customer:', error);
    }
}

async function deleteCustomer(id) {
    if (confirm('هل تريد حذف هذا العميل؟')) {
        try {
            const response = await fetch(`${API_URL}/customers/${id}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                showToast('تم حذف العميل بنجاح', 'success');
                loadCustomersList();
            }
        } catch (error) {
            console.error('Error deleting customer:', error);
        }
    }
}

