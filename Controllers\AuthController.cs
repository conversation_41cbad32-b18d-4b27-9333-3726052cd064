using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Aredoo.Server.Data;
using Aredoo.Server.Models;

namespace Aredoo.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly AredooDbContext _context;

    public AuthController(AredooDbContext context)
    {
        _context = context;
    }

    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        var user = await _context.Users
            .Include(u => u.Permissions)
            .FirstOrDefaultAsync(u => u.Username == request.Username && u.IsActive);

        if (user == null || !BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
        {
            return Unauthorized(new { message = "اسم المستخدم أو كلمة المرور غير صحيحة" });
        }

        var now = DateTime.Now;
        var today = now.Date;

        // Update last login
        user.LastLogin = now;

        bool isNewShift = false;
        WorkShift? todayShift = null;
        var canUseShifts = true;

        try
        {
            // Handle work shifts: close any open shifts from previous days and ensure today's shift exists
            var openShifts = await _context.WorkShifts
                .Where(s => s.UserId == user.Id && s.EndTime == null)
                .ToListAsync();

            todayShift = openShifts.FirstOrDefault(s => s.Date == today);

            // Close any open shifts from previous days at the end of their day
            foreach (var shift in openShifts.Where(s => s.Date < today))
            {
                shift.EndTime = shift.Date.AddDays(1).AddSeconds(-1);
            }

            // If there is no open shift for today, start a new one
            if (todayShift == null)
            {
                todayShift = new WorkShift
                {
                    UserId = user.Id,
                    Date = today,
                    StartTime = now
                };
                _context.WorkShifts.Add(todayShift);
                isNewShift = true;
            }
        }
        catch (Exception ex)
        {
            canUseShifts = false;
            Console.WriteLine($"[AuthController] Work shift handling disabled due to error: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"[AuthController] Inner exception: {ex.InnerException.Message}");
            }
        }

        // Log the login
        _context.AuditLogs.Add(new AuditLog
        {
            UserId = user.Id,
            Username = user.Username,
            Action = "Login",
            Entity = "User",
            EntityId = user.Id,
            Details = "تسجيل دخول ناجح",
            CreatedAt = now
        });

        await _context.SaveChangesAsync();

        return Ok(new
        {
            id = user.Id,
            username = user.Username,
            fullName = user.FullName,
            role = user.Role,
            permissions = user.Permissions.Select(p => new
            {
                id = p.Id,
                module = p.Module,
                canView = p.CanView,
                canAdd = p.CanAdd,
                canEdit = p.CanEdit,
                canDelete = p.CanDelete,
                canPrint = p.CanPrint,
                canExport = p.CanExport
            }),
            shift = canUseShifts && todayShift != null ? new
            {
                date = todayShift.Date,
                startTime = todayShift.StartTime,
                endTime = todayShift.EndTime
            } : null,
            isNewShift = canUseShifts && isNewShift
        });
    }

    [HttpPost("logout")]
    public async Task<IActionResult> Logout([FromBody] LogoutRequest request)
    {
        var now = DateTime.Now;
        var today = now.Date;

        // Close current open shift for this user if exists
        var openShift = await _context.WorkShifts
            .Where(s => s.UserId == request.UserId && s.EndTime == null)
            .OrderByDescending(s => s.StartTime)
            .FirstOrDefaultAsync();

        if (openShift != null)
        {
            if (openShift.Date == today)
            {
                // Close at logout time for today
                openShift.EndTime = now;
            }
            else
            {
                // Close at end of its day for previous days
                openShift.EndTime = openShift.Date.AddDays(1).AddSeconds(-1);
            }
        }

        _context.AuditLogs.Add(new AuditLog
        {
            UserId = request.UserId,
            Username = request.Username,
            Action = "Logout",
            Entity = "User",
            EntityId = request.UserId,
            Details = "تسجيل خروج",
            CreatedAt = now
        });

        await _context.SaveChangesAsync();

        return Ok(new { message = "تم تسجيل الخروج بنجاح" });
    }
}

public class LoginRequest
{
    public string Username { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
}

public record LogoutRequest(int UserId, string Username);

