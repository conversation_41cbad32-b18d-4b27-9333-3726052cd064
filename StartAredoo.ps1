# تشغيل Aredoo بدون إظهار نافذة CMD

# تشغيل التطبيق في الخلفية
$process = Start-Process -FilePath "dotnet" -ArgumentList "bin\Debug\net9.0\Aredoo.Server.dll" -WindowStyle Hidden -PassThru

# الانتظار 3 ثواني
Start-Sleep -Seconds 3

# فتح المتصفح
Start-Process "http://localhost:5000"

Write-Host "✓ تم تشغيل Aredoo بنجاح!" -ForegroundColor Green
Write-Host "✓ المتصفح سيفتح تلقائياً..." -ForegroundColor Green
Write-Host ""
Write-Host "لإيقاف التطبيق، استخدم: StopAredoo.ps1" -ForegroundColor Yellow

