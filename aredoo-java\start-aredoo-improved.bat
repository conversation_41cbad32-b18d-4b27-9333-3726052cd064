@echo off
chcp 65001 > nul
title Aredoo POS System - Java

echo ═══════════════════════════════════════════════════════
echo   أريدوو - Aredoo POS System (Java Version)
echo   نظام إدارة المبيعات والمخزون
echo ═══════════════════════════════════════════════════════

cd /d "%~dp0"

REM Check if Java is installed
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Java غير مثبت على النظام
    echo يرجى تثبيت Java 17 أو أحدث من: https://adoptium.net/
    pause
    exit /b 1
)

echo ✅ Java متوفر

REM Create data directory
if not exist "data" mkdir data

REM Check for Maven first, then try to run directly
mvn -version >nul 2>&1
if not errorlevel 1 (
    echo ✅ استخدام Maven لتشغيل التطبيق...
    echo 🌐 تشغيل الخادم على http://localhost:5000
    echo 👤 المستخدم الافتراضي: admin
    echo 🔑 كلمة المرور: 1234
    echo ═══════════════════════════════════════════════════════
    echo   اضغط Ctrl+C للإيقاف
    echo ═══════════════════════════════════════════════════════
    
    REM Run with Maven Spring Boot plugin
    mvn spring-boot:run
) else (
    REM Check if JAR file exists
    if exist "build\libs\aredoo-pos-1.0.0.jar" (
        echo ✅ تشغيل من ملف JAR موجود...
        echo 🌐 تشغيل الخادم على http://localhost:5000
        echo 👤 المستخدم الافتراضي: admin
        echo 🔑 كلمة المرور: 1234
        echo ═══════════════════════════════════════════════════════
        echo   اضغط Ctrl+C للإيقاف
        echo ═══════════════════════════════════════════════════════
        
        java -jar build\libs\aredoo-pos-1.0.0.jar
    ) else if exist "target\aredoo-pos-1.0.0.jar" (
        echo ✅ تشغيل من ملف JAR موجود...
        echo 🌐 تشغيل الخادم على http://localhost:5000
        echo 👤 المستخدم الافتراضي: admin
        echo 🔑 كلمة المرور: 1234
        echo ═══════════════════════════════════════════════════════
        echo   اضغط Ctrl+C للإيقاف
        echo ═══════════════════════════════════════════════════════
        
        java -jar target\aredoo-pos-1.0.0.jar
    ) else (
        echo ❌ لا يمكن العثور على Maven أو ملف JAR جاهز
        echo.
        echo الحلول المتاحة:
        echo 1. تثبيت Maven من: https://maven.apache.org/download.cgi
        echo 2. أو بناء التطبيق باستخدام IDE مثل IntelliJ IDEA أو Eclipse
        echo 3. أو استخدام ملف JAR جاهز
        echo.
        pause
        exit /b 1
    )
)

pause
