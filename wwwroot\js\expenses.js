async function loadExpensesPage() {
    const page = document.getElementById('expensesPage');
    const isAdmin = currentUser?.role === 'Admin' || currentUser?.role === 'Manager';

    page.innerHTML = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-cash-stack"></i> ${isAdmin ? 'المصاريف' : 'مصاريفي الشخصية'}</h5>
                <button class="btn btn-primary" onclick="showAddExpenseModal()">
                    <i class="bi bi-plus-circle"></i> إضافة مصروف
                </button>
            </div>
            <div class="card-body">
                ${isAdmin ? `
                <!-- الفلاتر -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <select class="form-select" id="expenseCategory" onchange="loadExpensesList()">
                            <option value="">جميع الفئات</option>
                            <option value="إيجار">إيجار</option>
                            <option value="رواتب">رواتب</option>
                            <option value="كهرباء">كهرباء</option>
                            <option value="ماء">ماء</option>
                            <option value="صيانة">صيانة</option>
                            <option value="نقل">نقل</option>
                            <option value="اتصالات">اتصالات</option>
                            <option value="مصاريف شخصية">مصاريف شخصية</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="expenseType" onchange="loadExpensesList()">
                            <option value="">جميع الأنواع</option>
                            <option value="يومي">يومي</option>
                            <option value="أسبوعي">أسبوعي</option>
                            <option value="شهري">شهري</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" class="form-control" id="expenseFromDate" onchange="loadExpensesList()">
                    </div>
                    <div class="col-md-3">
                        <input type="date" class="form-control" id="expenseToDate" onchange="loadExpensesList()">
                    </div>
                </div>
                ` : `
                <!-- فلاتر مبسطة للموظف -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <input type="date" class="form-control" id="expenseFromDate" onchange="loadExpensesList()" placeholder="من تاريخ">
                    </div>
                    <div class="col-md-6">
                        <input type="date" class="form-control" id="expenseToDate" onchange="loadExpensesList()" placeholder="إلى تاريخ">
                    </div>
                </div>
                `}

                <!-- الإحصائيات السريعة -->
                <div class="row mb-3" id="expenseStats"></div>

                <!-- جدول المصاريف -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>#</th>
                                <th>التاريخ</th>
                                <th>الوصف</th>
                                <th>الفئة</th>
                                <th>النوع</th>
                                <th>المبلغ</th>
                                <th>ملاحظات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="expensesTable"></tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Modal إضافة/تعديل مصروف -->
        <div class="modal fade" id="expenseModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="expenseModalTitle">إضافة مصروف</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="expenseForm">
                            <input type="hidden" id="expenseId">

                            <div class="mb-3">
                                <label class="form-label">الوصف *</label>
                                <input type="text" class="form-control" id="expenseDescription" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">المبلغ *</label>
                                <input type="number" class="form-control" id="expenseAmount" step="0.01" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">التاريخ *</label>
                                <input type="date" class="form-control" id="expenseDate" required>
                            </div>

                            <div class="mb-3" id="expenseCategoryDiv">
                                <label class="form-label">الفئة *</label>
                                <select class="form-select" id="expenseCategoryInput" required>
                                    <option value="">اختر الفئة</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">النوع *</label>
                                <select class="form-select" id="expenseTypeInput" required>
                                    <option value="">اختر النوع</option>
                                    <option value="يومي">يومي</option>
                                    <option value="أسبوعي">أسبوعي</option>
                                    <option value="شهري">شهري</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="expenseNotes" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveExpense()">حفظ</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // تعيين التاريخ الافتراضي
    document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];

    loadExpensesList();
    loadExpensesSummary();
}




async function loadExpensesList() {
    try {
        const category = document.getElementById('expenseCategory')?.value || '';
        const type = document.getElementById('expenseType')?.value || '';
        const fromDate = document.getElementById('expenseFromDate')?.value || '';
        const toDate = document.getElementById('expenseToDate')?.value || '';

        let url = `${API_URL}/expenses?`;
        if (category) url += `category=${category}&`;
        if (type) url += `type=${type}&`;
        if (fromDate) url += `fromDate=${fromDate}&`;
        if (toDate) url += `toDate=${toDate}&`;

        // إضافة معلومات المستخدم للصلاحيات
        if (currentUser) {
            url += `userId=${currentUser.id}&`;
            url += `userRole=${currentUser.role}&`;
        }

        const response = await fetch(url);
        const expenses = await response.json();

        const tbody = document.getElementById('expensesTable');
        tbody.innerHTML = expenses.map((exp, index) => `
            <tr>
                <td>${index + 1}</td>
                <td>${formatDate(exp.expenseDate)}</td>
                <td><strong>${exp.description}</strong></td>
                <td><span class="badge ${getCategoryBadge(exp.category)}">${exp.category}</span></td>
                <td><span class="badge ${getTypeBadge(exp.type)}">${exp.type}</span></td>
                <td><strong class="text-danger">${formatCurrency(exp.amount)}</strong></td>
                <td>${exp.notes || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-warning" onclick="editExpense(${exp.id})" title="تعديل">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteExpense(${exp.id})" title="حذف">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');

        loadExpensesSummary();
    } catch (error) {
        console.error('Error loading expenses:', error);
        showToast('خطأ في تحميل المصاريف', 'error');
    }
}

async function loadExpensesSummary() {
    try {
        const fromDate = document.getElementById('expenseFromDate')?.value || '';
        const toDate = document.getElementById('expenseToDate')?.value || '';

        let url = `${API_URL}/expenses/summary?`;
        if (fromDate) url += `fromDate=${fromDate}&`;
        if (toDate) url += `toDate=${toDate}&`;

        // إضافة معلومات المستخدم للصلاحيات
        if (currentUser) {
            url += `userId=${currentUser.id}&`;
            url += `userRole=${currentUser.role}&`;
        }

        const response = await fetch(url);
        const summary = await response.json();

        const statsDiv = document.getElementById('expenseStats');
        statsDiv.innerHTML = `
            <div class="col-md-4">
                <div class="card bg-danger text-white">
                    <div class="card-body">
                        <h6>إجمالي المصاريف</h6>
                        <h3>${formatCurrency(summary.total)}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <h6>حسب الفئة</h6>
                        ${summary.byCategory.slice(0, 3).map(c => `
                            <div class="d-flex justify-content-between">
                                <span>${c.category}</span>
                                <strong>${formatCurrency(c.total)}</strong>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-warning text-dark">
                    <div class="card-body">
                        <h6>حسب النوع</h6>
                        ${summary.byType.map(t => `
                            <div class="d-flex justify-content-between">
                                <span>${t.type}</span>
                                <strong>${formatCurrency(t.total)}</strong>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('Error loading summary:', error);
    }
}

function showAddExpenseModal() {
    document.getElementById('expenseModalTitle').textContent = 'إضافة مصروف';
    document.getElementById('expenseForm').reset();
    document.getElementById('expenseId').value = '';
    document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];

    // تحديد الفئات حسب صلاحية المستخدم
    const categorySelect = document.getElementById('expenseCategoryInput');
    const isAdmin = currentUser?.role === 'Admin' || currentUser?.role === 'Manager';

    if (isAdmin) {
        // الأدمن يرى جميع الفئات
        categorySelect.innerHTML = `
            <option value="">اختر الفئة</option>
            <option value="إيجار">إيجار</option>
            <option value="رواتب">رواتب</option>
            <option value="كهرباء">كهرباء</option>
            <option value="ماء">ماء</option>
            <option value="صيانة">صيانة</option>
            <option value="نقل">نقل</option>
            <option value="اتصالات">اتصالات</option>
            <option value="مصاريف شخصية">مصاريف شخصية</option>
            <option value="أخرى">أخرى</option>
        `;
    } else {
        // الموظف العادي يرى فقط "مصاريف شخصية"
        categorySelect.innerHTML = `
            <option value="مصاريف شخصية" selected>مصاريف شخصية</option>
        `;
        categorySelect.disabled = true;
    }

    const modal = new bootstrap.Modal(document.getElementById('expenseModal'));
    modal.show();
}

async function editExpense(id) {
    try {
        const response = await fetch(`${API_URL}/expenses/${id}`);
        const expense = await response.json();

        document.getElementById('expenseModalTitle').textContent = 'تعديل مصروف';
        document.getElementById('expenseId').value = expense.id;
        document.getElementById('expenseDescription').value = expense.description;
        document.getElementById('expenseAmount').value = expense.amount;
        document.getElementById('expenseDate').value = expense.expenseDate.split('T')[0];
        document.getElementById('expenseCategoryInput').value = expense.category;
        document.getElementById('expenseTypeInput').value = expense.type;
        document.getElementById('expenseNotes').value = expense.notes || '';

        const modal = new bootstrap.Modal(document.getElementById('expenseModal'));
        modal.show();
    } catch (error) {
        console.error('Error loading expense:', error);
        showToast('خطأ في تحميل بيانات المصروف', 'error');
    }
}

async function saveExpense() {
    const id = document.getElementById('expenseId').value;
    const isAdmin = currentUser?.role === 'Admin' || currentUser?.role === 'Manager';

    const expense = {
        description: document.getElementById('expenseDescription').value,
        amount: parseFloat(document.getElementById('expenseAmount').value),
        expenseDate: document.getElementById('expenseDate').value,
        category: document.getElementById('expenseCategoryInput').value,
        type: document.getElementById('expenseTypeInput').value,
        notes: document.getElementById('expenseNotes').value,
        createdBy: currentUser?.id || 1,
        isPersonal: !isAdmin || document.getElementById('expenseCategoryInput').value === 'مصاريف شخصية'
    };

    if (!expense.description || !expense.amount || !expense.expenseDate || !expense.category || !expense.type) {
        showToast('الرجاء تعبئة جميع الحقول المطلوبة', 'warning');
        return;
    }

    try {
        let url = id ? `${API_URL}/expenses/${id}` : `${API_URL}/expenses`;
        const method = id ? 'PUT' : 'POST';

        // إضافة userRole للـ POST فقط
        if (!id && currentUser) {
            url += `?userRole=${currentUser.role}`;
        }

        const response = await fetch(url, {
            method: method,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(expense)
        });

        if (response.ok) {
            showToast(id ? 'تم تحديث المصروف بنجاح' : 'تم إضافة المصروف بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('expenseModal')).hide();
            loadExpensesList();
        } else {
            showToast('خطأ في حفظ المصروف', 'error');
        }
    } catch (error) {
        console.error('Error saving expense:', error);
        showToast('خطأ في حفظ المصروف', 'error');
    }
}

async function deleteExpense(id) {
    if (!confirm('هل أنت متأكد من حذف هذا المصروف؟')) return;

    try {
        const response = await fetch(`${API_URL}/expenses/${id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showToast('تم حذف المصروف بنجاح', 'success');
            loadExpensesList();
        } else {
            showToast('خطأ في حذف المصروف', 'error');
        }
    } catch (error) {
        console.error('Error deleting expense:', error);
        showToast('خطأ في حذف المصروف', 'error');
    }
}

function getCategoryBadge(category) {
    const badges = {
        'إيجار': 'bg-primary',
        'رواتب': 'bg-success',
        'كهرباء': 'bg-warning',
        'ماء': 'bg-info',
        'صيانة': 'bg-danger',
        'نقل': 'bg-secondary',
        'اتصالات': 'bg-dark',
        'أخرى': 'bg-light text-dark'
    };
    return badges[category] || 'bg-secondary';
}

function getTypeBadge(type) {
    const badges = {
        'يومي': 'bg-success',
        'أسبوعي': 'bg-warning',
        'شهري': 'bg-danger'
    };
    return badges[type] || 'bg-secondary';
}
