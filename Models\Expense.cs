namespace Aredoo.Server.Models;

public class Expense
{
    public int Id { get; set; }
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public DateTime ExpenseDate { get; set; }
    public string Category { get; set; } = string.Empty; // إيجار، رواتب، كهرباء، ماء، صيانة، أخرى
    public string Type { get; set; } = string.Empty; // يومي، أسبوعي، شهري
    public string? Notes { get; set; }
    public int CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool IsDeleted { get; set; }
    public bool IsPersonal { get; set; } = false; // true = مصروف شخصي للموظف، false = مصروف عام للإدارة
}

