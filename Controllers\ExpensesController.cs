using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Aredoo.Server.Data;
using Aredoo.Server.Models;

namespace Aredoo.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ExpensesController : ControllerBase
{
    private readonly AredooDbContext _context;

    public ExpensesController(AredooDbContext context)
    {
        _context = context;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll(
        [FromQuery] string? category = null,
        [FromQuery] string? type = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int? userId = null,
        [FromQuery] string? userRole = null)
    {
        var query = _context.Expenses
            .Where(e => !e.IsDeleted)
            .AsQueryable();

        // إذا كان المستخدم موظف عادي (ليس Admin أو Manager)، يرى فقط مصاريفه الشخصية
        if (!string.IsNullOrEmpty(userRole) && userRole != "Admin" && userRole != "Manager" && userId.HasValue)
        {
            query = query.Where(e => e.IsPersonal && e.CreatedBy == userId.Value);
        }

        if (!string.IsNullOrEmpty(category))
            query = query.Where(e => e.Category == category);

        if (!string.IsNullOrEmpty(type))
            query = query.Where(e => e.Type == type);

        if (fromDate.HasValue)
            query = query.Where(e => e.ExpenseDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(e => e.ExpenseDate <= toDate.Value);

        var expenses = await query
            .OrderByDescending(e => e.ExpenseDate)
            .ToListAsync();

        return Ok(expenses);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id)
    {
        var expense = await _context.Expenses
            .FirstOrDefaultAsync(e => e.Id == id && !e.IsDeleted);

        if (expense == null)
            return NotFound();

        return Ok(expense);
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] Expense expense, [FromQuery] string? userRole = null)
    {
        expense.CreatedAt = DateTime.Now;
        expense.IsDeleted = false;

        // إذا كان المستخدم موظف عادي، يجب أن يكون المصروف شخصي
        if (!string.IsNullOrEmpty(userRole) && userRole != "Admin" && userRole != "Manager")
        {
            expense.IsPersonal = true;
        }

        _context.Expenses.Add(expense);
        await _context.SaveChangesAsync();

        return CreatedAtAction(nameof(GetById), new { id = expense.Id }, expense);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] Expense expense)
    {
        var existingExpense = await _context.Expenses.FindAsync(id);
        if (existingExpense == null || existingExpense.IsDeleted)
            return NotFound();

        existingExpense.Description = expense.Description;
        existingExpense.Amount = expense.Amount;
        existingExpense.ExpenseDate = expense.ExpenseDate;
        existingExpense.Category = expense.Category;
        existingExpense.Type = expense.Type;
        existingExpense.Notes = expense.Notes;

        await _context.SaveChangesAsync();
        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        var expense = await _context.Expenses.FindAsync(id);
        if (expense == null)
            return NotFound();

        expense.IsDeleted = true;
        await _context.SaveChangesAsync();

        return NoContent();
    }

    [HttpGet("summary")]
    public async Task<IActionResult> GetSummary(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int? userId = null,
        [FromQuery] string? userRole = null)
    {
        var query = _context.Expenses
            .Where(e => !e.IsDeleted)
            .AsQueryable();

        // إذا كان المستخدم موظف عادي، يرى فقط مصاريفه الشخصية
        if (!string.IsNullOrEmpty(userRole) && userRole != "Admin" && userRole != "Manager" && userId.HasValue)
        {
            query = query.Where(e => e.IsPersonal && e.CreatedBy == userId.Value);
        }

        if (fromDate.HasValue)
            query = query.Where(e => e.ExpenseDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(e => e.ExpenseDate <= toDate.Value);

        var expenses = await query.ToListAsync();

        var summary = new
        {
            total = expenses.Sum(e => e.Amount),
            byCategory = expenses.GroupBy(e => e.Category)
                .Select(g => new { category = g.Key, total = g.Sum(e => e.Amount), count = g.Count() })
                .OrderByDescending(x => x.total)
                .ToList(),
            byType = expenses.GroupBy(e => e.Type)
                .Select(g => new { type = g.Key, total = g.Sum(e => e.Amount), count = g.Count() })
                .OrderByDescending(x => x.total)
                .ToList()
        };

        return Ok(summary);
    }

    [HttpPost("init-table")]
    public async Task<IActionResult> InitTable()
    {
        try
        {
            await _context.Database.ExecuteSqlRawAsync(@"
                CREATE TABLE IF NOT EXISTS Expenses (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Description TEXT NOT NULL,
                    Amount REAL NOT NULL,
                    ExpenseDate TEXT NOT NULL,
                    Category TEXT NOT NULL,
                    Type TEXT NOT NULL,
                    Notes TEXT,
                    CreatedBy INTEGER NOT NULL,
                    CreatedAt TEXT NOT NULL,
                    IsDeleted INTEGER NOT NULL DEFAULT 0,
                    IsPersonal INTEGER NOT NULL DEFAULT 0,
                    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
                );

                CREATE INDEX IF NOT EXISTS IX_Expenses_ExpenseDate ON Expenses(ExpenseDate);
                CREATE INDEX IF NOT EXISTS IX_Expenses_Category ON Expenses(Category);
                CREATE INDEX IF NOT EXISTS IX_Expenses_Type ON Expenses(Type);
                CREATE INDEX IF NOT EXISTS IX_Expenses_IsDeleted ON Expenses(IsDeleted);
                CREATE INDEX IF NOT EXISTS IX_Expenses_IsPersonal ON Expenses(IsPersonal);
            ");

            return Ok(new { message = "تم إنشاء جدول المصاريف بنجاح" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "خطأ في إنشاء الجدول", error = ex.Message });
        }
    }

    [HttpPost("update-table")]
    public async Task<IActionResult> UpdateTable()
    {
        try
        {
            // إضافة حقل IsPersonal إذا لم يكن موجود
            await _context.Database.ExecuteSqlRawAsync(@"
                ALTER TABLE Expenses ADD COLUMN IsPersonal INTEGER NOT NULL DEFAULT 0;
                CREATE INDEX IF NOT EXISTS IX_Expenses_IsPersonal ON Expenses(IsPersonal);
            ");

            return Ok(new { message = "تم تحديث جدول المصاريف بنجاح" });
        }
        catch (Exception ex)
        {
            // إذا كان الحقل موجود بالفعل، سيحدث خطأ لكن لا مشكلة
            return Ok(new { message = "الجدول محدث بالفعل أو حدث خطأ", error = ex.Message });
        }
    }
}


