// Authentication and Authorization Management

// Get current user from localStorage
function getCurrentUser() {
    const userStr = localStorage.getItem('currentUser');
    return userStr ? JSON.parse(userStr) : null;
}

// Check if user is logged in
function isLoggedIn() {
    return getCurrentUser() !== null;
}

// Logout
function logout() {
    localStorage.removeItem('currentUser');
    window.location.href = 'login.html';
}

// Check if user has permission
function hasPermission(module, action) {
    const user = getCurrentUser();
    if (!user) return false;
    
    // Admin has all permissions
    if (user.role === 'Admin') return true;
    
    // Check specific permission
    const permission = user.permissions?.find(p => p.module === module);
    if (!permission) return false;
    
    switch (action.toLowerCase()) {
        case 'view': return permission.canView;
        case 'add': return permission.canAdd;
        case 'edit': return permission.canEdit;
        case 'delete': return permission.canDelete;
        case 'print': return permission.canPrint;
        case 'export': return permission.canExport;
        default: return false;
    }
}

// Apply permissions to UI
function applyPermissions() {
    const user = getCurrentUser();
    if (!user) {
        window.location.href = 'login.html';
        return;
    }

    // Update user info in header
    const userNameElement = document.getElementById('currentUserName');
    const userRoleElement = document.getElementById('currentUserRole');
    if (userNameElement) userNameElement.textContent = user.fullName;
    if (userRoleElement) userRoleElement.textContent = user.role;

    // Hide menu items based on permissions
    const menuItems = {
        'menu-sales': 'Sales',
        'menu-products': 'Products',
        'menu-categories': 'Categories',
        'menu-customers': 'Customers',
        'menu-suppliers': 'Suppliers',
        'menu-invoices': 'Invoices',
        'menu-employees': 'Employees',
        'menu-reports': 'Reports',
        'menu-settings': 'Settings',
        'menu-purchases': 'Purchases'
    };

    for (const [menuId, module] of Object.entries(menuItems)) {
        const menuElement = document.getElementById(menuId);
        if (menuElement) {
            if (!hasPermission(module, 'view')) {
                menuElement.style.display = 'none';
            }
        }
    }

    // Hide buttons based on permissions (run after a short delay to ensure DOM is ready)
    setTimeout(() => {
        document.querySelectorAll('[data-permission-module]').forEach(button => {
            const module = button.getAttribute('data-permission-module');
            const action = button.getAttribute('data-permission-action');

            if (!hasPermission(module, action)) {
                button.style.display = 'none';
            }
        });
    }, 100);
}

// Re-apply permissions (call this after loading any page content)
function reapplyPermissions() {
    setTimeout(() => {
        document.querySelectorAll('[data-permission-module]').forEach(button => {
            const module = button.getAttribute('data-permission-module');
            const action = button.getAttribute('data-permission-action');

            if (!hasPermission(module, action)) {
                button.style.display = 'none';
            }
        });
    }, 100);
}

// Initialize auth on page load
document.addEventListener('DOMContentLoaded', () => {
    // Skip auth check on login page
    if (window.location.pathname.includes('login.html')) {
        return;
    }

    // Check if user is logged in
    if (!isLoggedIn()) {
        window.location.href = 'login.html';
        return;
    }

    // Apply permissions
    applyPermissions();
});

