try {
    Write-Host "Checking backup file contents..."
    
    $backupPath = ".\Backups\aredoo_backup_20251119_193800.zip"
    
    if (-not (Test-Path $backupPath)) {
        Write-Host "Backup file not found: $backupPath" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Found backup file: $backupPath"
    Write-Host "File size: $((Get-Item $backupPath).Length) bytes"
    
    # Extract and check contents
    $tempDir = [System.IO.Path]::GetTempPath() + [System.Guid]::NewGuid().ToString()
    New-Item -ItemType Directory -Path $tempDir | Out-Null
    
    Write-Host "Extracting to: $tempDir"
    
    # Extract zip file
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($backupPath, $tempDir)
    
    Write-Host "`nBackup contents:"
    Get-ChildItem -Path $tempDir -Recurse | ForEach-Object {
        $relativePath = $_.FullName.Substring($tempDir.Length + 1)
        if ($_.PSIsContainer) {
            Write-Host "  [DIR]  $relativePath" -ForegroundColor Blue
        } else {
            Write-Host "  [FILE] $relativePath ($($_.Length) bytes)" -ForegroundColor Green
        }
    }
    
    # Check backup info
    $backupInfoPath = Join-Path $tempDir "backup_info.json"
    if (Test-Path $backupInfoPath) {
        Write-Host "`nBackup Info:"
        Get-Content $backupInfoPath | ConvertFrom-Json | ConvertTo-Json -Depth 3
    }
    
    # Check database file
    $dbPath = Join-Path $tempDir "data\aredoo.db"
    if (Test-Path $dbPath) {
        Write-Host "`nDatabase file size: $((Get-Item $dbPath).Length) bytes"
    } else {
        Write-Host "`nDatabase file not found!" -ForegroundColor Red
    }
    
    # Cleanup
    Remove-Item -Path $tempDir -Recurse -Force
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
