using Microsoft.EntityFrameworkCore;
using Aredoo.Server.Models;

namespace Aredoo.Server.Data;

public static class DbInitializer
{
    public static void Initialize(AredooDbContext context)
    {
        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        context.Database.EnsureCreated();

        // تطبيق أي تحديثات معلقة
        if (context.Database.GetPendingMigrations().Any())
        {
            context.Database.Migrate();
        }

        // إضافة البيانات الأولية
        SeedData(context);
    }

    private static void SeedData(AredooDbContext context)
    {
        // 1. إنشاء مستخدم افتراضي (admin)
        if (!context.Users.Any())
        {
            var adminUser = new User
            {
                Username = "admin",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("1234"),
                FullName = "المدير العام",
                Role = "Admin",
                IsActive = true,
                CreatedAt = DateTime.Now
            };
            context.Users.Add(adminUser);
            context.SaveChanges();

            Console.WriteLine("✓ تم إنشاء المستخدم الافتراضي: admin / 1234");

            // إضافة صلاحيات كاملة للمدير
            var modules = new[] { "Products", "Categories", "Customers", "Suppliers", "Purchases", "Sales", "Invoices", "Reports", "Settings", "Users", "Permissions" };

            foreach (var module in modules)
            {
                context.Permissions.Add(new Permission
                {
                    UserId = adminUser.Id,
                    Module = module,
                    CanView = true,
                    CanAdd = true,
                    CanEdit = true,
                    CanDelete = true,
                    CanPrint = true,
                    CanExport = true
                });
            }
            context.SaveChanges();
            Console.WriteLine("✓ تم إضافة صلاحيات كاملة للمدير");
        }

        // 2. إنشاء فئات افتراضية
        if (!context.Categories.Any())
        {
            var categories = new[]
            {
                new Category { Name = "إلكترونيات", Icon = "laptop", Color = "primary", IsActive = true },
                new Category { Name = "ملابس", Icon = "bag", Color = "success", IsActive = true },
                new Category { Name = "أغذية", Icon = "basket", Color = "warning", IsActive = true },
                new Category { Name = "مشروبات", Icon = "cup", Color = "info", IsActive = true },
                new Category { Name = "أدوات منزلية", Icon = "house", Color = "secondary", IsActive = true },
                new Category { Name = "قرطاسية", Icon = "pencil", Color = "danger", IsActive = true },
                new Category { Name = "مستحضرات تجميل", Icon = "heart", Color = "pink", IsActive = true },
                new Category { Name = "أخرى", Icon = "grid", Color = "dark", IsActive = true }
            };

            context.Categories.AddRange(categories);
            context.SaveChanges();
            Console.WriteLine($"✓ تم إضافة {categories.Length} فئات افتراضية");
        }

        // 3. إنشاء إعدادات افتراضية
        if (!context.Settings.Any())
        {
            var settings = new Settings
            {
                CompanyName = "أريدوو - Aredoo",
                CompanyNameAr = "أريدوو",
                Address = "العنوان",
                Phone = "0000000000",
                Email = "<EMAIL>",
                Currency = "IQD",
                CurrencySymbol = "د.ع",
                Language = "ar",
                InvoiceFooter = "شكراً لتعاملكم معنا",
                PrinterName = "Default",
                PaperSize = "80mm",
                AutoPrint = false,
                TaxRate = 0m,
                UpdatedAt = DateTime.Now
            };

            context.Settings.Add(settings);
            context.SaveChanges();
            Console.WriteLine("✓ تم إضافة الإعدادات الافتراضية");
        }

        // 4. إضافة منتجات تجريبية (اختياري)
        if (!context.Products.Any())
        {
            var sampleProducts = new[]
            {
                new Product
                {
                    Code = "P001",
                    Name = "منتج تجريبي 1",
                    Barcode = "2012345678905",
                    Category = "أخرى",
                    Unit = "قطعة",
                    PurchasePrice = 10.00m,
                    SalePrice = 15.00m,
                    WholesalePrice = 12.00m,
                    Quantity = 100,
                    MinQuantity = 10,
                    IsActive = true
                },
                new Product
                {
                    Code = "P002",
                    Name = "منتج تجريبي 2",
                    Barcode = "2098765432109",
                    Category = "أخرى",
                    Unit = "قطعة",
                    PurchasePrice = 20.00m,
                    SalePrice = 30.00m,
                    WholesalePrice = 25.00m,
                    Quantity = 50,
                    MinQuantity = 5,
                    IsActive = true
                }
            };

            context.Products.AddRange(sampleProducts);
            context.SaveChanges();
            Console.WriteLine($"✓ تم إضافة {sampleProducts.Length} منتجات تجريبية");
        }

        Console.WriteLine("═══════════════════════════════════════════════════════");
        Console.WriteLine("  ✓ تم تهيئة قاعدة البيانات بنجاح");
        Console.WriteLine("═══════════════════════════════════════════════════════");
    }
}

