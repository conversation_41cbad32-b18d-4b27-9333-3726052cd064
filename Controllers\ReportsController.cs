using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Aredoo.Server.Data;

namespace Aredoo.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ReportsController : ControllerBase
{
    private readonly AredooDbContext _context;

    public ReportsController(AredooDbContext context)
    {
        _context = context;
    }

    [HttpGet("sales-summary")]
    public async Task<IActionResult> GetSalesSummary(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.Invoices
            .Where(i => !i.IsDeleted && i.Type == "Sale");

        if (fromDate.HasValue)
            query = query.Where(i => i.InvoiceDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(i => i.InvoiceDate <= toDate.Value);

        var invoices = await query.ToListAsync();

        var summary = new
        {
            totalInvoices = invoices.Count,
            totalSales = invoices.Sum(i => i.Total),
            totalPaid = invoices.Sum(i => i.Paid),
            totalRemaining = invoices.Sum(i => i.Remaining),
            totalDiscount = invoices.Sum(i => i.Discount),
            cashSales = invoices.Where(i => i.PaymentType == "Cash").Sum(i => i.Total),
            creditSales = invoices.Where(i => i.PaymentType == "Credit").Sum(i => i.Total),
            wholesaleSales = invoices.Where(i => i.PaymentType == "Wholesale").Sum(i => i.Total),
            installmentSales = invoices.Where(i => i.PaymentType == "Installment").Sum(i => i.Total)
        };

        return Ok(summary);
    }

    [HttpGet("sales-by-product")]
    public async Task<IActionResult> GetSalesByProduct(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.InvoiceItems
            .Include(ii => ii.Invoice)
            .Include(ii => ii.Product)
            .Where(ii => !ii.Invoice!.IsDeleted && ii.Invoice.Type == "Sale");

        if (fromDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate <= toDate.Value);

        var items = await query.ToListAsync();

        var report = items
            .GroupBy(ii => new { ii.ProductId, ii.ProductName })
            .Select(g => new
            {
                productId = g.Key.ProductId,
                productName = g.Key.ProductName,
                quantity = g.Sum(ii => ii.Quantity),
                total = g.Sum(ii => ii.Total),
                profit = g.Sum(ii => ii.Profit)
            })
            .OrderByDescending(x => x.total)
            .ToList();

        return Ok(report);
    }

    [HttpGet("sales-by-customer")]
    public async Task<IActionResult> GetSalesByCustomer(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.Invoices
            .Include(i => i.Customer)
            .Where(i => !i.IsDeleted && i.Type == "Sale" && i.CustomerId.HasValue);

        if (fromDate.HasValue)
            query = query.Where(i => i.InvoiceDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(i => i.InvoiceDate <= toDate.Value);

        var invoices = await query.ToListAsync();

        var report = invoices
            .GroupBy(i => new { i.CustomerId, CustomerName = i.Customer!.Name })
            .Select(g => new
            {
                customerId = g.Key.CustomerId,
                customerName = g.Key.CustomerName,
                invoiceCount = g.Count(),
                total = g.Sum(i => i.Total),
                paid = g.Sum(i => i.Paid),
                remaining = g.Sum(i => i.Remaining)
            })
            .OrderByDescending(x => x.total)
            .ToList();

        return Ok(report);
    }

    [HttpGet("profit")]
    public async Task<IActionResult> GetProfit(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.InvoiceItems
            .Include(ii => ii.Invoice)
            .Where(ii => !ii.Invoice!.IsDeleted && ii.Invoice.Type == "Sale");

        if (fromDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate <= toDate.Value);

        var items = await query.ToListAsync();

        var totalProfit = items.Sum(ii => ii.Profit);
        var totalSales = items.Sum(ii => ii.Total);
        var totalCost = items.Sum(ii => ii.PurchasePrice * ii.Quantity);

        return Ok(new
        {
            totalSales,
            totalCost,
            totalProfit,
            profitMargin = totalSales > 0 ? (totalProfit / totalSales) * 100 : 0
        });
    }

    [HttpGet("inventory")]
    public async Task<IActionResult> GetInventory()
    {
        var products = await _context.Products
            .Where(p => p.IsActive)
            .OrderBy(p => p.Name)
            .ToListAsync();

        var report = products.Select(p => new
        {
            p.Id,
            p.Code,
            p.Name,
            p.Category,
            p.Quantity,
            p.PurchasePrice,
            p.SalePrice,
            totalValue = p.Quantity * p.PurchasePrice,
            expectedRevenue = p.Quantity * p.SalePrice
        }).ToList();

        return Ok(report);
    }

    [HttpGet("profit-by-period")]
    public async Task<IActionResult> GetProfitByPeriod(
        [FromQuery] string period = "daily", // daily, weekly, monthly
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.InvoiceItems
            .Include(ii => ii.Invoice)
            .Where(ii => !ii.Invoice!.IsDeleted && ii.Invoice.Type == "Sale");

        if (fromDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate <= toDate.Value);

        var items = await query.ToListAsync();

        object report = period.ToLower() switch
        {
            "daily" => items
                .GroupBy(ii => ii.Invoice!.InvoiceDate.Date)
                .Select(g => new
                {
                    date = g.Key.ToString("yyyy-MM-dd"),
                    displayDate = g.Key.ToString("dd/MM/yyyy"),
                    totalSales = g.Sum(ii => ii.Total),
                    totalCost = g.Sum(ii => ii.PurchasePrice * ii.Quantity),
                    totalProfit = g.Sum(ii => ii.Profit),
                    profitMargin = g.Sum(ii => ii.Total) > 0 ? (g.Sum(ii => ii.Profit) / g.Sum(ii => ii.Total)) * 100 : 0,
                    itemCount = g.Sum(ii => ii.Quantity)
                })
                .OrderBy(x => x.date)
                .ToList(),

            "weekly" => items
                .GroupBy(ii => new
                {
                    Year = ii.Invoice!.InvoiceDate.Year,
                    Week = System.Globalization.CultureInfo.CurrentCulture.Calendar.GetWeekOfYear(
                        ii.Invoice.InvoiceDate,
                        System.Globalization.CalendarWeekRule.FirstDay,
                        DayOfWeek.Saturday)
                })
                .Select(g => new
                {
                    date = $"{g.Key.Year}-W{g.Key.Week:D2}",
                    displayDate = $"أسبوع {g.Key.Week} - {g.Key.Year}",
                    totalSales = g.Sum(ii => ii.Total),
                    totalCost = g.Sum(ii => ii.PurchasePrice * ii.Quantity),
                    totalProfit = g.Sum(ii => ii.Profit),
                    profitMargin = g.Sum(ii => ii.Total) > 0 ? (g.Sum(ii => ii.Profit) / g.Sum(ii => ii.Total)) * 100 : 0,
                    itemCount = g.Sum(ii => ii.Quantity)
                })
                .OrderBy(x => x.date)
                .ToList(),

            "monthly" => items
                .GroupBy(ii => new
                {
                    Year = ii.Invoice!.InvoiceDate.Year,
                    Month = ii.Invoice.InvoiceDate.Month
                })
                .Select(g => new
                {
                    date = $"{g.Key.Year}-{g.Key.Month:D2}",
                    displayDate = $"{GetMonthName(g.Key.Month)} {g.Key.Year}",
                    totalSales = g.Sum(ii => ii.Total),
                    totalCost = g.Sum(ii => ii.PurchasePrice * ii.Quantity),
                    totalProfit = g.Sum(ii => ii.Profit),
                    profitMargin = g.Sum(ii => ii.Total) > 0 ? (g.Sum(ii => ii.Profit) / g.Sum(ii => ii.Total)) * 100 : 0,
                    itemCount = g.Sum(ii => ii.Quantity)
                })
                .OrderBy(x => x.date)
                .ToList(),

            _ => new List<object>()
        };

        return Ok(report);
    }

    [HttpGet("product-profit-details")]
    public async Task<IActionResult> GetProductProfitDetails(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.InvoiceItems
            .Include(ii => ii.Invoice)
            .Include(ii => ii.Product)
            .Where(ii => !ii.Invoice!.IsDeleted && ii.Invoice.Type == "Sale");

        if (fromDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate <= toDate.Value);

        var items = await query.ToListAsync();

        var report = items
            .GroupBy(ii => new { ii.ProductId, ii.ProductName })
            .Select(g => new
            {
                productId = g.Key.ProductId,
                productName = g.Key.ProductName,
                quantitySold = g.Sum(ii => ii.Quantity),
                totalSales = g.Sum(ii => ii.Total),
                totalCost = g.Sum(ii => ii.PurchasePrice * ii.Quantity),
                totalProfit = g.Sum(ii => ii.Profit),
                profitMargin = g.Sum(ii => ii.Total) > 0 ? (g.Sum(ii => ii.Profit) / g.Sum(ii => ii.Total)) * 100 : 0,
                avgSalePrice = g.Average(ii => ii.UnitPrice),
                avgPurchasePrice = g.Average(ii => ii.PurchasePrice),
                invoiceCount = g.Select(ii => ii.InvoiceId).Distinct().Count()
            })
            .OrderByDescending(x => x.totalProfit)
            .ToList();

        return Ok(report);
    }

    [HttpGet("dashboard-stats")]
    public async Task<IActionResult> GetDashboardStats()
    {
        var today = DateTime.Today;
        var thisWeekStart = today.AddDays(-(int)today.DayOfWeek + (int)DayOfWeek.Saturday);
        if (thisWeekStart > today) thisWeekStart = thisWeekStart.AddDays(-7);
        var thisMonthStart = new DateTime(today.Year, today.Month, 1);

        // Today's stats
        var todayItems = await _context.InvoiceItems
            .Include(ii => ii.Invoice)
            .Where(ii => !ii.Invoice!.IsDeleted &&
                         ii.Invoice.Type == "Sale" &&
                         ii.Invoice.InvoiceDate.Date == today)
            .ToListAsync();

        // This week's stats
        var weekItems = await _context.InvoiceItems
            .Include(ii => ii.Invoice)
            .Where(ii => !ii.Invoice!.IsDeleted &&
                         ii.Invoice.Type == "Sale" &&
                         ii.Invoice.InvoiceDate >= thisWeekStart)
            .ToListAsync();

        // This month's stats
        var monthItems = await _context.InvoiceItems
            .Include(ii => ii.Invoice)
            .Where(ii => !ii.Invoice!.IsDeleted &&
                         ii.Invoice.Type == "Sale" &&
                         ii.Invoice.InvoiceDate >= thisMonthStart)
            .ToListAsync();

        return Ok(new
        {
            today = new
            {
                sales = todayItems.Sum(ii => ii.Total),
                cost = todayItems.Sum(ii => ii.PurchasePrice * ii.Quantity),
                profit = todayItems.Sum(ii => ii.Profit),
                invoices = todayItems.Select(ii => ii.InvoiceId).Distinct().Count()
            },
            week = new
            {
                sales = weekItems.Sum(ii => ii.Total),
                cost = weekItems.Sum(ii => ii.PurchasePrice * ii.Quantity),
                profit = weekItems.Sum(ii => ii.Profit),
                invoices = weekItems.Select(ii => ii.InvoiceId).Distinct().Count()
            },
            month = new
            {
                sales = monthItems.Sum(ii => ii.Total),
                cost = monthItems.Sum(ii => ii.PurchasePrice * ii.Quantity),
                profit = monthItems.Sum(ii => ii.Profit),
                invoices = monthItems.Select(ii => ii.InvoiceId).Distinct().Count()
            }
        });
    }

    [HttpGet("top-products")]
    public async Task<IActionResult> GetTopProducts(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null,
        [FromQuery] int limit = 10)
    {
        var query = _context.InvoiceItems
            .Include(ii => ii.Invoice)
            .Include(ii => ii.Product)
            .Where(ii => !ii.Invoice!.IsDeleted && ii.Invoice.Type == "Sale");

        if (fromDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate <= toDate.Value);

        var items = await query.ToListAsync();

        var topByProfit = items
            .GroupBy(ii => new { ii.ProductId, ii.ProductName })
            .Select(g => new
            {
                productName = g.Key.ProductName,
                totalProfit = g.Sum(ii => ii.Profit),
                totalSales = g.Sum(ii => ii.Total),
                quantity = g.Sum(ii => ii.Quantity)
            })
            .OrderByDescending(x => x.totalProfit)
            .Take(limit)
            .ToList();

        var topBySales = items
            .GroupBy(ii => new { ii.ProductId, ii.ProductName })
            .Select(g => new
            {
                productName = g.Key.ProductName,
                totalSales = g.Sum(ii => ii.Total),
                totalProfit = g.Sum(ii => ii.Profit),
                quantity = g.Sum(ii => ii.Quantity)
            })
            .OrderByDescending(x => x.totalSales)
            .Take(limit)
            .ToList();

        var topByQuantity = items
            .GroupBy(ii => new { ii.ProductId, ii.ProductName })
            .Select(g => new
            {
                productName = g.Key.ProductName,
                quantity = g.Sum(ii => ii.Quantity),
                totalSales = g.Sum(ii => ii.Total),
                totalProfit = g.Sum(ii => ii.Profit)
            })
            .OrderByDescending(x => x.quantity)
            .Take(limit)
            .ToList();

        return Ok(new
        {
            topByProfit,
            topBySales,
            topByQuantity
        });
    }

    [HttpGet("sales-by-payment-type")]
    public async Task<IActionResult> GetSalesByPaymentType(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.InvoiceItems
            .Include(ii => ii.Invoice)
            .Where(ii => !ii.Invoice!.IsDeleted && ii.Invoice.Type == "Sale");

        if (fromDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate <= toDate.Value);

        var items = await query.ToListAsync();

        var report = items
            .GroupBy(ii => ii.Invoice!.PaymentType)
            .Select(g => new
            {
                paymentType = g.Key,
                paymentTypeName = GetPaymentTypeName(g.Key),
                invoiceCount = g.Select(ii => ii.InvoiceId).Distinct().Count(),
                totalSales = g.Sum(ii => ii.Total),
                totalProfit = g.Sum(ii => ii.Profit),
                totalPaid = g.GroupBy(ii => ii.InvoiceId).Sum(ig => ig.First().Invoice!.Paid),
                totalRemaining = g.GroupBy(ii => ii.InvoiceId).Sum(ig => ig.First().Invoice!.Remaining),
                totalDiscount = g.GroupBy(ii => ii.InvoiceId).Sum(ig => ig.First().Invoice!.Discount)
            })
            .OrderByDescending(x => x.totalSales)
            .ToList();

        return Ok(report);
    }

    [HttpGet("sales-by-category")]
    public async Task<IActionResult> GetSalesByCategory(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.InvoiceItems
            .Include(ii => ii.Invoice)
            .Include(ii => ii.Product)
            .Where(ii => !ii.Invoice!.IsDeleted && ii.Invoice.Type == "Sale");

        if (fromDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate <= toDate.Value);

        var items = await query.ToListAsync();

        var report = items
            .GroupBy(ii => ii.Product!.Category ?? "غير مصنف")
            .Select(g => new
            {
                category = g.Key,
                productCount = g.Select(ii => ii.ProductId).Distinct().Count(),
                quantitySold = g.Sum(ii => ii.Quantity),
                totalSales = g.Sum(ii => ii.Total),
                totalCost = g.Sum(ii => ii.PurchasePrice * ii.Quantity),
                totalProfit = g.Sum(ii => ii.Profit),
                profitMargin = g.Sum(ii => ii.Total) > 0 ? (g.Sum(ii => ii.Profit) / g.Sum(ii => ii.Total)) * 100 : 0
            })
            .OrderByDescending(x => x.totalProfit)
            .ToList();

        return Ok(report);
    }

    [HttpGet("customer-analysis")]
    public async Task<IActionResult> GetCustomerAnalysis(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.Invoices
            .Include(i => i.Customer)
            .Where(i => !i.IsDeleted && i.Type == "Sale" && i.CustomerId.HasValue);

        if (fromDate.HasValue)
            query = query.Where(i => i.InvoiceDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(i => i.InvoiceDate <= toDate.Value);

        var invoices = await query.ToListAsync();

        var report = invoices
            .GroupBy(i => new { i.CustomerId, CustomerName = i.Customer!.Name })
            .Select(g => new
            {
                customerId = g.Key.CustomerId,
                customerName = g.Key.CustomerName,
                invoiceCount = g.Count(),
                totalSales = g.Sum(i => i.Total),
                totalPaid = g.Sum(i => i.Paid),
                totalRemaining = g.Sum(i => i.Remaining),
                totalDiscount = g.Sum(i => i.Discount),
                avgInvoiceValue = g.Average(i => i.Total),
                lastPurchaseDate = g.Max(i => i.InvoiceDate)
            })
            .OrderByDescending(x => x.totalSales)
            .ToList();

        return Ok(report);
    }

    [HttpGet("low-stock-products")]
    public async Task<IActionResult> GetLowStockProducts([FromQuery] int threshold = 10)
    {
        var products = await _context.Products
            .Where(p => p.IsActive && p.Quantity <= threshold)
            .OrderBy(p => p.Quantity)
            .Select(p => new
            {
                p.Id,
                p.Code,
                p.Name,
                p.Category,
                p.Quantity,
                p.PurchasePrice,
                p.SalePrice,
                stockValue = p.Quantity * p.PurchasePrice,
                status = p.Quantity == 0 ? "نفذ" : p.Quantity <= 5 ? "حرج" : "قليل"
            })
            .ToListAsync();

        return Ok(products);
    }

    [HttpGet("daily-sales-chart")]
    public async Task<IActionResult> GetDailySalesChart(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var from = fromDate ?? DateTime.Today.AddDays(-30);
        var to = toDate ?? DateTime.Today;

        var query = _context.InvoiceItems
            .Include(ii => ii.Invoice)
            .Where(ii => !ii.Invoice!.IsDeleted &&
                         ii.Invoice.Type == "Sale" &&
                         ii.Invoice.InvoiceDate >= from &&
                         ii.Invoice.InvoiceDate <= to);

        var items = await query.ToListAsync();

        var chartData = items
            .GroupBy(ii => ii.Invoice!.InvoiceDate.Date)
            .Select(g => new
            {
                date = g.Key.ToString("yyyy-MM-dd"),
                sales = g.Sum(ii => ii.Total),
                profit = g.Sum(ii => ii.Profit),
                cost = g.Sum(ii => ii.PurchasePrice * ii.Quantity),
                invoices = g.Select(ii => ii.InvoiceId).Distinct().Count()
            })
            .OrderBy(x => x.date)
            .ToList();

        return Ok(chartData);
    }

    [HttpGet("comprehensive-summary")]
    public async Task<IActionResult> GetComprehensiveSummary(
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.InvoiceItems
            .Include(ii => ii.Invoice)
            .Where(ii => !ii.Invoice!.IsDeleted && ii.Invoice.Type == "Sale");

        if (fromDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(ii => ii.Invoice!.InvoiceDate <= toDate.Value);

        var items = await query.ToListAsync();
        var invoices = items.Select(ii => ii.Invoice).Distinct().ToList();

        var totalProducts = await _context.Products.CountAsync(p => p.IsActive);
        var totalCustomers = await _context.Customers.CountAsync(c => c.IsActive);
        var lowStockCount = await _context.Products.CountAsync(p => p.IsActive && p.Quantity <= 10);

        return Ok(new
        {
            // المبيعات
            totalSales = items.Sum(ii => ii.Total),
            totalCost = items.Sum(ii => ii.PurchasePrice * ii.Quantity),
            totalProfit = items.Sum(ii => ii.Profit),
            profitMargin = items.Sum(ii => ii.Total) > 0 ? (items.Sum(ii => ii.Profit) / items.Sum(ii => ii.Total)) * 100 : 0,

            // الفواتير
            totalInvoices = invoices.Count,
            totalPaid = invoices.Sum(i => i.Paid),
            totalRemaining = invoices.Sum(i => i.Remaining),
            totalDiscount = invoices.Sum(i => i.Discount),

            // المنتجات
            totalItemsSold = items.Sum(ii => ii.Quantity),
            uniqueProductsSold = items.Select(ii => ii.ProductId).Distinct().Count(),
            avgItemsPerInvoice = invoices.Count > 0 ? items.Count / (decimal)invoices.Count : 0,

            // العملاء
            uniqueCustomers = invoices.Where(i => i.CustomerId.HasValue).Select(i => i.CustomerId).Distinct().Count(),
            avgSalePerCustomer = invoices.Where(i => i.CustomerId.HasValue).Select(i => i.CustomerId).Distinct().Count() > 0
                ? items.Sum(ii => ii.Total) / invoices.Where(i => i.CustomerId.HasValue).Select(i => i.CustomerId).Distinct().Count()
                : 0,

            // المخزون
            totalProducts,
            totalCustomers,
            lowStockCount,

            // متوسطات
            avgInvoiceValue = invoices.Count > 0 ? invoices.Average(i => i.Total) : 0,
            avgProfit = invoices.Count > 0 ? items.Sum(ii => ii.Profit) / invoices.Count : 0
        });
    }


	    [HttpGet("work-shifts")]
	    public async Task<IActionResult> GetWorkShifts(
	        [FromQuery] DateTime? fromDate = null,
	        [FromQuery] DateTime? toDate = null,
	        [FromQuery] int? userId = null)
	    {
	        var query = _context.WorkShifts
	            .Include(s => s.User)
	            .AsQueryable();

	        if (fromDate.HasValue)
	        {
	            var from = fromDate.Value.Date;
	            query = query.Where(s => s.Date >= from);
	        }

	        if (toDate.HasValue)
	        {
	            var to = toDate.Value.Date;
	            query = query.Where(s => s.Date <= to);
	        }

	        if (userId.HasValue)
	        {
	            query = query.Where(s => s.UserId == userId.Value);
	        }

	        var shifts = await query
	            .OrderByDescending(s => s.Date)
	            .ThenBy(s => s.StartTime)
	            .ToListAsync();

	        var result = shifts.Select(s => new
	        {
	            id = s.Id,
	            userId = s.UserId,
	            username = s.User != null ? s.User.Username : string.Empty,
	            fullName = s.User != null ? s.User.FullName : string.Empty,
	            date = s.Date,
	            startTime = s.StartTime,
	            endTime = s.EndTime
	        });

	        return Ok(result);
	    }

    private static string GetMonthName(int month)
    {
        return month switch
        {
            1 => "يناير",
            2 => "فبراير",
            3 => "مارس",
            4 => "أبريل",
            5 => "مايو",
            6 => "يونيو",
            7 => "يوليو",
            8 => "أغسطس",
            9 => "سبتمبر",
            10 => "أكتوبر",
            11 => "نوفمبر",
            12 => "ديسمبر",
            _ => ""
        };
    }

    private static string GetPaymentTypeName(string paymentType)
    {
        return paymentType switch
        {
            "Cash" => "نقدي",
            "Credit" => "آجل",
            "Wholesale" => "جملة",
            "Installment" => "قسط",
            _ => paymentType
        };
    }
}

