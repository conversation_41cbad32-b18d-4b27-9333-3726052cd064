# نص PowerShell لإنشاء نسخة احتياطية كاملة من نظام أريدوو
# Aredoo POS System - Complete Backup Script

param(
    [string]$BackupPath = ".\Backups",
    [switch]$OpenFolder
)

Write-Host "=== نظام النسخ الاحتياطي لأريدوو ===" -ForegroundColor Green
Write-Host "Aredoo POS System - Backup Tool" -ForegroundColor Green
Write-Host ""

# إنشاء مجلد النسخ الاحتياطية
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupDir = Join-Path $BackupPath "aredoo_backup_$timestamp"

try {
    Write-Host "Creating backup directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

    # Copy database
    Write-Host "Copying database..." -ForegroundColor Yellow
    if (Test-Path ".\Data\aredoo.db") {
        Copy-Item ".\Data\aredoo.db" "$backupDir\aredoo.db"
        Write-Host "Database copied successfully" -ForegroundColor Green
    } else {
        Write-Host "Database not found" -ForegroundColor Red
    }

    # Copy images
    Write-Host "Copying images..." -ForegroundColor Yellow
    if (Test-Path ".\wwwroot\images") {
        Copy-Item ".\wwwroot\images" "$backupDir\images" -Recurse
        $imageCount = (Get-ChildItem "$backupDir\images" -Recurse -File).Count
        Write-Host "Copied $imageCount images" -ForegroundColor Green
    } else {
        Write-Host "Images folder not found" -ForegroundColor Yellow
    }

    # Copy settings files
    Write-Host "Copying settings..." -ForegroundColor Yellow
    $settingsFiles = @("appsettings.json", "appsettings.Development.json")
    foreach ($file in $settingsFiles) {
        if (Test-Path $file) {
            Copy-Item $file "$backupDir\$file"
            Write-Host "Copied $file" -ForegroundColor Green
        }
    }
    
    # Create backup info file
    Write-Host "Creating backup info..." -ForegroundColor Yellow
    $backupInfo = @{
        CreatedAt = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Version = "1.0"
        ComputerName = $env:COMPUTERNAME
        UserName = $env:USERNAME
        BackupType = "Complete"
        Description = "Complete backup of Aredoo POS System"
    }
    
    $backupInfo | ConvertTo-Json -Depth 3 | Out-File "$backupDir\backup_info.json" -Encoding UTF8
    
    # Create ZIP file
    Write-Host "Creating ZIP file..." -ForegroundColor Yellow
    $zipPath = "$BackupPath\aredoo_backup_$timestamp.zip"
    Compress-Archive -Path "$backupDir\*" -DestinationPath $zipPath -Force
    
    # Remove temporary folder
    Remove-Item $backupDir -Recurse -Force

    # Calculate file size
    $fileSize = (Get-Item $zipPath).Length
    $fileSizeMB = [math]::Round($fileSize / 1MB, 2)

    Write-Host ""
    Write-Host "=== Backup Created Successfully ===" -ForegroundColor Green
    Write-Host "Path: $zipPath" -ForegroundColor Cyan
    Write-Host "Size: $fileSizeMB MB" -ForegroundColor Cyan
    Write-Host "Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan
    Write-Host ""

    if ($OpenFolder) {
        Write-Host "Opening backup folder..." -ForegroundColor Yellow
        Invoke-Item (Split-Path $zipPath -Parent)
    }

    Write-Host "Completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Host ""
    Write-Host "Error occurred during backup creation:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    exit 1
}

# Wait for key press to close
Write-Host ""
Write-Host "Press any key to close..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
