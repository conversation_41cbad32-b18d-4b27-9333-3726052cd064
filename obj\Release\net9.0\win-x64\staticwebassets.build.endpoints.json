{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/style.css", "AssetFile": "css/style.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000322788896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3097"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic=\""}, {"Name": "ETag", "Value": "W/\"j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc="}]}, {"Route": "css/style.css", "AssetFile": "css/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "13011"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:28:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc="}]}, {"Route": "css/style.css.gz", "AssetFile": "css/style.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3097"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic="}]}, {"Route": "css/style.jv2slhbvk0.css", "AssetFile": "css/style.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000322788896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3097"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic=\""}, {"Name": "ETag", "Value": "W/\"j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jv2slhbvk0"}, {"Name": "integrity", "Value": "sha256-j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc="}, {"Name": "label", "Value": "css/style.css"}]}, {"Route": "css/style.jv2slhbvk0.css", "AssetFile": "css/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "13011"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:28:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jv2slhbvk0"}, {"Name": "integrity", "Value": "sha256-j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc="}, {"Name": "label", "Value": "css/style.css"}]}, {"Route": "css/style.jv2slhbvk0.css.gz", "AssetFile": "css/style.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3097"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jv2slhbvk0"}, {"Name": "integrity", "Value": "sha256-0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic="}, {"Name": "label", "Value": "css/style.css.gz"}]}, {"Route": "index.2p28n1lgul.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000814995925"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1226"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"cVDSNuyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU=\""}, {"Name": "ETag", "Value": "W/\"pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2p28n1lgul"}, {"Name": "integrity", "Value": "sha256-pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.2p28n1lgul.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5415"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 16:02:26 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2p28n1lgul"}, {"Name": "integrity", "Value": "sha256-pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.2p28n1lgul.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1226"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"cVDSNuyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2p28n1lgul"}, {"Name": "integrity", "Value": "sha256-c<PERSON><PERSON>uyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000814995925"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1226"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"cVDSNuyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU=\""}, {"Name": "ETag", "Value": "W/\"pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5415"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 16:02:26 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1226"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"cVDSNuyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c<PERSON><PERSON>uyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU="}]}, {"Route": "js/app.ds7d0gz88x.js", "AssetFile": "js/app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000511247444"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1955"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw=\""}, {"Name": "ETag", "Value": "W/\"rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ds7d0gz88x"}, {"Name": "integrity", "Value": "sha256-rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.ds7d0gz88x.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5664"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 12:53:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ds7d0gz88x"}, {"Name": "integrity", "Value": "sha256-rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.ds7d0gz88x.js.gz", "AssetFile": "js/app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1955"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ds7d0gz88x"}, {"Name": "integrity", "Value": "sha256-YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw="}, {"Name": "label", "Value": "js/app.js.gz"}]}, {"Route": "js/app.js", "AssetFile": "js/app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000511247444"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1955"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw=\""}, {"Name": "ETag", "Value": "W/\"rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY="}]}, {"Route": "js/app.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5664"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 12:53:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY="}]}, {"Route": "js/app.js.gz", "AssetFile": "js/app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1955"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw="}]}, {"Route": "js/auth.iyy8yegl4k.js", "AssetFile": "js/auth.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000880281690"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "ETag", "Value": "W/\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyy8yegl4k"}, {"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}, {"Name": "label", "Value": "js/auth.js"}]}, {"Route": "js/auth.iyy8yegl4k.js", "AssetFile": "js/auth.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 15:25:05 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyy8yegl4k"}, {"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}, {"Name": "label", "Value": "js/auth.js"}]}, {"Route": "js/auth.iyy8yegl4k.js.gz", "AssetFile": "js/auth.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyy8yegl4k"}, {"Name": "integrity", "Value": "sha256-X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc="}, {"Name": "label", "Value": "js/auth.js.gz"}]}, {"Route": "js/auth.js", "AssetFile": "js/auth.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000880281690"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "ETag", "Value": "W/\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}]}, {"Route": "js/auth.js", "AssetFile": "js/auth.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 15:25:05 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}]}, {"Route": "js/auth.js.gz", "AssetFile": "js/auth.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc="}]}, {"Route": "js/categories.js", "AssetFile": "js/categories.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000418235048"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "ETag", "Value": "W/\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}]}, {"Route": "js/categories.js", "AssetFile": "js/categories.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10343"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:53:33 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}]}, {"Route": "js/categories.js.gz", "AssetFile": "js/categories.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM="}]}, {"Route": "js/categories.q4k5ude2ax.js", "AssetFile": "js/categories.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000418235048"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "ETag", "Value": "W/\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q4k5ude2ax"}, {"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}, {"Name": "label", "Value": "js/categories.js"}]}, {"Route": "js/categories.q4k5ude2ax.js", "AssetFile": "js/categories.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10343"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:53:33 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q4k5ude2ax"}, {"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}, {"Name": "label", "Value": "js/categories.js"}]}, {"Route": "js/categories.q4k5ude2ax.js.gz", "AssetFile": "js/categories.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q4k5ude2ax"}, {"Name": "integrity", "Value": "sha256-N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM="}, {"Name": "label", "Value": "js/categories.js.gz"}]}, {"Route": "js/customers.hy3h9tabsi.js", "AssetFile": "js/customers.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000372439479"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "ETag", "Value": "W/\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy3h9tabsi"}, {"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}, {"Name": "label", "Value": "js/customers.js"}]}, {"Route": "js/customers.hy3h9tabsi.js", "AssetFile": "js/customers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14539"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:52:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy3h9tabsi"}, {"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}, {"Name": "label", "Value": "js/customers.js"}]}, {"Route": "js/customers.hy3h9tabsi.js.gz", "AssetFile": "js/customers.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy3h9tabsi"}, {"Name": "integrity", "Value": "sha256-sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4="}, {"Name": "label", "Value": "js/customers.js.gz"}]}, {"Route": "js/customers.js", "AssetFile": "js/customers.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000372439479"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "ETag", "Value": "W/\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}]}, {"Route": "js/customers.js", "AssetFile": "js/customers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14539"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:52:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}]}, {"Route": "js/customers.js.gz", "AssetFile": "js/customers.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4="}]}, {"Route": "js/invoices.7ene3dnx54.js", "AssetFile": "js/invoices.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000203417413"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "ETag", "Value": "W/\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ene3dnx54"}, {"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}, {"Name": "label", "Value": "js/invoices.js"}]}, {"Route": "js/invoices.7ene3dnx54.js", "AssetFile": "js/invoices.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "30213"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:27:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ene3dnx54"}, {"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}, {"Name": "label", "Value": "js/invoices.js"}]}, {"Route": "js/invoices.7ene3dnx54.js.gz", "AssetFile": "js/invoices.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ene3dnx54"}, {"Name": "integrity", "Value": "sha256-oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI="}, {"Name": "label", "Value": "js/invoices.js.gz"}]}, {"Route": "js/invoices.js", "AssetFile": "js/invoices.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000203417413"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "ETag", "Value": "W/\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}]}, {"Route": "js/invoices.js", "AssetFile": "js/invoices.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "30213"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:27:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}]}, {"Route": "js/invoices.js.gz", "AssetFile": "js/invoices.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI="}]}, {"Route": "js/pos.51r6od7olg.js", "AssetFile": "js/pos.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000145011601"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6895"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY=\""}, {"Name": "ETag", "Value": "W/\"p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "51r6od7olg"}, {"Name": "integrity", "Value": "sha256-p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME="}, {"Name": "label", "Value": "js/pos.js"}]}, {"Route": "js/pos.51r6od7olg.js", "AssetFile": "js/pos.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "31619"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 19:02:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "51r6od7olg"}, {"Name": "integrity", "Value": "sha256-p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME="}, {"Name": "label", "Value": "js/pos.js"}]}, {"Route": "js/pos.51r6od7olg.js.gz", "AssetFile": "js/pos.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6895"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "51r6od7olg"}, {"Name": "integrity", "Value": "sha256-uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY="}, {"Name": "label", "Value": "js/pos.js.gz"}]}, {"Route": "js/pos.js", "AssetFile": "js/pos.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000145011601"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6895"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY=\""}, {"Name": "ETag", "Value": "W/\"p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME="}]}, {"Route": "js/pos.js", "AssetFile": "js/pos.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "31619"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 19:02:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME="}]}, {"Route": "js/pos.js.gz", "AssetFile": "js/pos.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6895"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY="}]}, {"Route": "js/products.9n0jr0b026.js", "AssetFile": "js/products.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000120700060"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "ETag", "Value": "W/\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:35 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0jr0b026"}, {"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}, {"Name": "label", "Value": "js/products.js"}]}, {"Route": "js/products.9n0jr0b026.js", "AssetFile": "js/products.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "44477"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0jr0b026"}, {"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}, {"Name": "label", "Value": "js/products.js"}]}, {"Route": "js/products.9n0jr0b026.js.gz", "AssetFile": "js/products.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:35 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0jr0b026"}, {"Name": "integrity", "Value": "sha256-cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU="}, {"Name": "label", "Value": "js/products.js.gz"}]}, {"Route": "js/products.js", "AssetFile": "js/products.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000120700060"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "ETag", "Value": "W/\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:35 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}]}, {"Route": "js/products.js", "AssetFile": "js/products.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44477"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}]}, {"Route": "js/products.js.gz", "AssetFile": "js/products.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:35 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU="}]}, {"Route": "js/reports.99d2u4ky5k.js", "AssetFile": "js/reports.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000217770035"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4591"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M=\""}, {"Name": "ETag", "Value": "W/\"nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "99d2u4ky5k"}, {"Name": "integrity", "Value": "sha256-nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI="}, {"Name": "label", "Value": "js/reports.js"}]}, {"Route": "js/reports.99d2u4ky5k.js", "AssetFile": "js/reports.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "38742"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:33:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "99d2u4ky5k"}, {"Name": "integrity", "Value": "sha256-nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI="}, {"Name": "label", "Value": "js/reports.js"}]}, {"Route": "js/reports.99d2u4ky5k.js.gz", "AssetFile": "js/reports.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4591"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "99d2u4ky5k"}, {"Name": "integrity", "Value": "sha256-sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M="}, {"Name": "label", "Value": "js/reports.js.gz"}]}, {"Route": "js/reports.js", "AssetFile": "js/reports.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000217770035"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4591"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M=\""}, {"Name": "ETag", "Value": "W/\"nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI="}]}, {"Route": "js/reports.js", "AssetFile": "js/reports.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "38742"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:33:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI="}]}, {"Route": "js/reports.js.gz", "AssetFile": "js/reports.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4591"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M="}]}, {"Route": "js/settings.js", "AssetFile": "js/settings.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083745080"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11940"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M=\""}, {"Name": "ETag", "Value": "W/\"kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc="}]}, {"Route": "js/settings.js", "AssetFile": "js/settings.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "81891"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:14 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc="}]}, {"Route": "js/settings.js.gz", "AssetFile": "js/settings.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11940"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M="}]}, {"Route": "js/settings.x4y7n64zvw.js", "AssetFile": "js/settings.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083745080"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11940"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M=\""}, {"Name": "ETag", "Value": "W/\"kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x4y7n64zvw"}, {"Name": "integrity", "Value": "sha256-kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc="}, {"Name": "label", "Value": "js/settings.js"}]}, {"Route": "js/settings.x4y7n64zvw.js", "AssetFile": "js/settings.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "81891"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:14 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x4y7n64zvw"}, {"Name": "integrity", "Value": "sha256-kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc="}, {"Name": "label", "Value": "js/settings.js"}]}, {"Route": "js/settings.x4y7n64zvw.js.gz", "AssetFile": "js/settings.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11940"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:52 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x4y7n64zvw"}, {"Name": "integrity", "Value": "sha256-swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M="}, {"Name": "label", "Value": "js/settings.js.gz"}]}, {"Route": "login.html", "AssetFile": "login.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000487329435"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "ETag", "Value": "W/\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}]}, {"Route": "login.html", "AssetFile": "login.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6732"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:38:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}]}, {"Route": "login.html.gz", "AssetFile": "login.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ="}]}, {"Route": "login.nqonojhrly.html", "AssetFile": "login.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000487329435"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "ETag", "Value": "W/\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}, {"Name": "label", "Value": "login.html"}]}, {"Route": "login.nqonojhrly.html", "AssetFile": "login.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6732"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:38:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}, {"Name": "label", "Value": "login.html"}]}, {"Route": "login.nqonojhrly.html.gz", "AssetFile": "login.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ="}, {"Name": "label", "Value": "login.html.gz"}]}, {"Route": "print-invoice.2cv1skr50a.html", "AssetFile": "print-invoice.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000250752257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "ETag", "Value": "W/\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cv1skr50a"}, {"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}, {"Name": "label", "Value": "print-invoice.html"}]}, {"Route": "print-invoice.2cv1skr50a.html", "AssetFile": "print-invoice.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17980"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 19:09:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cv1skr50a"}, {"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}, {"Name": "label", "Value": "print-invoice.html"}]}, {"Route": "print-invoice.2cv1skr50a.html.gz", "AssetFile": "print-invoice.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cv1skr50a"}, {"Name": "integrity", "Value": "sha256-bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE="}, {"Name": "label", "Value": "print-invoice.html.gz"}]}, {"Route": "print-invoice.html", "AssetFile": "print-invoice.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000250752257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "ETag", "Value": "W/\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}]}, {"Route": "print-invoice.html", "AssetFile": "print-invoice.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17980"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 19:09:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}]}, {"Route": "print-invoice.html.gz", "AssetFile": "print-invoice.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE="}]}]}