# أريدوو - Aredoo POS System

نظام إدارة المبيعات والمخزون - أوفلاين بالكامل

## 🌟 المميزات

- ✅ **أوفلاين بالكامل** - يعمل بدون إنترنت
- ✅ **سهل الاستخدام** - واجهة عربية بسيطة وسريعة
- ✅ **نقطة بيع متكاملة** - بيع نقدي، آجل، جملة، قسط
- ✅ **إدارة المخزون** - تتبع المنتجات والكميات
- ✅ **إدارة العملاء والموردين**
- ✅ **تقارير شاملة** - مبيعات، أرباح، مخزون
- ✅ **نسخ احتياطي تلقائي**
- ✅ **متعدد المستخدمين** - صلاحيات مختلفة

## 📋 المتطلبات

- Windows 10 أو أحدث
- .NET 9.0 SDK أو أحدث

## 🚀 التشغيل

### الطريقة الأولى (مباشرة):
1. انقر نقراً مزدوجاً على `StartAredoo.bat`
2. سيفتح المتصفح تلقائياً على `http://localhost:5000`

### الطريقة الثانية (من Terminal):
```bash
dotnet run
```

## 🔐 تسجيل الدخول

**المستخدم الافتراضي:**
- اسم المستخدم: `admin`
- كلمة المرور: `1234`

## 📁 هيكل المشروع

```
Aredoo/
├── Controllers/          # API Controllers
├── Models/              # نماذج البيانات
├── Data/                # قاعدة البيانات
├── wwwroot/             # الواجهة الأمامية
│   ├── css/            # ملفات التنسيق
│   ├── js/             # ملفات JavaScript
│   └── index.html      # الصفحة الرئيسية
├── data/                # قاعدة البيانات SQLite
│   └── aredoo.db       # ملف قاعدة البيانات
└── StartAredoo.bat     # ملف التشغيل السريع
```

## 📊 الوحدات الرئيسية

### 1. نقطة البيع (POS)
- بيع سريع وسهل
- بحث عن المنتجات
- أنواع دفع متعددة (نقدي، آجل، جملة، قسط)
- حساب تلقائي للإجمالي والخصم والضريبة

### 2. إدارة المنتجات
- إضافة وتعديل المنتجات
- تتبع المخزون
- تنبيهات الكميات المنخفضة
- الباركود والأكواد

### 3. إدارة العملاء
- قائمة العملاء
- كشف الحساب
- الأرصدة والمتأخرات

### 4. الفواتير
- عرض جميع الفواتير
- فلترة حسب النوع والتاريخ
- طباعة الفواتير

### 5. التقارير
- تقرير المبيعات
- تقرير الأرباح
- المبيعات حسب المنتج
- المبيعات حسب العميل
- تقرير المخزون

### 6. الإعدادات
- معلومات الشركة
- إعدادات العملة
- النسخ الاحتياطي
- تصفير النظام

## 💾 النسخ الاحتياطي

يتم حفظ النسخ الاحتياطية في:
```
data/backups/YYYY-MM-DD/
```

## 🔧 التطوير

### إضافة مستخدم جديد:
```sql
INSERT INTO Users (Username, PasswordHash, FullName, Role, IsActive)
VALUES ('username', 'hashed_password', 'الاسم الكامل', 'Cashier', 1);
```

### تغيير المنفذ:
في ملف `Program.cs`:
```csharp
builder.WebHost.UseUrls("http://localhost:PORT");
```

## 📝 ملاحظات

- قاعدة البيانات: SQLite (محلية بالكامل)
- الواجهة: Bootstrap 5 RTL
- الخطوط: Cairo (عربي)
- لا يحتاج اتصال بالإنترنت

## 🆘 الدعم

للمشاكل والاستفسارات، يرجى فتح Issue في GitHub

## 📄 الترخيص

MIT License - مفتوح المصدر

---

**تم التطوير بواسطة Augment AI** 🤖

