namespace Aredoo.Server.Models;

public class Installment
{
    public int Id { get; set; }
    public int InvoiceId { get; set; }
    public Invoice? Invoice { get; set; }
    public int InstallmentNumber { get; set; }
    public decimal Amount { get; set; }
    public DateTime DueDate { get; set; }
    public DateTime? PaidDate { get; set; }
    public decimal PaidAmount { get; set; }
    public bool IsPaid { get; set; } = false;
    public string? Notes { get; set; }
}

