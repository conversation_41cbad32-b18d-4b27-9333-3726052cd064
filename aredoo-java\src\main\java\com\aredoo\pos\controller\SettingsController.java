package com.aredoo.pos.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/settings")
@CrossOrigin(origins = "*")
public class SettingsController {

    private static final Map<String, Object> settings = new HashMap<>();
    
    static {
        // Initialize default settings
        settings.put("companyName", "أريدوو");
        settings.put("companyNameEn", "Aredoo");
        settings.put("address", "العراق - بغداد");
        settings.put("phone", "+964 XXX XXX XXXX");
        settings.put("email", "<EMAIL>");
        settings.put("website", "www.aredoo.com");
        settings.put("currency", "IQD");
        settings.put("taxRate", 0.0);
        settings.put("receiptFooter", "شكراً لتعاملكم معنا");
        settings.put("lowStockAlert", true);
        settings.put("autoBackup", true);
        settings.put("printAfterSale", true);
        settings.put("showBarcode", true);
        settings.put("language", "ar");
        settings.put("theme", "light");
    }

    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllSettings() {
        return ResponseEntity.ok(settings);
    }

    @GetMapping("/{key}")
    public ResponseEntity<Object> getSetting(@PathVariable String key) {
        Object value = settings.get(key);
        if (value != null) {
            return ResponseEntity.ok(value);
        }
        return ResponseEntity.notFound().build();
    }

    @PostMapping
    public ResponseEntity<Map<String, Object>> updateSettings(@RequestBody Map<String, Object> newSettings) {
        try {
            settings.putAll(newSettings);
            return ResponseEntity.ok(settings);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{key}")
    public ResponseEntity<Object> updateSetting(@PathVariable String key, @RequestBody Object value) {
        try {
            settings.put(key, value);
            return ResponseEntity.ok(value);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/{key}")
    public ResponseEntity<Void> deleteSetting(@PathVariable String key) {
        if (settings.containsKey(key)) {
            settings.remove(key);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }

    @PostMapping("/reset")
    public ResponseEntity<Map<String, Object>> resetToDefaults() {
        settings.clear();
        
        // Restore default settings
        settings.put("companyName", "أريدوو");
        settings.put("companyNameEn", "Aredoo");
        settings.put("address", "العراق - بغداد");
        settings.put("phone", "+964 XXX XXX XXXX");
        settings.put("email", "<EMAIL>");
        settings.put("website", "www.aredoo.com");
        settings.put("currency", "IQD");
        settings.put("taxRate", 0.0);
        settings.put("receiptFooter", "شكراً لتعاملكم معنا");
        settings.put("lowStockAlert", true);
        settings.put("autoBackup", true);
        settings.put("printAfterSale", true);
        settings.put("showBarcode", true);
        settings.put("language", "ar");
        settings.put("theme", "light");
        
        return ResponseEntity.ok(settings);
    }
}
