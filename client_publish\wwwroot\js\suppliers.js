﻿// Suppliers Management

async function loadSuppliersPage() {
    const page = document.getElementById('suppliersPage');
    
    page.innerHTML = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-truck"></i> الموردين</h5>
                <button class="btn btn-primary" onclick="showAddSupplierModal()">
                    <i class="bi bi-plus-circle"></i> إضافة مورد
                </button>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <input type="text" class="form-control" id="supplierSearch" placeholder="بحث بالاسم أو الرمز أو الهاتف..." onkeyup="loadSuppliersList()">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="supplierStatus" onchange="loadSuppliersList()">
                            <option value="">جميع الموردين</option>
                            <option value="true" selected>نشط</option>
                            <option value="false">غير نشط</option>
                        </select>
                    </div>
                </div>

                <div id="suppliersTableContainer"></div>
            </div>
        </div>

        <!-- Supplier Modal -->
        <div class="modal fade" id="supplierModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="supplierModalTitle">إضافة مورد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="supplierForm">
                            <input type="hidden" id="supplierId">
                            
                            <div class="mb-3">
                                <label class="form-label">الاسم *</label>
                                <input type="text" class="form-control" id="supplierName" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">رمز المورد</label>
                                <input type="text" class="form-control" id="supplierCode" placeholder="سيتم إنشاؤه تلقائياً">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الهاتف</label>
                                <input type="tel" class="form-control" id="supplierPhone">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea class="form-control" id="supplierAddress" rows="2"></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="supplierEmail">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="supplierNotes" rows="2"></textarea>
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="supplierIsActive" checked>
                                <label class="form-check-label" for="supplierIsActive">نشط</label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveSupplier()">حفظ</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    await loadSuppliersList();
}

async function loadSuppliersList() {
    try {
        const search = document.getElementById('supplierSearch')?.value || '';
        const status = document.getElementById('supplierStatus')?.value || '';

        let url = `/api/suppliers`;
        if (status) url += `?isActive=${status}`;

        const response = await fetch(url);
        const suppliers = await response.json();

        let filteredSuppliers = suppliers;
        if (search) {
            const searchLower = search.toLowerCase();
            filteredSuppliers = suppliers.filter(s => 
                s.name.toLowerCase().includes(searchLower) ||
                s.code.toLowerCase().includes(searchLower) ||
                (s.phone && s.phone.includes(search))
            );
        }

        const container = document.getElementById('suppliersTableContainer');
        if (filteredSuppliers.length === 0) {
            container.innerHTML = '<div class="alert alert-info">لا توجد موردين</div>';
            return;
        }

        let html = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الرمز</th>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>الرصيد</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        filteredSuppliers.forEach(supplier => {
            const balanceClass = supplier.balance > 0 ? 'text-danger' : supplier.balance < 0 ? 'text-success' : '';
            const statusBadge = supplier.isActive 
                ? '<span class="badge bg-success">نشط</span>' 
                : '<span class="badge bg-secondary">غير نشط</span>';

            html += `
                <tr>
                    <td>${supplier.code}</td>
                    <td>${supplier.name}</td>
                    <td>${supplier.phone || '-'}</td>
                    <td class="${balanceClass}">${supplier.balance.toLocaleString('en-IQ')} د.ع</td>
                    <td>${statusBadge}</td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="editSupplier(${supplier.id})">
                            <i class="bi bi-pencil"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;
    } catch (error) {
        console.error('Error loading suppliers:', error);
        showToast('خطأ في تحميل الموردين', 'error');
    }
}

function showAddSupplierModal() {
    document.getElementById('supplierModalTitle').textContent = 'إضافة مورد';
    document.getElementById('supplierForm').reset();
    document.getElementById('supplierId').value = '';
    document.getElementById('supplierIsActive').checked = true;
    
    const modal = new bootstrap.Modal(document.getElementById('supplierModal'));
    modal.show();
}

async function editSupplier(id) {
    try {
        const response = await fetch(`/api/suppliers/${id}`);
        const supplier = await response.json();

        document.getElementById('supplierModalTitle').textContent = 'تعديل مورد';
        document.getElementById('supplierId').value = supplier.id;
        document.getElementById('supplierName').value = supplier.name;
        document.getElementById('supplierCode').value = supplier.code;
        document.getElementById('supplierPhone').value = supplier.phone || '';
        document.getElementById('supplierAddress').value = supplier.address || '';
        document.getElementById('supplierEmail').value = supplier.email || '';
        document.getElementById('supplierNotes').value = supplier.notes || '';
        document.getElementById('supplierIsActive').checked = supplier.isActive;

        const modal = new bootstrap.Modal(document.getElementById('supplierModal'));
        modal.show();
    } catch (error) {
        console.error('Error loading supplier:', error);
        showToast('خطأ في تحميل بيانات المورد', 'error');
    }
}

async function saveSupplier() {
    const id = document.getElementById('supplierId').value;
    const supplier = {
        name: document.getElementById('supplierName').value,
        code: document.getElementById('supplierCode').value,
        phone: document.getElementById('supplierPhone').value,
        address: document.getElementById('supplierAddress').value,
        email: document.getElementById('supplierEmail').value,
        notes: document.getElementById('supplierNotes').value,
        isActive: document.getElementById('supplierIsActive').checked
    };

    if (!supplier.name) {
        showToast('الرجاء إدخال اسم المورد', 'warning');
        return;
    }

    try {
        const url = id ? `/api/suppliers/${id}` : `/api/suppliers`;
        const method = id ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(supplier)
        });

        if (response.ok) {
            showToast(id ? 'تم تحديث المورد بنجاح' : 'تم إضافة المورد بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('supplierModal')).hide();
            await loadSuppliersList();
        } else {
            showToast('خطأ في حفظ المورد', 'error');
        }
    } catch (error) {
        console.error('Error saving supplier:', error);
        showToast('خطأ في حفظ المورد', 'error');
    }
}
