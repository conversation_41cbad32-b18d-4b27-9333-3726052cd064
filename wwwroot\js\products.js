async function loadProductsPage() {
    const page = document.getElementById('productsPage');
    page.innerHTML = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-box-seam"></i> إدارة المنتجات</h5>
                <button class="btn btn-primary" onclick="showProductModal()" data-permission-module="Products" data-permission-action="add">
                    <i class="bi bi-plus-circle"></i> منتج جديد
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>صورة</th>
                                <th>الكود</th>
                                <th>الاسم</th>
                                <th>الفئة</th>
                                <th>سعر الشراء</th>
                                <th>سعر البيع</th>
                                <th>الكمية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="productsTable"></tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Modal إضافة/تعديل منتج -->
        <div class="modal fade" id="productModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="productModalTitle">
                            <i class="bi bi-box-seam"></i> منتج جديد
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="productForm">
                            <input type="hidden" id="productId">

                            <div class="row">
                                <!-- صورة المنتج -->
                                <div class="col-md-4 text-center mb-3">
                                    <label class="form-label">صورة المنتج (اختياري)</label>
                                    <div class="product-image-upload">
                                        <img id="productImagePreview" src="" alt="صورة المنتج"
                                             class="product-image-preview d-none">
                                        <div id="productImagePlaceholder" class="product-image-placeholder">
                                            <i class="bi bi-image" style="font-size: 3rem; color: #ccc;"></i>
                                            <p class="text-muted mt-2">اضغط لاختيار صورة</p>
                                        </div>
                                        <input type="file" id="productImage" accept="image/*"
                                               class="d-none" onchange="previewProductImage(event)">
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-2"
                                                onclick="document.getElementById('productImage').click()">
                                            <i class="bi bi-upload"></i> اختر صورة
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger mt-2"
                                                onclick="removeProductImage()" id="removeImageBtn" style="display:none;">
                                            <i class="bi bi-trash"></i> حذف
                                        </button>
                                    </div>
                                </div>

                                <!-- معلومات المنتج -->
                                <div class="col-md-8">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">كود المنتج *</label>
                                            <input type="text" class="form-control" id="productCode" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">الباركود</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="productBarcode" placeholder="أدخل أو أنشئ باركود">
                                                <button type="button" class="btn btn-outline-secondary" onclick="generateBarcode()" title="إنشاء باركود تلقائي">
                                                    <i class="bi bi-arrow-clockwise"></i> إنشاء
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">اسم المنتج *</label>
                                        <input type="text" class="form-control" id="productName" required>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">الفئة *</label>
                                            <select class="form-select" id="productCategory" required>
                                                <option value="">اختر الفئة</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">الوحدة</label>
                                            <select class="form-select" id="productUnit">
                                                <option value="قطعة">قطعة</option>
                                                <option value="كرتون">كرتون</option>
                                                <option value="كيلو">كيلو</option>
                                                <option value="لتر">لتر</option>
                                                <option value="متر">متر</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">سعر الشراء *</label>
                                    <input type="number" class="form-control" id="productPurchasePrice"
                                           step="0.01" min="0" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">سعر البيع *</label>
                                    <input type="number" class="form-control" id="productSalePrice"
                                           step="0.01" min="0" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">سعر الجملة</label>
                                    <input type="number" class="form-control" id="productWholesalePrice"
                                           step="0.01" min="0">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الكمية الحالية *</label>
                                    <input type="number" class="form-control" id="productQuantity"
                                           min="0" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الحد الأدنى للكمية</label>
                                    <input type="number" class="form-control" id="productMinQuantity"
                                           value="10" min="0">
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle"></i> إلغاء
                        </button>
                        <button type="button" class="btn btn-primary" onclick="saveProduct()">
                            <i class="bi bi-check-circle"></i> حفظ
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal طباعة الباركود -->
        <div class="modal fade" id="barcodeModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-upc-scan"></i> إنشاء وطباعة الباركود
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <!-- إعدادات الباركود -->
                            <div class="col-md-6">
                                <h6 class="mb-3"><i class="bi bi-gear"></i> إعدادات الباركود</h6>

                                <div class="mb-3">
                                    <label class="form-label">رقم الباركود</label>
                                    <input type="text" class="form-control" id="barcodeValue" readonly>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">اسم المنتج</label>
                                    <input type="text" class="form-control" id="barcodeProductName" readonly>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">السعر</label>
                                    <input type="number" class="form-control" id="barcodePrice" readonly>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">عدد النسخ</label>
                                    <input type="number" class="form-control" id="barcodeCopies" value="1" min="1" max="100">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">حجم الباركود</label>
                                    <select class="form-select" id="barcodeSize" onchange="updateBarcodePreview()">
                                        <option value="small">صغير (40mm × 25mm)</option>
                                        <option value="medium" selected>متوسط (50mm × 30mm)</option>
                                        <option value="large">كبير (60mm × 40mm)</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">طريقة الطباعة</label>
                                    <select class="form-select" id="barcodePrintMethod">
                                        <option value="browser">طباعة عبر المتصفح (افتراضي)</option>
                                        <option value="direct">طباعة مباشرة على طابعة USB</option>
                                    </select>
                                </div>

                                <div class="mb-3" id="printerSelectionDiv" style="display: none;">
                                    <label class="form-label">اختر الطابعة</label>
                                    <select class="form-select" id="selectedPrinter">
                                        <option value="">جاري تحميل الطابعات...</option>
                                    </select>
                                    <small class="text-muted">تأكد من توصيل الطابعة عبر USB</small>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="barcodeShowPrice" checked onchange="updateBarcodePreview()">
                                        <label class="form-check-label" for="barcodeShowPrice">
                                            عرض السعر
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="barcodeShowName" checked onchange="updateBarcodePreview()">
                                        <label class="form-check-label" for="barcodeShowName">
                                            عرض اسم المنتج
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- معاينة الباركود -->
                            <div class="col-md-6">
                                <h6 class="mb-3"><i class="bi bi-eye"></i> معاينة</h6>
                                <div id="barcodePreview" class="text-center p-4 border rounded bg-light">
                                    <!-- سيتم إنشاء الباركود هنا -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle"></i> إلغاء
                        </button>
                        <button type="button" class="btn btn-primary" onclick="printBarcode()">
                            <i class="bi bi-printer"></i> طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    loadProducts();

    // تأخير بسيط للتأكد من تحميل الـ Modal في الـ DOM
    setTimeout(() => {
        loadCategories();
    }, 100);
}

async function loadProducts() {
    try {
        const response = await fetch(`${API_URL}/products`);
        const products = await response.json();

        const tbody = document.getElementById('productsTable');
        tbody.innerHTML = products.map(p => `
            <tr class="${p.quantity <= p.minQuantity ? 'low-stock' : ''} ${p.quantity < 0 ? 'negative-stock' : ''}">
                <td>
                    ${p.image ?
                        `<img src="${p.image}" alt="${p.name}" class="product-thumb">` :
                        `<div class="product-thumb-placeholder"><i class="bi bi-image"></i></div>`
                    }
                </td>
                <td>${p.code}</td>
                <td>${p.name}</td>
                <td><span class="badge bg-info">${p.category}</span></td>
                <td>${formatCurrency(p.purchasePrice)}</td>
                <td>${formatCurrency(p.salePrice)}</td>
                <td>
                    <span class="badge ${p.quantity <= 0 ? 'bg-danger' : p.quantity <= p.minQuantity ? 'bg-warning' : 'bg-success'}">
                        ${p.quantity}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-secondary" onclick="showBarcodeModal(${p.id}, '${p.barcode || p.code}', '${p.name.replace(/'/g, "\\'")}', ${p.salePrice})" title="باركود" data-permission-module="Products" data-permission-action="print">
                        <i class="bi bi-upc-scan"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="editProduct(${p.id})" data-permission-module="Products" data-permission-action="edit">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteProduct(${p.id})" data-permission-module="Products" data-permission-action="delete">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    } catch (error) {
        console.error('Error loading products:', error);
    }
}

async function loadCategories() {
    try {
        const response = await fetch(`${API_URL}/categories`);
        const categories = await response.json();

        const select = document.getElementById('productCategory');
        if (select) {
            select.innerHTML = '<option value="">اختر الفئة</option>';
            categories.forEach(c => {
                const option = document.createElement('option');
                option.value = c.name;
                option.textContent = c.name;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

async function showProductModal(product = null) {
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    const form = document.getElementById('productForm');
    form.reset();

    document.getElementById('productModalTitle').innerHTML = product ?
        '<i class="bi bi-pencil"></i> تعديل منتج' :
        '<i class="bi bi-box-seam"></i> منتج جديد';

    // تحميل الفئات مباشرة
    try {
        const response = await fetch(`${API_URL}/categories`);
        const categories = await response.json();

        const select = document.getElementById('productCategory');
        if (select) {
            select.innerHTML = '<option value="">اختر الفئة</option>';
            categories.forEach(cat => {
                const option = document.createElement('option');
                option.value = cat.name;
                option.textContent = cat.name;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading categories:', error);
    }

    if (product) {
        document.getElementById('productId').value = product.id;
        document.getElementById('productCode').value = product.code;
        document.getElementById('productBarcode').value = product.barcode || '';
        document.getElementById('productName').value = product.name;

        // تعيين الفئة بعد تحميل الفئات
        setTimeout(() => {
            document.getElementById('productCategory').value = product.category;
        }, 100);

        document.getElementById('productUnit').value = product.unit || 'قطعة';
        document.getElementById('productPurchasePrice').value = product.purchasePrice;
        document.getElementById('productSalePrice').value = product.salePrice;
        document.getElementById('productWholesalePrice').value = product.wholesalePrice;
        document.getElementById('productQuantity').value = product.quantity;
        document.getElementById('productMinQuantity').value = product.minQuantity;

        if (product.image) {
            document.getElementById('productImagePreview').src = product.image;
            document.getElementById('productImagePreview').classList.remove('d-none');
            document.getElementById('productImagePlaceholder').classList.add('d-none');
            document.getElementById('removeImageBtn').style.display = 'inline-block';
        }
    } else {
        document.getElementById('productImagePreview').classList.add('d-none');
        document.getElementById('productImagePlaceholder').classList.remove('d-none');
        document.getElementById('removeImageBtn').style.display = 'none';
    }

    modal.show();
}

function previewProductImage(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('productImagePreview').src = e.target.result;
            document.getElementById('productImagePreview').classList.remove('d-none');
            document.getElementById('productImagePlaceholder').classList.add('d-none');
            document.getElementById('removeImageBtn').style.display = 'inline-block';
        };
        reader.readAsDataURL(file);
    }
}

function removeProductImage() {
    document.getElementById('productImage').value = '';
    document.getElementById('productImagePreview').src = '';
    document.getElementById('productImagePreview').classList.add('d-none');
    document.getElementById('productImagePlaceholder').classList.remove('d-none');
    document.getElementById('removeImageBtn').style.display = 'none';
}

async function saveProduct() {
    const id = document.getElementById('productId').value;
    const imageFile = document.getElementById('productImage').files[0];
    let imageBase64 = null;

    // تحويل الصورة إلى Base64 إذا تم اختيارها
    if (imageFile) {
        imageBase64 = await new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.readAsDataURL(imageFile);
        });
    } else if (id) {
        // الاحتفاظ بالصورة القديمة عند التعديل
        const preview = document.getElementById('productImagePreview');
        if (!preview.classList.contains('d-none')) {
            imageBase64 = preview.src;
        }
    }

    // قراءة القيم من الحقول
    const code = document.getElementById('productCode').value.trim();
    const name = document.getElementById('productName').value.trim();
    const barcode = document.getElementById('productBarcode').value || null;
    const category = document.getElementById('productCategory').value;
    const unit = document.getElementById('productUnit').value;
    const purchasePriceStr = document.getElementById('productPurchasePrice').value;
    const salePriceStr = document.getElementById('productSalePrice').value;
    const wholesalePriceStr = document.getElementById('productWholesalePrice').value;
    const quantityStr = document.getElementById('productQuantity').value;
    const minQuantityStr = document.getElementById('productMinQuantity').value;

    // التحقق من الحقول المطلوبة قبل إرسال الطلب
    if (!code || !name || !category || !purchasePriceStr || !salePriceStr || !quantityStr) {
        showToast('الرجاء تعبئة جميع الحقول المطلوبة (الكود، الاسم، الفئة، الأسعار، الكمية).', 'warning');
        return;
    }

    const purchasePrice = parseFloat(purchasePriceStr);
    const salePrice = parseFloat(salePriceStr);
    const quantity = parseInt(quantityStr);
    const wholesalePrice = wholesalePriceStr
        ? parseFloat(wholesalePriceStr)
        : salePrice;
    const minQuantity = minQuantityStr
        ? parseInt(minQuantityStr)
        : 0;

    if (Number.isNaN(purchasePrice) || Number.isNaN(salePrice) || Number.isNaN(quantity) ||
        Number.isNaN(wholesalePrice) || Number.isNaN(minQuantity)) {
        showToast('تأكد أن قيم الأسعار والكمية أرقام صحيحة.', 'warning');
        return;
    }

    const productData = {
        code,
        name,
        nameAr: name,
        barcode,
        category,
        unit,
        purchasePrice,
        salePrice,
        wholesalePrice,
        quantity,
        minQuantity,
        image: imageBase64,
        isActive: true
    };

    let success;
    if (id) {
        productData.id = parseInt(id);
        success = await updateProduct(id, productData);
    } else {
        success = await createProduct(productData);
    }

    if (success) {
        bootstrap.Modal.getInstance(document.getElementById('productModal')).hide();
    }
}

async function createProduct(product) {
    try {
        const response = await fetch(`${API_URL}/products`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(product)
        });

        // قراءة رد الخادم (قد يحتوي على رسالة خطأ أو بيانات المنتج)
        let result = {};
        try {
            result = await response.json();
        } catch (_) {
            result = {};
        }

        if (response.ok) {
            // في حالة النجاح
            showToast(result.message || 'تم إضافة المنتج بنجاح', 'success');
            loadProducts();
            return true;
        } else {
            // في حالة حدوث خطأ من الخادم نحاول قراءة تفاصيل الخطأ من الرد
            let message = 'فشل إضافة المنتج، تحقق من البيانات أو تواصل مع المبرمج';

            if (result) {
                if (result.message) {
                    // رسالة خطأ صريحة من السيرفر
                    message = result.message;
                } else if (result.errors) {
                    // أخطاء ModelState (مثلاً من [ApiController])
                    const errors = [];
                    for (const key in result.errors) {
                        if (Array.isArray(result.errors[key]) && result.errors[key].length > 0) {
                            errors.push(result.errors[key][0]);
                        }
                    }
                    if (errors.length > 0) {
                        message = errors.join(' - ');
                    }
                } else if (result.title) {
                    // بعض ردود ASP.NET Core تستخدم الحقل Title
                    message = result.title;
                }
            }

            message += ` (رمز الخطأ: ${response.status})`;
            showToast(message, 'danger');
            console.error('Error creating product (server error):', response.status, result);
            return false;
        }
    } catch (error) {
        console.error('Error creating product (network error):', error);
        showToast('فشل الاتصال بالخادم أثناء حفظ المنتج', 'danger');
        return false;
    }
}

async function updateProduct(id, product) {
    try {
        product.id = id;
        const response = await fetch(`${API_URL}/products/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(product)
        });

        let result = {};
        try {
            if (response.headers.get('Content-Type')?.includes('application/json')) {
                result = await response.json();
            }
        } catch {
            result = {};
        }

        if (response.ok) {
            showToast('تم تحديث المنتج بنجاح', 'success');
            loadProducts();
            return true;
        } else {
            let message = 'فشل تحديث المنتج، تحقق من البيانات أو تواصل مع المبرمج';

            if (result && result.message) {
                message = result.message;
            }

            message += ` (رمز الخطأ: ${response.status})`;
            showToast(message, 'danger');
            console.error('Error updating product (server error):', response.status, result);
            return false;
        }
    } catch (error) {
        console.error('Error updating product (network error):', error);
        showToast('فشل الاتصال بالخادم أثناء تحديث المنتج', 'danger');
        return false;
    }
}

async function editProduct(id) {
    try {
        const response = await fetch(`${API_URL}/products/${id}`);
        const product = await response.json();
        showProductModal(product);
    } catch (error) {
        console.error('Error loading product:', error);
    }
}

async function deleteProduct(id) {
    if (confirm('هل تريد حذف هذا المنتج؟')) {
        try {
            const response = await fetch(`${API_URL}/products/${id}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                showToast('تم حذف المنتج بنجاح', 'success');
                loadProducts();
            }
        } catch (error) {
            console.error('Error deleting product:', error);
        }
    }
}

// ==================== Barcode Functions ====================

let currentBarcodeData = null;

// إنشاء باركود تلقائي
function generateBarcode() {
    // توليد رقم باركود من 13 رقم (EAN-13 format)
    // الصيغة: 2 (بادئة محلية) + 10 أرقام عشوائية + 1 رقم تحقق

    const prefix = '20'; // بادئة للمنتجات المحلية
    let randomDigits = '';

    // توليد 10 أرقام عشوائية
    for (let i = 0; i < 10; i++) {
        randomDigits += Math.floor(Math.random() * 10);
    }

    const barcodeWithoutChecksum = prefix + randomDigits;

    // حساب رقم التحقق (checksum) لـ EAN-13
    let sum = 0;
    for (let i = 0; i < 12; i++) {
        const digit = parseInt(barcodeWithoutChecksum[i]);
        sum += (i % 2 === 0) ? digit : digit * 3;
    }
    const checksum = (10 - (sum % 10)) % 10;

    const barcode = barcodeWithoutChecksum + checksum;

    // وضع الباركود في الحقل
    document.getElementById('productBarcode').value = barcode;

    showToast('تم إنشاء باركود جديد: ' + barcode, 'success');
}

function showBarcodeModal(productId, barcode, productName, price) {
    currentBarcodeData = {
        productId,
        barcode,
        productName,
        price
    };

    document.getElementById('barcodeValue').value = barcode;
    document.getElementById('barcodeProductName').value = productName;
    document.getElementById('barcodePrice').value = price;
    document.getElementById('barcodeCopies').value = 1;
    document.getElementById('barcodeSize').value = 'medium';
    document.getElementById('barcodePrintMethod').value = 'browser';
    document.getElementById('barcodeShowPrice').checked = true;
    document.getElementById('barcodeShowName').checked = true;
    document.getElementById('printerSelectionDiv').style.display = 'none';

    // إضافة مستمع لتغيير طريقة الطباعة
    document.getElementById('barcodePrintMethod').onchange = function() {
        const method = this.value;
        const printerDiv = document.getElementById('printerSelectionDiv');

        if (method === 'direct') {
            printerDiv.style.display = 'block';
            loadAvailablePrinters();
        } else {
            printerDiv.style.display = 'none';
        }
    };

    updateBarcodePreview();

    const modal = new bootstrap.Modal(document.getElementById('barcodeModal'));
    modal.show();
}

// تحميل الطابعات المتاحة
async function loadAvailablePrinters() {
    const select = document.getElementById('selectedPrinter');

    try {
        // استخدام Web Print API إذا كان متاحاً
        if ('getInstalledRelatedApps' in navigator) {
            select.innerHTML = '<option value="">جاري البحث عن الطابعات...</option>';

            // محاولة الحصول على قائمة الطابعات من النظام
            // ملاحظة: هذا يتطلب إعدادات خاصة في المتصفح
            const printers = await getSystemPrinters();

            if (printers && printers.length > 0) {
                select.innerHTML = '<option value="">اختر الطابعة</option>';
                printers.forEach(printer => {
                    const option = document.createElement('option');
                    option.value = printer.name;
                    option.textContent = printer.name;
                    select.appendChild(option);
                });
            } else {
                select.innerHTML = '<option value="">لم يتم العثور على طابعات</option>';
            }
        } else {
            // إذا لم يكن API متاحاً، استخدم الطابعة الافتراضية
            select.innerHTML = `
                <option value="default">الطابعة الافتراضية</option>
                <option value="usb">طابعة USB</option>
                <option value="thermal">طابعة حرارية</option>
            `;
        }
    } catch (error) {
        console.error('Error loading printers:', error);
        select.innerHTML = `
            <option value="default">الطابعة الافتراضية</option>
            <option value="usb">طابعة USB</option>
            <option value="thermal">طابعة حرارية</option>
        `;
    }
}

// الحصول على طابعات النظام
async function getSystemPrinters() {
    // هذه دالة مساعدة - في الواقع، المتصفحات لا توفر API مباشر للطابعات
    // سنستخدم الطباعة عبر نافذة الطباعة القياسية
    return [
        { name: 'الطابعة الافتراضية', id: 'default' }
    ];
}

function updateBarcodePreview() {
    if (!currentBarcodeData) return;

    const size = document.getElementById('barcodeSize').value;
    const showPrice = document.getElementById('barcodeShowPrice').checked;
    const showName = document.getElementById('barcodeShowName').checked;

    const preview = document.getElementById('barcodePreview');
    preview.innerHTML = generateBarcodeHTML(
        currentBarcodeData.barcode,
        currentBarcodeData.productName,
        currentBarcodeData.price,
        size,
        showPrice,
        showName
    );

    // إنشاء الباركود باستخدام JsBarcode
    setTimeout(() => {
        const svg = document.getElementById(`barcode-svg-${currentBarcodeData.barcode}`);
        if (svg && typeof JsBarcode !== 'undefined') {
            JsBarcode(svg, currentBarcodeData.barcode, {
                format: 'CODE128',
                width: 2,
                height: 50,
                displayValue: false,
                margin: 0
            });
        }
    }, 100);
}

function generateBarcodeHTML(barcode, productName, price, size, showPrice, showName) {
    const sizes = {
        small: { width: '150px', height: '80px', fontSize: '10px', barcodeHeight: '40px' },
        medium: { width: '190px', height: '110px', fontSize: '12px', barcodeHeight: '50px' },
        large: { width: '230px', height: '140px', fontSize: '14px', barcodeHeight: '60px' }
    };

    const s = sizes[size];

    return `
        <div class="barcode-label" style="
            width: ${s.width};
            height: ${s.height};
            border: 1px solid #ddd;
            padding: 8px;
            margin: 10px auto;
            background: white;
            font-family: Arial, sans-serif;
            display: inline-block;
        ">
            ${showName ? `
                <div style="font-size: ${s.fontSize}; font-weight: bold; text-align: center; margin-bottom: 4px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    ${productName}
                </div>
            ` : ''}

            <div style="text-align: center; margin: 5px 0;">
                <svg id="barcode-svg-${barcode}" style="height: ${s.barcodeHeight};"></svg>
            </div>

            <div style="font-size: ${s.fontSize}; text-align: center; font-family: monospace; margin-top: 2px;">
                ${barcode}
            </div>

            ${showPrice ? `
                <div style="font-size: ${s.fontSize}; font-weight: bold; text-align: center; margin-top: 4px;">
                    ${formatCurrency(price)}
                </div>
            ` : ''}
        </div>
    `;
}

function printBarcode() {
    if (!currentBarcodeData) return;

    const copies = parseInt(document.getElementById('barcodeCopies').value) || 1;
    const size = document.getElementById('barcodeSize').value;
    const showPrice = document.getElementById('barcodeShowPrice').checked;
    const showName = document.getElementById('barcodeShowName').checked;
    const printMethod = document.getElementById('barcodePrintMethod').value;
    const selectedPrinter = document.getElementById('selectedPrinter').value;

    if (printMethod === 'direct') {
        // الطباعة المباشرة
        printBarcodeDirectly(copies, size, showPrice, showName, selectedPrinter);
    } else {
        // الطباعة عبر المتصفح (الطريقة الافتراضية)
        printBarcodeBrowser(copies, size, showPrice, showName);
    }
}

// الطباعة عبر المتصفح
function printBarcodeBrowser(copies, size, showPrice, showName) {
    // إنشاء نافذة الطباعة
    const printWindow = window.open('', '_blank');

    let barcodesHTML = '';
    for (let i = 0; i < copies; i++) {
        barcodesHTML += generateBarcodeHTML(
            currentBarcodeData.barcode,
            currentBarcodeData.productName,
            currentBarcodeData.price,
            size,
            showPrice,
            showName
        );
    }

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>طباعة باركود - ${currentBarcodeData.productName}</title>
            <style>
                @media print {
                    @page {
                        size: auto;
                        margin: 5mm;
                    }
                    body {
                        margin: 0;
                        padding: 10px;
                    }
                }
                body {
                    font-family: Arial, sans-serif;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 5px;
                    justify-content: flex-start;
                }
                .barcode-label {
                    page-break-inside: avoid;
                }
            </style>
            <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
        </head>
        <body>
            ${barcodesHTML}
            <script>
                // إنشاء الباركود لكل نسخة
                document.querySelectorAll('[id^="barcode-svg-"]').forEach(svg => {
                    const barcodeValue = svg.id.replace('barcode-svg-', '');
                    JsBarcode(svg, barcodeValue, {
                        format: 'CODE128',
                        width: 2,
                        height: 50,
                        displayValue: false,
                        margin: 0
                    });
                });

                // طباعة تلقائية بعد تحميل الباركود
                setTimeout(() => {
                    window.print();
                    // إغلاق النافذة بعد الطباعة (اختياري)
                    // window.close();
                }, 500);
            </script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

// الطباعة المباشرة على طابعة USB
async function printBarcodeDirectly(copies, size, showPrice, showName, printerName) {
    try {
        // إنشاء محتوى الطباعة
        let barcodesHTML = '';
        for (let i = 0; i < copies; i++) {
            barcodesHTML += generateBarcodeHTML(
                currentBarcodeData.barcode,
                currentBarcodeData.productName,
                currentBarcodeData.price,
                size,
                showPrice,
                showName
            );
        }

        // إنشاء iframe مخفي للطباعة
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        document.body.appendChild(iframe);

        const iframeDoc = iframe.contentWindow.document;
        iframeDoc.open();
        iframeDoc.write(`
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>طباعة باركود - ${currentBarcodeData.productName}</title>
                <style>
                    @media print {
                        @page {
                            size: auto;
                            margin: 5mm;
                        }
                        body {
                            margin: 0;
                            padding: 10px;
                        }
                    }
                    body {
                        font-family: Arial, sans-serif;
                        display: flex;
                        flex-wrap: wrap;
                        gap: 5px;
                        justify-content: flex-start;
                    }
                    .barcode-label {
                        page-break-inside: avoid;
                    }
                </style>
                <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
            </head>
            <body>
                ${barcodesHTML}
                <script>
                    // إنشاء الباركود لكل نسخة
                    document.querySelectorAll('[id^="barcode-svg-"]').forEach(svg => {
                        const barcodeValue = svg.id.replace('barcode-svg-', '');
                        JsBarcode(svg, barcodeValue, {
                            format: 'CODE128',
                            width: 2,
                            height: 50,
                            displayValue: false,
                            margin: 0
                        });
                    });

                    // طباعة مباشرة
                    setTimeout(() => {
                        window.print();

                        // إزالة iframe بعد الطباعة
                        setTimeout(() => {
                            document.body.removeChild(window.frameElement);
                        }, 1000);
                    }, 500);
                </script>
            </body>
            </html>
        `);
        iframeDoc.close();

        // إظهار رسالة نجاح
        showToast('جاري الطباعة على ' + (printerName || 'الطابعة الافتراضية'), 'success');

    } catch (error) {
        console.error('Error printing barcode:', error);
        showToast('فشل الطباعة: ' + error.message, 'danger');
    }
}

