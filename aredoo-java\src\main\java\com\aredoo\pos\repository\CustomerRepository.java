package com.aredoo.pos.repository;

import com.aredoo.pos.model.Customer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CustomerRepository extends JpaRepository<Customer, Long> {
    
    List<Customer> findByIsActiveTrue();
    
    List<Customer> findByIsActiveTrueOrderByName();
    
    Optional<Customer> findByPhone(String phone);
    
    Optional<Customer> findByEmail(String email);
    
    List<Customer> findByCustomerType(String customerType);
    
    List<Customer> findByCustomerTypeAndIsActiveTrue(String customerType);
    
    @Query("SELECT c FROM Customer c WHERE c.isActive = true AND " +
           "(LOWER(c.name) LIKE LOWER(CONCAT('%', :term, '%')) OR " +
           "LOWER(c.phone) LIKE LOWER(CONCAT('%', :term, '%')) OR " +
           "LOWER(c.email) LIKE LOWER(CONCAT('%', :term, '%')))")
    List<Customer> searchCustomers(@Param("term") String term);
    
    @Query("SELECT c FROM Customer c WHERE c.balance > 0 AND c.isActive = true")
    List<Customer> findCustomersWithBalance();
    
    @Query("SELECT COUNT(c) FROM Customer c WHERE c.isActive = true")
    long countActiveCustomers();
    
    @Query("SELECT SUM(c.balance) FROM Customer c WHERE c.isActive = true")
    Double getTotalCustomerBalance();
}
