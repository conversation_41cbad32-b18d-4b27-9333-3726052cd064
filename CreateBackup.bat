@echo off
chcp 65001 >nul
title نظام النسخ الاحتياطي - أريدوو

echo.
echo ===============================================
echo    نظام النسخ الاحتياطي لأريدوو
echo    Aredoo POS System - Backup Tool
echo ===============================================
echo.

REM التحقق من وجود PowerShell
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo ❌ PowerShell غير متوفر على هذا النظام
    echo يرجى تثبيت PowerShell أو استخدام Windows 10/11
    pause
    exit /b 1
)

REM تشغيل سكريبت PowerShell
echo جاري تشغيل نظام النسخ الاحتياطي...
echo.

powershell -ExecutionPolicy Bypass -File "CreateBackup.ps1" -OpenFolder

if errorlevel 1 (
    echo.
    echo ❌ فشل في إنشاء النسخة الاحتياطية
    pause
    exit /b 1
)

echo.
echo ✅ تم الانتهاء بنجاح!
pause
