package com.aredoo.pos.controller;

import com.aredoo.pos.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/reports")
@CrossOrigin(origins = "*")
public class ReportController {

    @Autowired
    private InvoiceRepository invoiceRepository;
    
    @Autowired
    private ProductRepository productRepository;
    
    @Autowired
    private CustomerRepository customerRepository;
    
    @Autowired
    private SupplierRepository supplierRepository;
    
    @Autowired
    private ExpenseRepository expenseRepository;

    @GetMapping("/dashboard")
    public ResponseEntity<Map<String, Object>> getDashboardData() {
        Map<String, Object> dashboard = new HashMap<>();
        
        // Sales statistics
        long totalSales = invoiceRepository.countSaleInvoices();
        BigDecimal totalSalesAmount = invoiceRepository.getTotalSalesAmount();
        BigDecimal totalRemaining = invoiceRepository.getTotalRemainingAmount();
        
        // Product statistics
        long totalProducts = productRepository.countActiveProducts();
        Double inventoryValue = productRepository.getTotalInventoryValue();
        
        // Customer statistics
        long totalCustomers = customerRepository.countActiveCustomers();
        Double customerBalance = customerRepository.getTotalCustomerBalance();
        
        // Supplier statistics
        long totalSuppliers = supplierRepository.countActiveSuppliers();
        Double supplierBalance = supplierRepository.getTotalSupplierBalance();
        
        // Expense statistics
        BigDecimal totalExpenses = expenseRepository.getTotalExpenseAmount();
        
        dashboard.put("sales", Map.of(
            "totalInvoices", totalSales,
            "totalAmount", totalSalesAmount != null ? totalSalesAmount : BigDecimal.ZERO,
            "totalRemaining", totalRemaining != null ? totalRemaining : BigDecimal.ZERO
        ));
        
        dashboard.put("products", Map.of(
            "totalProducts", totalProducts,
            "inventoryValue", inventoryValue != null ? inventoryValue : 0.0
        ));
        
        dashboard.put("customers", Map.of(
            "totalCustomers", totalCustomers,
            "totalBalance", customerBalance != null ? customerBalance : 0.0
        ));
        
        dashboard.put("suppliers", Map.of(
            "totalSuppliers", totalSuppliers,
            "totalBalance", supplierBalance != null ? supplierBalance : 0.0
        ));
        
        dashboard.put("expenses", Map.of(
            "totalExpenses", totalExpenses != null ? totalExpenses : BigDecimal.ZERO
        ));
        
        return ResponseEntity.ok(dashboard);
    }

    @GetMapping("/sales")
    public ResponseEntity<Map<String, Object>> getSalesReport(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "Sale") String type) {
        
        Map<String, Object> report = new HashMap<>();
        
        if (startDate != null && endDate != null) {
            LocalDateTime start = LocalDateTime.parse(startDate + "T00:00:00");
            LocalDateTime end = LocalDateTime.parse(endDate + "T23:59:59");
            
            var invoices = invoiceRepository.findByTypeAndDateRange(type, start, end);
            
            BigDecimal totalAmount = invoices.stream()
                .map(invoice -> invoice.getTotal())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
                
            BigDecimal totalPaid = invoices.stream()
                .map(invoice -> invoice.getPaid())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
                
            BigDecimal totalRemaining = invoices.stream()
                .map(invoice -> invoice.getRemaining())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            report.put("invoices", invoices);
            report.put("summary", Map.of(
                "totalInvoices", invoices.size(),
                "totalAmount", totalAmount,
                "totalPaid", totalPaid,
                "totalRemaining", totalRemaining
            ));
        } else {
            var invoices = invoiceRepository.findByTypeAndIsDeletedFalse(type);
            report.put("invoices", invoices);
        }
        
        return ResponseEntity.ok(report);
    }

    @GetMapping("/inventory")
    public ResponseEntity<Map<String, Object>> getInventoryReport() {
        Map<String, Object> report = new HashMap<>();
        
        var allProducts = productRepository.findByIsActiveTrueOrderByName();
        var lowStockProducts = productRepository.findLowStockProducts();
        
        Double totalValue = productRepository.getTotalInventoryValue();
        
        report.put("allProducts", allProducts);
        report.put("lowStockProducts", lowStockProducts);
        report.put("summary", Map.of(
            "totalProducts", allProducts.size(),
            "lowStockCount", lowStockProducts.size(),
            "totalValue", totalValue != null ? totalValue : 0.0
        ));
        
        return ResponseEntity.ok(report);
    }

    @GetMapping("/expenses")
    public ResponseEntity<Map<String, Object>> getExpensesReport(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        Map<String, Object> report = new HashMap<>();
        
        if (startDate != null && endDate != null) {
            LocalDateTime start = LocalDateTime.parse(startDate + "T00:00:00");
            LocalDateTime end = LocalDateTime.parse(endDate + "T23:59:59");
            
            var expenses = expenseRepository.findByDateRange(start, end);
            BigDecimal totalAmount = expenseRepository.getTotalExpenseAmountByDateRange(start, end);
            
            report.put("expenses", expenses);
            report.put("summary", Map.of(
                "totalExpenses", expenses.size(),
                "totalAmount", totalAmount != null ? totalAmount : BigDecimal.ZERO
            ));
        } else {
            var expenses = expenseRepository.findAllByOrderByExpenseDateDesc();
            BigDecimal totalAmount = expenseRepository.getTotalExpenseAmount();
            
            report.put("expenses", expenses);
            report.put("summary", Map.of(
                "totalExpenses", expenses.size(),
                "totalAmount", totalAmount != null ? totalAmount : BigDecimal.ZERO
            ));
        }
        
        return ResponseEntity.ok(report);
    }
}
