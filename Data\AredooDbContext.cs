using Microsoft.EntityFrameworkCore;
using Aredoo.Server.Models;

namespace Aredoo.Server.Data;

public class AredooDbContext : DbContext
{
    public AredooDbContext(DbContextOptions<AredooDbContext> options) : base(options)
    {
    }

    public DbSet<User> Users { get; set; }
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<Product> Products { get; set; }
    public DbSet<Category> Categories { get; set; }
    public DbSet<Customer> Customers { get; set; }
    public DbSet<Supplier> Suppliers { get; set; }
    public DbSet<Invoice> Invoices { get; set; }
    public DbSet<InvoiceItem> InvoiceItems { get; set; }
    public DbSet<Employee> Employees { get; set; }
    public DbSet<Attendance> Attendances { get; set; }
    public DbSet<WorkShift> WorkShifts { get; set; }
    public DbSet<Installment> Installments { get; set; }
    public DbSet<Transaction> Transactions { get; set; }
    public DbSet<Settings> Settings { get; set; }
    public DbSet<AuditLog> AuditLogs { get; set; }
    public DbSet<Expense> Expenses { get; set; }
    public DbSet<Purchase> Purchases { get; set; }
    public DbSet<PurchaseItem> PurchaseItems { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Seed default admin user
        modelBuilder.Entity<User>().HasData(
            new User
            {
                Id = 1,
                Username = "admin",
                PasswordHash = BCrypt.Net.BCrypt.HashPassword("1234"),
                FullName = "المدير العام",
                Role = "Admin",
                IsActive = true,
                CreatedAt = DateTime.Now
            }
        );

        // Seed default settings
        modelBuilder.Entity<Settings>().HasData(
            new Settings
            {
                Id = 1,
                CompanyName = "أريدوو - Aredoo",
                CompanyNameAr = "أريدوو",
                Currency = "IQD",
                CurrencySymbol = "د.ع",
                Language = "ar",
                InvoiceFooter = "شكراً لتعاملكم معنا",
                PrinterName = "Default",
                PaperSize = "80mm",
                AutoPrint = false,
                TaxRate = 0,
                UpdatedAt = DateTime.Now
            }
        );

        // Configure decimal precision
        modelBuilder.Entity<Product>()
            .Property(p => p.PurchasePrice).HasPrecision(18, 2);
        modelBuilder.Entity<Product>()
            .Property(p => p.SalePrice).HasPrecision(18, 2);
        modelBuilder.Entity<Product>()
            .Property(p => p.WholesalePrice).HasPrecision(18, 2);

        modelBuilder.Entity<Invoice>()
            .Property(i => i.SubTotal).HasPrecision(18, 2);
        modelBuilder.Entity<Invoice>()
            .Property(i => i.Discount).HasPrecision(18, 2);
        modelBuilder.Entity<Invoice>()
            .Property(i => i.Tax).HasPrecision(18, 2);
        modelBuilder.Entity<Invoice>()
            .Property(i => i.Total).HasPrecision(18, 2);
        modelBuilder.Entity<Invoice>()
            .Property(i => i.Paid).HasPrecision(18, 2);
        modelBuilder.Entity<Invoice>()
            .Property(i => i.Remaining).HasPrecision(18, 2);

        modelBuilder.Entity<InvoiceItem>()
            .Property(ii => ii.Quantity).HasPrecision(18, 2);
        modelBuilder.Entity<InvoiceItem>()
            .Property(ii => ii.UnitPrice).HasPrecision(18, 2);
        modelBuilder.Entity<InvoiceItem>()
            .Property(ii => ii.Discount).HasPrecision(18, 2);
        modelBuilder.Entity<InvoiceItem>()
            .Property(ii => ii.Total).HasPrecision(18, 2);
        modelBuilder.Entity<InvoiceItem>()
            .Property(ii => ii.PurchasePrice).HasPrecision(18, 2);
        modelBuilder.Entity<InvoiceItem>()
            .Property(ii => ii.Profit).HasPrecision(18, 2);

        modelBuilder.Entity<Customer>()
            .Property(c => c.Balance).HasPrecision(18, 2);
        modelBuilder.Entity<Customer>()
            .Property(c => c.CreditLimit).HasPrecision(18, 2);

        modelBuilder.Entity<Supplier>()
            .Property(s => s.Balance).HasPrecision(18, 2);

        modelBuilder.Entity<Employee>()
            .Property(e => e.Salary).HasPrecision(18, 2);

        modelBuilder.Entity<Installment>()
            .Property(i => i.Amount).HasPrecision(18, 2);
        modelBuilder.Entity<Installment>()
            .Property(i => i.PaidAmount).HasPrecision(18, 2);

        modelBuilder.Entity<Transaction>()
            .Property(t => t.Amount).HasPrecision(18, 2);

        modelBuilder.Entity<Settings>()
            .Property(s => s.TaxRate).HasPrecision(18, 2);

        modelBuilder.Entity<Expense>()
            .Property(e => e.Amount).HasPrecision(18, 2);

        modelBuilder.Entity<Purchase>()
            .Property(p => p.SubTotal).HasPrecision(18, 2);
        modelBuilder.Entity<Purchase>()
            .Property(p => p.Discount).HasPrecision(18, 2);
        modelBuilder.Entity<Purchase>()
            .Property(p => p.Tax).HasPrecision(18, 2);
        modelBuilder.Entity<Purchase>()
            .Property(p => p.Total).HasPrecision(18, 2);
        modelBuilder.Entity<Purchase>()
            .Property(p => p.Paid).HasPrecision(18, 2);
        modelBuilder.Entity<Purchase>()
            .Property(p => p.Remaining).HasPrecision(18, 2);

        modelBuilder.Entity<PurchaseItem>()
            .Property(pi => pi.Quantity).HasPrecision(18, 2);
        modelBuilder.Entity<PurchaseItem>()
            .Property(pi => pi.UnitPrice).HasPrecision(18, 2);
        modelBuilder.Entity<PurchaseItem>()
            .Property(pi => pi.Discount).HasPrecision(18, 2);
        modelBuilder.Entity<PurchaseItem>()
            .Property(pi => pi.Total).HasPrecision(18, 2);

        // Configure User-Permission relationship
        modelBuilder.Entity<Permission>()
            .HasOne(p => p.User)
            .WithMany(u => u.Permissions)
            .HasForeignKey(p => p.UserId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}

