{"Version": 1, "Hash": "n/je/euVFtvwglhpjEEbfNJbhNU+cFR0qMX6ZuzYEjU=", "Source": "Aredoo.Server", "BasePath": "_content/Aredoo.Server", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Aredoo.Server\\wwwroot", "Source": "Aredoo.Server", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\25wrlhmap0-2p28n1lgul.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "index#[.{fingerprint=2p28n1lgul}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7m7o73mfcj", "Integrity": "cVDSNuyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\index.html", "FileLength": 1226, "LastWriteTime": "2025-11-15T16:54:03+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\5lixpq0z40-51r6od7olg.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/pos#[.{fingerprint=51r6od7olg}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\pos.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pcrxd91t7y", "Integrity": "uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\pos.js", "FileLength": 6895, "LastWriteTime": "2025-11-15T16:54:03+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\8c7jydm8ty-iyy8yegl4k.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/auth#[.{fingerprint=iyy8yegl4k}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\auth.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "voushzzqiw", "Integrity": "X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\auth.js", "FileLength": 1135, "LastWriteTime": "2025-11-15T16:54:03+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\ill7x5ozqf-hy3h9tabsi.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/customers#[.{fingerprint=hy3h9tabsi}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\customers.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nynguhmsve", "Integrity": "sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\customers.js", "FileLength": 2684, "LastWriteTime": "2025-11-15T16:54:03+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\kd4at48or8-x4y7n64zvw.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/settings#[.{fingerprint=x4y7n64zvw}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\settings.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fdxzdd7euf", "Integrity": "swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\settings.js", "FileLength": 11940, "LastWriteTime": "2025-11-15T18:43:52+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\pv27i78e2o-2cv1skr50a.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "print-invoice#[.{fingerprint=2cv1skr50a}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\print-invoice.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f16cn8vlan", "Integrity": "bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\print-invoice.html", "FileLength": 3987, "LastWriteTime": "2025-11-15T16:54:03+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\sif3j9439v-7ene3dnx54.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/invoices#[.{fingerprint=7ene3dnx54}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\invoices.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "w8qy1uzoa1", "Integrity": "oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\invoices.js", "FileLength": 4915, "LastWriteTime": "2025-11-15T16:54:03+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\tghp5tkl3n-jv2slhbvk0.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "css/style#[.{fingerprint=jv2slhbvk0}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\css\\style.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oukxn03nmb", "Integrity": "0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\css\\style.css", "FileLength": 3097, "LastWriteTime": "2025-11-15T16:54:03+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\vkpingxf2f-q4k5ude2ax.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/categories#[.{fingerprint=q4k5ude2ax}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\categories.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3h5qqccxjn", "Integrity": "N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\categories.js", "FileLength": 2390, "LastWriteTime": "2025-11-15T16:54:03+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\wa9tzd9v0l-9n0jr0b026.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/products#[.{fingerprint=9n0jr0b026}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\products.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ou6rhzkhk7", "Integrity": "cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\products.js", "FileLength": 8284, "LastWriteTime": "2025-11-15T18:35:35+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\wg78tukmqu-ds7d0gz88x.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/app#[.{fingerprint=ds7d0gz88x}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uw4rohcwxe", "Integrity": "YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\app.js", "FileLength": 1955, "LastWriteTime": "2025-11-15T16:54:03+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\x78a6k87cc-99d2u4ky5k.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/reports#[.{fingerprint=99d2u4ky5k}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\reports.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "t0bedaywmf", "Integrity": "sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\reports.js", "FileLength": 4591, "LastWriteTime": "2025-11-15T16:54:03+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\ziqm8tcidl-nqonojhrly.gz", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "login#[.{fingerprint=nqonojhrly}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\login.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dp8os1n73s", "Integrity": "701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\login.html", "FileLength": 2051, "LastWriteTime": "2025-11-15T16:54:03+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\css\\style.css", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "css/style#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jv2slhbvk0", "Integrity": "j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\style.css", "FileLength": 13011, "LastWriteTime": "2025-11-12T19:28:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\index.html", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2p28n1lgul", "Integrity": "pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 5415, "LastWriteTime": "2025-11-14T16:02:26+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\app.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ds7d0gz88x", "Integrity": "rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\app.js", "FileLength": 5664, "LastWriteTime": "2025-11-14T12:53:51+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\auth.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/auth#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iyy8yegl4k", "Integrity": "BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\auth.js", "FileLength": 3765, "LastWriteTime": "2025-11-13T15:25:05+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\categories.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/categories#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q4k5ude2ax", "Integrity": "Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\categories.js", "FileLength": 10343, "LastWriteTime": "2025-11-13T13:53:33+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\customers.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/customers#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hy3h9tabsi", "Integrity": "tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\customers.js", "FileLength": 14539, "LastWriteTime": "2025-11-13T13:52:53+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\invoices.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/invoices#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7ene3dnx54", "Integrity": "71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\invoices.js", "FileLength": 30213, "LastWriteTime": "2025-11-12T19:27:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\pos.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/pos#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "51r6od7olg", "Integrity": "p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\pos.js", "FileLength": 31619, "LastWriteTime": "2025-11-13T19:02:07+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\products.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/products#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9n0jr0b026", "Integrity": "+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\products.js", "FileLength": 44477, "LastWriteTime": "2025-11-15T18:35:17+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\reports.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/reports#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "99d2u4ky5k", "Integrity": "nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\reports.js", "FileLength": 38742, "LastWriteTime": "2025-11-14T15:33:52+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\settings.js", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "js/settings#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x4y7n64zvw", "Integrity": "kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\settings.js", "FileLength": 81891, "LastWriteTime": "2025-11-15T18:43:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\login.html", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "login#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "n<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\login.html", "FileLength": 6732, "LastWriteTime": "2025-11-14T15:38:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\print-invoice.html", "SourceId": "Aredoo.Server", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\", "BasePath": "_content/Aredoo.Server", "RelativePath": "print-invoice#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2cv1skr50a", "Integrity": "4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\print-invoice.html", "FileLength": 17980, "LastWriteTime": "2025-11-13T19:09:19+00:00"}], "Endpoints": [{"Route": "css/style.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\tghp5tkl3n-jv2slhbvk0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000322788896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3097"}, {"Name": "ETag", "Value": "\"0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc="}]}, {"Route": "css/style.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\css\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13011"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:28:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc="}]}, {"Route": "css/style.css.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\tghp5tkl3n-jv2slhbvk0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3097"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic="}]}, {"Route": "css/style.jv2slhbvk0.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\tghp5tkl3n-jv2slhbvk0.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000322788896"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3097"}, {"Name": "ETag", "Value": "\"0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jv2slhbvk0"}, {"Name": "label", "Value": "css/style.css"}, {"Name": "integrity", "Value": "sha256-j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc="}]}, {"Route": "css/style.jv2slhbvk0.css", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\css\\style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13011"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:28:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jv2slhbvk0"}, {"Name": "label", "Value": "css/style.css"}, {"Name": "integrity", "Value": "sha256-j4amGWopJuwhbBZN6JqaoLVhBnybrZb9ngQgudj2OZc="}]}, {"Route": "css/style.jv2slhbvk0.css.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\tghp5tkl3n-jv2slhbvk0.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3097"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jv2slhbvk0"}, {"Name": "label", "Value": "css/style.css.gz"}, {"Name": "integrity", "Value": "sha256-0E5MqM1sGKMFj/fDj+30KzbNY5yhoCTQhQKfT+3Q7ic="}]}, {"Route": "index.2p28n1lgul.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\25wrlhmap0-2p28n1lgul.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000814995925"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1226"}, {"Name": "ETag", "Value": "\"cVDSNuyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2p28n1lgul"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY="}]}, {"Route": "index.2p28n1lgul.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5415"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 16:02:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2p28n1lgul"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY="}]}, {"Route": "index.2p28n1lgul.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\25wrlhmap0-2p28n1lgul.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1226"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"cVDSNuyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2p28n1lgul"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-c<PERSON><PERSON>uyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\25wrlhmap0-2p28n1lgul.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000814995925"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1226"}, {"Name": "ETag", "Value": "\"cVDSNuyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5415"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 16:02:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pipglAWg7DVK2F02ZBv4Cn8XUpm5TvI8DCjYkFDzQeY="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\25wrlhmap0-2p28n1lgul.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1226"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"cVDSNuyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c<PERSON><PERSON>uyaJ9fSu8kTHIZ0jcgT9QxUJLKhcC6RgjSfRrU="}]}, {"Route": "js/app.ds7d0gz88x.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\wg78tukmqu-ds7d0gz88x.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000511247444"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1955"}, {"Name": "ETag", "Value": "\"YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ds7d0gz88x"}, {"Name": "label", "Value": "js/app.js"}, {"Name": "integrity", "Value": "sha256-rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY="}]}, {"Route": "js/app.ds7d0gz88x.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5664"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 12:53:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ds7d0gz88x"}, {"Name": "label", "Value": "js/app.js"}, {"Name": "integrity", "Value": "sha256-rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY="}]}, {"Route": "js/app.ds7d0gz88x.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\wg78tukmqu-ds7d0gz88x.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1955"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ds7d0gz88x"}, {"Name": "label", "Value": "js/app.js.gz"}, {"Name": "integrity", "Value": "sha256-YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw="}]}, {"Route": "js/app.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\wg78tukmqu-ds7d0gz88x.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000511247444"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1955"}, {"Name": "ETag", "Value": "\"YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY="}]}, {"Route": "js/app.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5664"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 12:53:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rdhQJz03J4ci2vau2BIw/wRGSlmeDkxq/0D2omzlFFY="}]}, {"Route": "js/app.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\wg78tukmqu-ds7d0gz88x.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1955"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YvLqQZ0OYpy8YzIIIRUm85p7Z+4LGH1g4sj/9YQm3Fw="}]}, {"Route": "js/auth.iyy8yegl4k.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\8c7jydm8ty-iyy8yegl4k.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000880281690"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyy8yegl4k"}, {"Name": "label", "Value": "js/auth.js"}, {"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}]}, {"Route": "js/auth.iyy8yegl4k.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\auth.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 15:25:05 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyy8yegl4k"}, {"Name": "label", "Value": "js/auth.js"}, {"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}]}, {"Route": "js/auth.iyy8yegl4k.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\8c7jydm8ty-iyy8yegl4k.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyy8yegl4k"}, {"Name": "label", "Value": "js/auth.js.gz"}, {"Name": "integrity", "Value": "sha256-X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc="}]}, {"Route": "js/auth.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\8c7jydm8ty-iyy8yegl4k.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000880281690"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}]}, {"Route": "js/auth.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\auth.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 15:25:05 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}]}, {"Route": "js/auth.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\8c7jydm8ty-iyy8yegl4k.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc="}]}, {"Route": "js/categories.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\vkpingxf2f-q4k5ude2ax.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000418235048"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}]}, {"Route": "js/categories.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\categories.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10343"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:53:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}]}, {"Route": "js/categories.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\vkpingxf2f-q4k5ude2ax.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM="}]}, {"Route": "js/categories.q4k5ude2ax.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\vkpingxf2f-q4k5ude2ax.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000418235048"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q4k5ude2ax"}, {"Name": "label", "Value": "js/categories.js"}, {"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}]}, {"Route": "js/categories.q4k5ude2ax.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\categories.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10343"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:53:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q4k5ude2ax"}, {"Name": "label", "Value": "js/categories.js"}, {"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}]}, {"Route": "js/categories.q4k5ude2ax.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\vkpingxf2f-q4k5ude2ax.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q4k5ude2ax"}, {"Name": "label", "Value": "js/categories.js.gz"}, {"Name": "integrity", "Value": "sha256-N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM="}]}, {"Route": "js/customers.hy3h9tabsi.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\ill7x5ozqf-hy3h9tabsi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000372439479"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy3h9tabsi"}, {"Name": "label", "Value": "js/customers.js"}, {"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}]}, {"Route": "js/customers.hy3h9tabsi.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\customers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14539"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:52:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy3h9tabsi"}, {"Name": "label", "Value": "js/customers.js"}, {"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}]}, {"Route": "js/customers.hy3h9tabsi.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\ill7x5ozqf-hy3h9tabsi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy3h9tabsi"}, {"Name": "label", "Value": "js/customers.js.gz"}, {"Name": "integrity", "Value": "sha256-sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4="}]}, {"Route": "js/customers.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\ill7x5ozqf-hy3h9tabsi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000372439479"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}]}, {"Route": "js/customers.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\customers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14539"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:52:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}]}, {"Route": "js/customers.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\ill7x5ozqf-hy3h9tabsi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4="}]}, {"Route": "js/invoices.7ene3dnx54.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\sif3j9439v-7ene3dnx54.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000203417413"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ene3dnx54"}, {"Name": "label", "Value": "js/invoices.js"}, {"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}]}, {"Route": "js/invoices.7ene3dnx54.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\invoices.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30213"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:27:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ene3dnx54"}, {"Name": "label", "Value": "js/invoices.js"}, {"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}]}, {"Route": "js/invoices.7ene3dnx54.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\sif3j9439v-7ene3dnx54.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ene3dnx54"}, {"Name": "label", "Value": "js/invoices.js.gz"}, {"Name": "integrity", "Value": "sha256-oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI="}]}, {"Route": "js/invoices.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\sif3j9439v-7ene3dnx54.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000203417413"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}]}, {"Route": "js/invoices.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\invoices.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30213"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:27:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}]}, {"Route": "js/invoices.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\sif3j9439v-7ene3dnx54.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI="}]}, {"Route": "js/pos.51r6od7olg.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\5lixpq0z40-51r6od7olg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000145011601"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6895"}, {"Name": "ETag", "Value": "\"uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "51r6od7olg"}, {"Name": "label", "Value": "js/pos.js"}, {"Name": "integrity", "Value": "sha256-p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME="}]}, {"Route": "js/pos.51r6od7olg.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\pos.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31619"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 19:02:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "51r6od7olg"}, {"Name": "label", "Value": "js/pos.js"}, {"Name": "integrity", "Value": "sha256-p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME="}]}, {"Route": "js/pos.51r6od7olg.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\5lixpq0z40-51r6od7olg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6895"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "51r6od7olg"}, {"Name": "label", "Value": "js/pos.js.gz"}, {"Name": "integrity", "Value": "sha256-uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY="}]}, {"Route": "js/pos.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\5lixpq0z40-51r6od7olg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000145011601"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6895"}, {"Name": "ETag", "Value": "\"uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME="}]}, {"Route": "js/pos.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\pos.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31619"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 19:02:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p1W1DXspaYfH5/8mBpGM4FpUrPfViUz8bdC9gzTg9ME="}]}, {"Route": "js/pos.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\5lixpq0z40-51r6od7olg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6895"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uUmgIMzjEoY/xKzO6j3WSsR2NGsjI9DxVjnPc9O51NY="}]}, {"Route": "js/products.9n0jr0b026.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\wa9tzd9v0l-9n0jr0b026.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000120700060"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0jr0b026"}, {"Name": "label", "Value": "js/products.js"}, {"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}]}, {"Route": "js/products.9n0jr0b026.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\products.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44477"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0jr0b026"}, {"Name": "label", "Value": "js/products.js"}, {"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}]}, {"Route": "js/products.9n0jr0b026.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\wa9tzd9v0l-9n0jr0b026.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0jr0b026"}, {"Name": "label", "Value": "js/products.js.gz"}, {"Name": "integrity", "Value": "sha256-cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU="}]}, {"Route": "js/products.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\wa9tzd9v0l-9n0jr0b026.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000120700060"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}]}, {"Route": "js/products.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\products.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44477"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}]}, {"Route": "js/products.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\wa9tzd9v0l-9n0jr0b026.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU="}]}, {"Route": "js/reports.99d2u4ky5k.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\x78a6k87cc-99d2u4ky5k.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000217770035"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4591"}, {"Name": "ETag", "Value": "\"sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "99d2u4ky5k"}, {"Name": "label", "Value": "js/reports.js"}, {"Name": "integrity", "Value": "sha256-nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI="}]}, {"Route": "js/reports.99d2u4ky5k.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\reports.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38742"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:33:52 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "99d2u4ky5k"}, {"Name": "label", "Value": "js/reports.js"}, {"Name": "integrity", "Value": "sha256-nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI="}]}, {"Route": "js/reports.99d2u4ky5k.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\x78a6k87cc-99d2u4ky5k.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4591"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "99d2u4ky5k"}, {"Name": "label", "Value": "js/reports.js.gz"}, {"Name": "integrity", "Value": "sha256-sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M="}]}, {"Route": "js/reports.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\x78a6k87cc-99d2u4ky5k.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000217770035"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4591"}, {"Name": "ETag", "Value": "\"sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI="}]}, {"Route": "js/reports.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\reports.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "38742"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:33:52 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nd1jfJ9A8YBYE9DTfDqecOqg/6cAc9ZahZyY1FVWTnI="}]}, {"Route": "js/reports.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\x78a6k87cc-99d2u4ky5k.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4591"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sxskJD3oDtn6sJKkuNvFl0tS2JGVsqx33wqxJfIDU/M="}]}, {"Route": "js/settings.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\kd4at48or8-x4y7n64zvw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083745080"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11940"}, {"Name": "ETag", "Value": "\"swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:52 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc="}]}, {"Route": "js/settings.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\settings.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81891"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc="}]}, {"Route": "js/settings.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\kd4at48or8-x4y7n64zvw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11940"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:52 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M="}]}, {"Route": "js/settings.x4y7n64zvw.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\kd4at48or8-x4y7n64zvw.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000083745080"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11940"}, {"Name": "ETag", "Value": "\"swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:52 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x4y7n64zvw"}, {"Name": "label", "Value": "js/settings.js"}, {"Name": "integrity", "Value": "sha256-kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc="}]}, {"Route": "js/settings.x4y7n64zvw.js", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\js\\settings.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "81891"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x4y7n64zvw"}, {"Name": "label", "Value": "js/settings.js"}, {"Name": "integrity", "Value": "sha256-kfeQaWcqrFhq5xhs4ZYWnQ1lPE1vI/VJzH/Y0m7jdlc="}]}, {"Route": "js/settings.x4y7n64zvw.js.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\kd4at48or8-x4y7n64zvw.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11940"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:52 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x4y7n64zvw"}, {"Name": "label", "Value": "js/settings.js.gz"}, {"Name": "integrity", "Value": "sha256-swT82FE4IHA6ZNZ/uuMHoXFXxjcq04ALbvlotVsZV0M="}]}, {"Route": "login.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\ziqm8tcidl-nqonojhrly.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000487329435"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}]}, {"Route": "login.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\login.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6732"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:38:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}]}, {"Route": "login.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\ziqm8tcidl-nqonojhrly.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ="}]}, {"Route": "login.nqonojhrly.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\ziqm8tcidl-nqonojhrly.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000487329435"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "login.html"}, {"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}]}, {"Route": "login.nqonojhrly.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\login.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6732"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:38:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "login.html"}, {"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}]}, {"Route": "login.nqonojhrly.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\ziqm8tcidl-nqonojhrly.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "login.html.gz"}, {"Name": "integrity", "Value": "sha256-701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ="}]}, {"Route": "print-invoice.2cv1skr50a.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\pv27i78e2o-2cv1skr50a.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000250752257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cv1skr50a"}, {"Name": "label", "Value": "print-invoice.html"}, {"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}]}, {"Route": "print-invoice.2cv1skr50a.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\print-invoice.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17980"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 19:09:19 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cv1skr50a"}, {"Name": "label", "Value": "print-invoice.html"}, {"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}]}, {"Route": "print-invoice.2cv1skr50a.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\pv27i78e2o-2cv1skr50a.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cv1skr50a"}, {"Name": "label", "Value": "print-invoice.html.gz"}, {"Name": "integrity", "Value": "sha256-bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE="}]}, {"Route": "print-invoice.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\pv27i78e2o-2cv1skr50a.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000250752257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}]}, {"Route": "print-invoice.html", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\wwwroot\\print-invoice.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17980"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 19:09:19 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}]}, {"Route": "print-invoice.html.gz", "AssetFile": "C:\\Users\\<USER>\\Desktop\\pos\\obj\\Release\\net9.0\\win-x64\\compressed\\pv27i78e2o-2cv1skr50a.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 16:54:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE="}]}]}