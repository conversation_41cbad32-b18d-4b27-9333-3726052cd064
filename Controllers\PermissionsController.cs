using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Aredoo.Server.Data;
using Aredoo.Server.Models;

namespace Aredoo.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PermissionsController : ControllerBase
{
    private readonly AredooDbContext _context;

    public PermissionsController(AredooDbContext context)
    {
        _context = context;
    }

    [HttpGet("user/{userId}")]
    public async Task<IActionResult> GetUserPermissions(int userId)
    {
        var permissions = await _context.Permissions
            .Where(p => p.UserId == userId)
            .ToListAsync();

        return Ok(permissions);
    }

    [HttpPost("user/{userId}")]
    public async Task<IActionResult> SaveUserPermissions(int userId, [FromBody] List<PermissionDto> permissionsDto)
    {
        try
        {
            // Delete existing permissions
            var existingPermissions = await _context.Permissions
                .Where(p => p.UserId == userId)
                .ToListAsync();

            _context.Permissions.RemoveRange(existingPermissions);

            // Add new permissions
            foreach (var dto in permissionsDto)
            {
                var permission = new Permission
                {
                    UserId = userId,
                    Module = dto.Module,
                    CanView = dto.CanView,
                    CanAdd = dto.CanAdd,
                    CanEdit = dto.CanEdit,
                    CanDelete = dto.CanDelete,
                    CanPrint = dto.CanPrint,
                    CanExport = dto.CanExport
                };
                _context.Permissions.Add(permission);
            }

            await _context.SaveChangesAsync();

            return Ok(new { message = "تم حفظ الصلاحيات بنجاح" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "فشل حفظ الصلاحيات", error = ex.Message });
        }
    }

    [HttpPost("create-default/{userId}")]
    public async Task<IActionResult> CreateDefaultPermissions(int userId, [FromQuery] string role)
    {
        // Delete existing permissions
        var existingPermissions = await _context.Permissions
            .Where(p => p.UserId == userId)
            .ToListAsync();
        
        _context.Permissions.RemoveRange(existingPermissions);

        // Create default permissions based on role
        var defaultPermissions = GetDefaultPermissionsByRole(role, userId);
        
        _context.Permissions.AddRange(defaultPermissions);
        await _context.SaveChangesAsync();

        return Ok(new { message = $"تم إنشاء صلاحيات {role} الافتراضية بنجاح", permissions = defaultPermissions });
    }

    private List<Permission> GetDefaultPermissionsByRole(string role, int userId)
    {
        var permissions = new List<Permission>();
        var modules = new[] { "Sales", "Products", "Categories", "Customers", "Suppliers", "Invoices", "Employees", "Reports", "Settings", "Purchases", "Transactions" };

        switch (role.ToLower())
        {
            case "admin":
                // Admin has full permissions
                foreach (var module in modules)
                {
                    permissions.Add(new Permission
                    {
                        UserId = userId,
                        Module = module,
                        CanView = true,
                        CanAdd = true,
                        CanEdit = true,
                        CanDelete = true,
                        CanPrint = true,
                        CanExport = true
                    });
                }
                break;

            case "manager":
                // Manager has most permissions except delete in critical modules
                foreach (var module in modules)
                {
                    bool canDelete = module != "Settings" && module != "Transactions";
                    permissions.Add(new Permission
                    {
                        UserId = userId,
                        Module = module,
                        CanView = true,
                        CanAdd = true,
                        CanEdit = true,
                        CanDelete = canDelete,
                        CanPrint = true,
                        CanExport = true
                    });
                }
                break;

            case "cashier":
                // Cashier has limited permissions for POS operations
                var cashierModules = new[] { "Sales", "Products", "Customers", "Reports" };
                foreach (var module in cashierModules)
                {
                    permissions.Add(new Permission
                    {
                        UserId = userId,
                        Module = module,
                        CanView = true,
                        CanAdd = module == "Sales" || module == "Customers",
                        CanEdit = false,
                        CanDelete = false,
                        CanPrint = module == "Sales",
                        CanExport = false
                    });
                }
                break;

            case "employee":
                // Employee has view-only permissions
                var employeeModules = new[] { "Products", "Customers", "Reports" };
                foreach (var module in employeeModules)
                {
                    permissions.Add(new Permission
                    {
                        UserId = userId,
                        Module = module,
                        CanView = true,
                        CanAdd = false,
                        CanEdit = false,
                        CanDelete = false,
                        CanPrint = false,
                        CanExport = false
                    });
                }
                break;
        }

        return permissions;
    }
}

