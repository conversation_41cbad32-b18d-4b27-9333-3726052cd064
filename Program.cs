using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using Aredoo.Server.Data;

var builder = WebApplication.CreateBuilder(args);

// Configure database
var dbPath = Path.Combine(Directory.GetCurrentDirectory(), "data", "aredoo.db");
Directory.CreateDirectory(Path.GetDirectoryName(dbPath)!);

builder.Services.AddDbContext<AredooDbContext>(options =>
    options.UseSqlite($"Data Source={dbPath}"));

// Add controllers with JSON options
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles;
        options.JsonSerializerOptions.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull;
    });

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Configure Kestrel to listen on localhost:5000
builder.WebHost.UseUrls("http://localhost:5000");

var app = builder.Build();

// Initialize database
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<AredooDbContext>();
    DbInitializer.Initialize(context);
}

// Configure middleware
app.UseCors();
app.UseDefaultFiles();
app.UseStaticFiles();
app.MapControllers();

// Redirect root to index.html
app.MapGet("/", () => Results.Redirect("/index.html"));

// Auto-open browser on startup
app.Lifetime.ApplicationStarted.Register(() =>
{
    try
    {
        var url = "http://localhost:5000";
        Process.Start(new ProcessStartInfo
        {
            FileName = url,
            UseShellExecute = true
        });
        Console.WriteLine($"✓ تم فتح المتصفح على {url}");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"تنبيه: لم يتم فتح المتصفح تلقائياً. افتح http://localhost:5000 يدوياً");
        Console.WriteLine($"الخطأ: {ex.Message}");
    }
});

Console.WriteLine("═══════════════════════════════════════════════════════");
Console.WriteLine("  أريدوو - Aredoo POS System");
Console.WriteLine("  نظام إدارة المبيعات والمخزون");
Console.WriteLine("═══════════════════════════════════════════════════════");
Console.WriteLine($"  🌐 العنوان: http://localhost:5000");
Console.WriteLine($"  📁 قاعدة البيانات: {dbPath}");
Console.WriteLine($"  👤 المستخدم الافتراضي: admin");
Console.WriteLine($"  🔑 كلمة المرور: 1234");
Console.WriteLine("═══════════════════════════════════════════════════════");
Console.WriteLine("  اضغط Ctrl+C للإيقاف");
Console.WriteLine("═══════════════════════════════════════════════════════");

app.Run();
