using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Aredoo.Server.Data;
using Aredoo.Server.Models;

namespace Aredoo.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PurchasesController : ControllerBase
{
    private readonly AredooDbContext _context;

    public PurchasesController(AredooDbContext context)
    {
        _context = context;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll(
        [FromQuery] int? supplierId = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.Purchases
            .Include(p => p.Supplier)
            .Include(p => p.Items)
            .Where(p => !p.IsDeleted)
            .AsQueryable();

        if (supplierId.HasValue)
            query = query.Where(p => p.SupplierId == supplierId.Value);

        if (fromDate.HasValue)
            query = query.Where(p => p.PurchaseDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(p => p.PurchaseDate <= toDate.Value);

        var purchases = await query
            .OrderByDescending(p => p.PurchaseDate)
            .ToListAsync();

        return Ok(purchases);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id)
    {
        var purchase = await _context.Purchases
            .Include(p => p.Supplier)
            .Include(p => p.Items)
            .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

        if (purchase == null)
            return NotFound();

        return Ok(purchase);
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] Purchase purchase)
    {
        // Generate invoice number if not provided
        if (string.IsNullOrEmpty(purchase.InvoiceNumber))
        {
            var lastPurchase = await _context.Purchases
                .OrderByDescending(p => p.Id)
                .FirstOrDefaultAsync();

            var nextNumber = (lastPurchase?.Id ?? 0) + 1;
            purchase.InvoiceNumber = $"PUR{nextNumber:D6}";
        }

        purchase.CreatedAt = DateTime.Now;
        purchase.IsDeleted = false;

        // Process items and update product quantities and purchase prices
        foreach (var item in purchase.Items)
        {
            var product = await _context.Products.FindAsync(item.ProductId);
            if (product != null)
            {
                // Store product name
                if (string.IsNullOrEmpty(item.ProductName))
                    item.ProductName = product.Name;

                // Update product quantity (add to stock)
                product.Quantity += (int)item.Quantity;

                // Update product purchase price
                product.PurchasePrice = item.UnitPrice;
            }
        }

        // Update supplier balance if payment is not full
        if (purchase.Remaining > 0)
        {
            var supplier = await _context.Suppliers.FindAsync(purchase.SupplierId);
            if (supplier != null)
            {
                supplier.Balance += purchase.Remaining;
            }
        }

        _context.Purchases.Add(purchase);
        await _context.SaveChangesAsync();

        return CreatedAtAction(nameof(GetById), new { id = purchase.Id }, purchase);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] Purchase purchase)
    {
        var existingPurchase = await _context.Purchases
            .Include(p => p.Items)
            .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

        if (existingPurchase == null)
            return NotFound();

        // Revert old quantities
        foreach (var oldItem in existingPurchase.Items)
        {
            var product = await _context.Products.FindAsync(oldItem.ProductId);
            if (product != null)
            {
                product.Quantity -= (int)oldItem.Quantity;
            }
        }

        // Remove old items
        _context.PurchaseItems.RemoveRange(existingPurchase.Items);

        // Update purchase details
        existingPurchase.PurchaseDate = purchase.PurchaseDate;
        existingPurchase.SupplierId = purchase.SupplierId;
        existingPurchase.SubTotal = purchase.SubTotal;
        existingPurchase.Discount = purchase.Discount;
        existingPurchase.Tax = purchase.Tax;
        existingPurchase.Total = purchase.Total;
        existingPurchase.Paid = purchase.Paid;
        existingPurchase.Remaining = purchase.Remaining;
        existingPurchase.PaymentType = purchase.PaymentType;
        existingPurchase.Notes = purchase.Notes;




        // Add new items and update quantities
        foreach (var item in purchase.Items)
        {
            var product = await _context.Products.FindAsync(item.ProductId);
            if (product != null)
            {
                if (string.IsNullOrEmpty(item.ProductName))
                    item.ProductName = product.Name;

                product.Quantity += (int)item.Quantity;
                product.PurchasePrice = item.UnitPrice;
            }

            existingPurchase.Items.Add(item);
        }

        await _context.SaveChangesAsync();
        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        var purchase = await _context.Purchases
            .Include(p => p.Items)
            .FirstOrDefaultAsync(p => p.Id == id);

        if (purchase == null)
            return NotFound();

        // Revert quantities
        foreach (var item in purchase.Items)
        {
            var product = await _context.Products.FindAsync(item.ProductId);
            if (product != null)
            {
                product.Quantity -= (int)item.Quantity;
            }
        }

        // Soft delete
        purchase.IsDeleted = true;
        await _context.SaveChangesAsync();

        return NoContent();
    }

    [HttpPost("init-tables")]
    public async Task<IActionResult> InitTables()
    {
        try
        {
            await _context.Database.ExecuteSqlRawAsync(@"
                CREATE TABLE IF NOT EXISTS Purchases (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    InvoiceNumber TEXT NOT NULL,
                    PurchaseDate TEXT NOT NULL,
                    SupplierId INTEGER NOT NULL,
                    SubTotal REAL NOT NULL,
                    Discount REAL NOT NULL DEFAULT 0,
                    Tax REAL NOT NULL DEFAULT 0,
                    Total REAL NOT NULL,
                    Paid REAL NOT NULL DEFAULT 0,
                    Remaining REAL NOT NULL DEFAULT 0,
                    PaymentType TEXT NOT NULL DEFAULT 'Cash',
                    Notes TEXT,
                    CreatedBy INTEGER NOT NULL,
                    CreatedAt TEXT NOT NULL,
                    IsDeleted INTEGER NOT NULL DEFAULT 0,
                    FOREIGN KEY (SupplierId) REFERENCES Suppliers(Id),
                    FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
                );

                CREATE TABLE IF NOT EXISTS PurchaseItems (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    PurchaseId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    ProductName TEXT NOT NULL,
                    Quantity REAL NOT NULL,
                    UnitPrice REAL NOT NULL,
                    Discount REAL NOT NULL DEFAULT 0,
                    Total REAL NOT NULL,
                    FOREIGN KEY (PurchaseId) REFERENCES Purchases(Id) ON DELETE CASCADE,
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                );

                CREATE INDEX IF NOT EXISTS IX_Purchases_PurchaseDate ON Purchases(PurchaseDate);
                CREATE INDEX IF NOT EXISTS IX_Purchases_SupplierId ON Purchases(SupplierId);
                CREATE INDEX IF NOT EXISTS IX_Purchases_IsDeleted ON Purchases(IsDeleted);
                CREATE INDEX IF NOT EXISTS IX_PurchaseItems_PurchaseId ON PurchaseItems(PurchaseId);
                CREATE INDEX IF NOT EXISTS IX_PurchaseItems_ProductId ON PurchaseItems(ProductId);
            ");

            return Ok(new { message = "تم إنشاء جداول المشتريات بنجاح" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "خطأ في إنشاء الجداول", error = ex.Message });
        }
    }
}
