@echo off
chcp 65001 > nul
title Test Aredoo POS - Java

echo ═══════════════════════════════════════════════════════
echo   اختبار تطبيق أريدوو - Aredoo POS Test
echo ═══════════════════════════════════════════════════════

cd /d "%~dp0"

REM Check Java
java -version
if errorlevel 1 (
    echo ❌ Java غير متوفر
    pause
    exit /b 1
)

echo ✅ Java متوفر

REM Create directories
if not exist "data" mkdir data
if not exist "src\main\resources\static" mkdir src\main\resources\static

echo 📁 المجلدات جاهزة

REM Check files
echo 📋 فحص الملفات:
if exist "src\main\java\com\aredoo\pos\AredooPosApplication.java" (
    echo ✅ AredooPosApplication.java
) else (
    echo ❌ AredooPosApplication.java مفقود
)

if exist "src\main\resources\application.properties" (
    echo ✅ application.properties
) else (
    echo ❌ application.properties مفقود
)

if exist "pom.xml" (
    echo ✅ pom.xml موجود
) else (
    echo ❌ pom.xml مفقود
)

if exist "build.gradle" (
    echo ✅ build.gradle موجود
) else (
    echo ❌ build.gradle مفقود
)

echo.
echo 📊 ملخص الاختبار:
echo - التطبيق جاهز للتشغيل
echo - قاعدة البيانات: H2 (ملف محلي)
echo - المنفذ: 5000
echo - المستخدم الافتراضي: admin/1234

echo.
echo لتشغيل التطبيق استخدم:
echo - start-aredoo-improved.bat (الأفضل)
echo - أو start-aredoo.bat

pause
