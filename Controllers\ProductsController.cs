using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Aredoo.Server.Data;
using Aredoo.Server.Models;

namespace Aredoo.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ProductsController : ControllerBase
{
    private readonly AredooDbContext _context;

    public ProductsController(AredooDbContext context)
    {
        _context = context;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll([FromQuery] bool? activeOnly = true)
    {
        var query = _context.Products.AsQueryable();
        
        if (activeOnly == true)
            query = query.Where(p => p.IsActive);

        var products = await query.OrderBy(p => p.Name).ToListAsync();
        return Ok(products);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id)
    {
        var product = await _context.Products.FindAsync(id);
        if (product == null)
            return NotFound();

        return Ok(product);
    }

    [HttpGet("search")]
    public async Task<IActionResult> Search([FromQuery] string term)
    {
        var products = await _context.Products
            .Where(p => p.IsActive && (
                p.Name.Contains(term) ||
                p.NameAr.Contains(term) ||
                p.Code.Contains(term) ||
                (p.Barcode != null && p.Barcode.Contains(term))
            ))
            .Take(20)
            .ToListAsync();

        return Ok(products);
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] Product product)
    {
        try
        {
            product.CreatedAt = DateTime.Now;
            _context.Products.Add(product);
            await _context.SaveChangesAsync();

            // نرجع استجابة بسيطة مع رسالة نجاح ليستخدمها الواجهة الأمامية
            return Ok(new
            {
                id = product.Id,
                message = "تم إضافة المنتج بنجاح"
            });
        }
        catch (DbUpdateException ex)
        {
            // أخطاء قاعدة البيانات (مثل مشكلة في القيم، قفل القاعدة، إلخ)
            var errorMessage = ex.InnerException?.Message ?? ex.Message;
            Console.WriteLine($"خطأ في حفظ المنتج: {errorMessage}");
            return BadRequest(new { message = $"خطأ في حفظ المنتج في قاعدة البيانات: {errorMessage}" });
        }
        catch (Exception ex)
        {
            // أي خطأ غير متوقع آخر
            Console.WriteLine($"خطأ غير متوقع في حفظ المنتج: {ex}");
            return StatusCode(500, new { message = $"حدث خطأ غير متوقع أثناء حفظ المنتج: {ex.Message}" });
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] Product product)
    {
        if (id != product.Id)
            return BadRequest();

        product.UpdatedAt = DateTime.Now;
        _context.Entry(product).State = EntityState.Modified;

        try
        {
            await _context.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!await _context.Products.AnyAsync(p => p.Id == id))
                return NotFound();
            throw;
        }

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        var product = await _context.Products.FindAsync(id);
        if (product == null)
            return NotFound();

        product.IsActive = false;
        await _context.SaveChangesAsync();

        return NoContent();
    }

    [HttpGet("low-stock")]
    public async Task<IActionResult> GetLowStock()
    {
        var products = await _context.Products
            .Where(p => p.IsActive && p.Quantity <= p.MinQuantity)
            .OrderBy(p => p.Quantity)
            .ToListAsync();

        return Ok(products);
    }

    [HttpGet("negative-stock")]
    public async Task<IActionResult> GetNegativeStock()
    {
        var products = await _context.Products
            .Where(p => p.IsActive && p.Quantity < 0)
            .OrderBy(p => p.Quantity)
            .ToListAsync();

        return Ok(products);
    }
}

