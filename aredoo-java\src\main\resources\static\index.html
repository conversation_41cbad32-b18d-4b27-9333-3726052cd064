<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أريدوو - Aredoo POS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css?v=62" rel="stylesheet">
</head>
<body class="app-body">
    <div id="mainApp">
        <nav class="navbar navbar-expand-lg navbar-dark app-navbar shadow-sm">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-shop"></i> أريدوو
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item" id="menu-sales">
                            <a class="nav-link" href="#" data-page="pos" onclick="showPage('pos')">
                                <i class="bi bi-cart-plus"></i> نقطة البيع
                            </a>
                        </li>
                        <li class="nav-item" id="menu-products">
                            <a class="nav-link" href="#" data-page="products" onclick="showPage('products')">
                                <i class="bi bi-box-seam"></i> المنتجات
                            </a>
                        </li>
                        <li class="nav-item" id="menu-categories">
                            <a class="nav-link" href="#" data-page="categories" onclick="showPage('categories')">
                                <i class="bi bi-grid-3x3-gap"></i> الفئات
                            </a>
                        </li>
                        <li class="nav-item" id="menu-customers">
                            <a class="nav-link" href="#" data-page="customers" onclick="showPage('customers')">
                                <i class="bi bi-people"></i> العملاء
                            </a>
                        </li>
                        <li class="nav-item" id="menu-invoices">
                            <a class="nav-link" href="#" data-page="invoices" onclick="showPage('invoices')">
                                <i class="bi bi-receipt"></i> الفواتير
                            </a>
                        </li>
                        <li class="nav-item" id="menu-reports">
                            <a class="nav-link" href="#" data-page="reports" onclick="showPage('reports')">
                                <i class="bi bi-graph-up"></i> التقارير
                            </a>
                        </li>
                        <li class="nav-item" id="menu-expenses">
                            <a class="nav-link" href="#" data-page="expenses" onclick="showPage('expenses')">
                                <i class="bi bi-cash-stack"></i> المصاريف
                            </a>
                        </li>
                        <li class="nav-item" id="menu-suppliers">
                            <a class="nav-link" href="#" data-page="suppliers" onclick="showPage('suppliers')">
                                <i class="bi bi-truck"></i> الموردين
                            </a>
                        </li>
                        <li class="nav-item" id="menu-purchases">
                            <a class="nav-link" href="#" data-page="purchases" onclick="showPage('purchases')">
                                <i class="bi bi-cart-plus"></i> المشتريات
                            </a>
                        </li>
                        <li class="nav-item" id="menu-settings">
                            <a class="nav-link" href="#" data-page="settings" onclick="showPage('settings')">
                                <i class="bi bi-gear"></i> الإعدادات
                            </a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i> <span id="currentUserName">المستخدم</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item disabled"><i class="bi bi-shield-check"></i> <span id="currentUserRole">الدور</span></a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="javascript:void(0);" onclick="logout(); return false;"><i class="bi bi-box-arrow-left"></i> تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <div class="container-fluid mt-3 app-container">
            <div id="posPage" class="page-content d-none"></div>
            <div id="productsPage" class="page-content d-none"></div>
            <div id="categoriesPage" class="page-content d-none"></div>
            <div id="customersPage" class="page-content d-none"></div>
            <div id="invoicesPage" class="page-content d-none"></div>
            <div id="reportsPage" class="page-content d-none"></div>
            <div id="expensesPage" class="page-content d-none"></div>
            <div id="suppliersPage" class="page-content d-none"></div>
            <div id="purchasesPage" class="page-content d-none"></div>
            <div id="settingsPage" class="page-content d-none"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
    <script src="js/auth.js?v=50"></script>
    <script src="js/app.js?v=50"></script>
    <script src="js/pos.js?v=55"></script>
    <script src="js/products.js?v=49"></script>
    <script src="js/categories.js?v=49"></script>
    <script src="js/customers.js?v=49"></script>
    <script src="js/invoices.js?v=49"></script>
    <script src="js/reports.js?v=49"></script>
    <script src="js/expenses.js?v=49"></script>
    <script src="js/suppliers.js?v=49"></script>
    <script src="js/purchases.js?v=49"></script>
    <script src="js/settings.js?v=49"></script>
</body>
</html>

