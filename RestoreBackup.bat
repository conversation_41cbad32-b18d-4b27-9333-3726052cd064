@echo off
chcp 65001 >nul
title نظام استعادة النسخ الاحتياطية - أريدوو

echo.
echo ===============================================
echo    نظام استعادة النسخ الاحتياطية لأريدوو
echo    Aredoo POS System - Restore Tool
echo ===============================================
echo.

REM التحقق من وجود PowerShell
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo ❌ PowerShell غير متوفر على هذا النظام
    echo يرجى تثبيت PowerShell أو استخدام Windows 10/11
    pause
    exit /b 1
)

REM تشغيل سكريبت PowerShell
echo جاري تشغيل نظام استعادة النسخ الاحتياطية...
echo.

powershell -ExecutionPolicy Bypass -File "RestoreBackup.ps1"

if errorlevel 1 (
    echo.
    echo ❌ فشل في استعادة النسخة الاحتياطية
    pause
    exit /b 1
)

echo.
echo ✅ تم الانتهاء بنجاح!
pause
