using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Aredoo.Server.Data;
using Aredoo.Server.Models;

namespace Aredoo.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SuppliersController : ControllerBase
{
    private readonly AredooDbContext _context;

    public SuppliersController(AredooDbContext context)
    {
        _context = context;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll([FromQuery] bool? isActive = null)
    {
        var query = _context.Suppliers.AsQueryable();

        if (isActive.HasValue)
            query = query.Where(s => s.IsActive == isActive.Value);

        var suppliers = await query
            .OrderBy(s => s.Name)
            .ToListAsync();

        return Ok(suppliers);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id)
    {
        var supplier = await _context.Suppliers.FindAsync(id);
        if (supplier == null)
            return NotFound();

        return Ok(supplier);
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] Supplier supplier)
    {
        // Generate code if not provided
        if (string.IsNullOrEmpty(supplier.Code))
        {
            var lastSupplier = await _context.Suppliers
                .OrderByDescending(s => s.Id)
                .FirstOrDefaultAsync();
            
            var nextNumber = (lastSupplier?.Id ?? 0) + 1;
            supplier.Code = $"SUP{nextNumber:D4}";
        }

        supplier.CreatedAt = DateTime.Now;
        _context.Suppliers.Add(supplier);
        await _context.SaveChangesAsync();

        return CreatedAtAction(nameof(GetById), new { id = supplier.Id }, supplier);
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] Supplier supplier)
    {
        var existingSupplier = await _context.Suppliers.FindAsync(id);
        if (existingSupplier == null)
            return NotFound();

        existingSupplier.Name = supplier.Name;
        existingSupplier.Phone = supplier.Phone;
        existingSupplier.Address = supplier.Address;
        existingSupplier.Email = supplier.Email;
        existingSupplier.Notes = supplier.Notes;
        existingSupplier.IsActive = supplier.IsActive;

        await _context.SaveChangesAsync();
        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        var supplier = await _context.Suppliers.FindAsync(id);
        if (supplier == null)
            return NotFound();

        // Soft delete by setting IsActive to false
        supplier.IsActive = false;
        await _context.SaveChangesAsync();

        return NoContent();
    }

    [HttpGet("search")]
    public async Task<IActionResult> Search([FromQuery] string query)
    {
        if (string.IsNullOrWhiteSpace(query))
            return Ok(new List<Supplier>());

        var suppliers = await _context.Suppliers
            .Where(s => s.IsActive && 
                   (s.Name.Contains(query) || 
                    s.Code.Contains(query) || 
                    (s.Phone != null && s.Phone.Contains(query))))
            .Take(10)
            .ToListAsync();

        return Ok(suppliers);
    }
}

