<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة مبيعات - أريدوو</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;800&family=Tajawal:wght@400;500;700&family=Almarai:wght@400;700;800&display=swap" rel="stylesheet">
    <style>
        @media print {
            @page { size: 80mm auto; margin: 5mm; }
            body { margin: 0; padding: 0; }
            .no-print { display: none !important; }
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            max-width: 300px;
            margin: 0 auto;
            padding: 8px;
            font-size: 12px;
            background: #f8f9fa;
            color: #2c3e50;
        }

        .invoice-container {
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 10px;
        }

        .logo {
            width: 50px;
            height: 50px;
            margin: 0 auto 8px;
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .company-name {
            font-size: 18px;
            font-weight: 800;
            color: white;
            margin: 5px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .company-info {
            font-size: 11px;
            color: rgba(255,255,255,0.95);
            margin: 3px 0;
        }

        .invoice-title {
            text-align: center;
            background: #2c3e50;
            color: white;
            padding: 8px;
            font-size: 14px;
            font-weight: 700;
        }

        .invoice-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            padding: 12px;
            background: #f8f9fa;
            border-bottom: 2px dashed #dee2e6;
        }

        .detail-item {
            font-size: 11px;
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-weight: 700;
            color: #6c757d;
            font-size: 10px;
            margin-bottom: 2px;
        }

        .detail-value {
            color: #2c3e50;
            font-weight: 600;
            font-size: 12px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            font-size: 11px;
        }

        thead {
            background: #2c3e50;
            color: white;
        }

        th {
            padding: 8px 4px;
            font-size: 11px;
            font-weight: 700;
            text-align: center;
            border-bottom: 2px solid #495057;
        }

        td {
            padding: 6px 4px;
            font-size: 11px;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
        }

        tbody tr:nth-child(even) {
            background: #f8f9fa;
        }

        tbody tr:last-child td {
            border-bottom: 2px solid #dee2e6;
        }

        .summary {
            padding: 10px 12px;
            background: #fff;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            font-size: 12px;
            border-bottom: 1px dashed #e9ecef;
        }

        .summary-row:last-child {
            border-bottom: none;
        }

        .summary-label {
            font-weight: 600;
            color: #6c757d;
        }

        .summary-value {
            font-weight: 700;
            color: #2c3e50;
        }

        .total-row {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 12px;
            margin: 0;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .total-row .summary-label,
        .total-row .summary-value {
            color: white;
            font-size: 14px;
            font-weight: 800;
        }

        .remaining-row {
            padding: 8px 12px;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
        }

        .remaining-positive {
            color: #28a745;
            font-weight: 800;
        }

        .remaining-negative {
            color: #dc3545;
            font-weight: 800;
        }

        .footer {
            text-align: center;
            padding: 12px;
            background: #f8f9fa;
            border-top: 2px dashed #dee2e6;
        }

        .thank-you {
            font-size: 13px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 8px;
        }

        .footer-note {
            font-size: 10px;
            color: #6c757d;
            margin: 3px 0;
        }

        .divider {
            border: none;
            border-top: 1px dashed #dee2e6;
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🛒</div>
            <div class="company-name" id="companyName">أريدوو - Aredoo</div>
            <div class="company-info" id="companyAddress">العنوان</div>
            <div class="company-info" id="companyPhone">📞 رقم الهاتف</div>
        </div>

        <!-- Invoice Title -->
        <div class="invoice-title">فاتورة مبيعات</div>

        <!-- Invoice Details -->
        <div class="invoice-details">
            <div class="detail-item">
                <span class="detail-label">رقم الفاتورة</span>
                <span class="detail-value" id="invoiceNumber">-</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">التاريخ</span>
                <span class="detail-value" id="invoiceDate">-</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">العميل</span>
                <span class="detail-value" id="customerName">-</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">نوع الدفع</span>
                <span class="detail-value" id="paymentType">-</span>
            </div>
        </div>

        <!-- Products Table -->
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody id="itemsTable">
                <!-- سيتم ملؤها ديناميكياً -->
            </tbody>
        </table>

        <!-- Summary -->
        <div class="summary">
            <div class="summary-row">
                <span class="summary-label">المجموع الفرعي:</span>
                <span class="summary-value" id="subTotal">0 د.ع</span>
            </div>
            <div class="summary-row">
                <span class="summary-label">الخصم:</span>
                <span class="summary-value" id="discount">0 د.ع</span>
            </div>
        </div>

        <!-- Total -->
        <div class="total-row">
            <span class="summary-label">الإجمالي النهائي:</span>
            <span class="summary-value" id="total">0 د.ع</span>
        </div>

        <!-- Remaining -->
        <div class="remaining-row">
            <span class="summary-label">المبلغ المستلم:</span>
            <span class="summary-value" id="received">0 د.ع</span>
        </div>
        <div class="remaining-row">
            <span class="summary-label">الباقي:</span>
            <span class="summary-value" id="remaining">0 د.ع</span>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="thank-you">✨ شكراً لتعاملكم معنا ✨</div>
            <hr class="divider">
            <div class="footer-note" id="printTime">-</div>
            <div class="footer-note">نظام أريدوو - Aredoo POS System</div>
        </div>
    </div>

    <script>
        function formatCurrency(amount) {
            if (amount === null || amount === undefined || isNaN(amount)) {
                return '0 د.ع';
            }
            const formatted = new Intl.NumberFormat('ar-IQ', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
            return formatted + ' د.ع';
        }

        function applyPrintSettings(invoiceData) {
            // تحديد عرض الورق
            const paperWidth = invoiceData.paperWidth || '80';
            if (paperWidth === '58') {
                document.body.style.maxWidth = '220px';
            } else if (paperWidth === 'A4') {
                document.body.style.maxWidth = '210mm';
            } else {
                document.body.style.maxWidth = '300px';
            }

            // تحديد حجم الخط
            const fontSize = invoiceData.fontSize || 'medium';
            if (fontSize === 'small') {
                document.body.style.fontSize = '10px';
            } else if (fontSize === 'large') {
                document.body.style.fontSize = '14px';
            } else if (fontSize === 'xlarge') {
                document.body.style.fontSize = '16px';
            } else {
                document.body.style.fontSize = '12px';
            }

            // تطبيق نمط الخط
            const fontFamily = invoiceData.fontFamily || 'Cairo';
            document.body.style.fontFamily = `'${fontFamily}', 'Segoe UI', 'Tahoma', sans-serif`;

            // تطبيق لون الرأس
            const headerColor = invoiceData.headerColor || '#667eea';
            const headerEl = document.querySelector('.header');
            if (headerEl) {
                headerEl.style.background = `linear-gradient(135deg, ${headerColor} 0%, #764ba2 100%)`;
            }

            // تطبيق الهوامش
            const marginTop = invoiceData.marginTop || '5';
            const marginBottom = invoiceData.marginBottom || '5';
            const marginLeft = invoiceData.marginLeft || '5';
            const marginRight = invoiceData.marginRight || '5';

            const styleEl = document.createElement('style');
            styleEl.textContent = `
                @media print {
                    @page {
                        margin: ${marginTop}mm ${marginRight}mm ${marginBottom}mm ${marginLeft}mm;
                    }
                }
            `;
            document.head.appendChild(styleEl);
        }

        // استلام البيانات من URL
        const urlParams = new URLSearchParams(window.location.search);
        const data = urlParams.get('data');

        if (data) {
            try {
                const invoiceData = JSON.parse(decodeURIComponent(data));

                // تطبيق الإعدادات الديناميكية
                applyPrintSettings(invoiceData);

                // تعبئة البيانات
                document.getElementById('companyName').textContent = invoiceData.companyName || 'أريدوو - Aredoo';
                document.getElementById('companyAddress').textContent = invoiceData.companyAddress || 'العنوان';
                document.getElementById('companyPhone').textContent = '📞 ' + (invoiceData.companyPhone || 'رقم الهاتف');

                // إخفاء/إظهار العناصر
                const logoEl = document.querySelector('.logo');
                if (logoEl && invoiceData.printLogo === false) {
                    logoEl.style.display = 'none';
                }

                const headerEl = document.querySelector('.header');
                if (headerEl && invoiceData.printHeader === false) {
                    headerEl.style.display = 'none';
                }

                const footerEl = document.querySelector('.footer');
                if (footerEl && invoiceData.printFooter === false) {
                    footerEl.style.display = 'none';
                }

                // معلومات الفاتورة
                document.getElementById('invoiceNumber').textContent = invoiceData.invoiceNumber || '-';

                // تنسيق التاريخ
                let dateText = '-';
                if (invoiceData.date) {
                    try {
                        const dateObj = new Date(invoiceData.date);
                        if (!isNaN(dateObj.getTime())) {
                            dateText = dateObj.toLocaleDateString('ar-IQ', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit'
                            });
                        } else {
                            dateText = invoiceData.date;
                        }
                    } catch (e) {
                        dateText = invoiceData.date;
                    }
                }
                document.getElementById('invoiceDate').textContent = dateText;

                document.getElementById('customerName').textContent = invoiceData.customerName || 'عميل نقدي';

                const paymentTypes = {
                    'Cash': 'نقدي',
                    'Credit': 'آجل',
                    'Wholesale': 'جملة',
                    'Installment': 'قسط'
                };
                document.getElementById('paymentType').textContent = paymentTypes[invoiceData.paymentType] || 'نقدي';

                // المنتجات
                const itemsTable = document.getElementById('itemsTable');
                itemsTable.innerHTML = '';
                if (invoiceData.items && invoiceData.items.length > 0) {
                    invoiceData.items.forEach((item, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td style="text-align: center;">${index + 1}</td>
                            <td style="font-weight: 600; text-align: right;">${item.name || ''}</td>
                            <td style="text-align: center;">${item.quantity || 0}</td>
                            <td style="text-align: right;">${formatCurrency(item.price)}</td>
                            <td style="text-align: right; font-weight: 700;">${formatCurrency(item.total)}</td>
                        `;
                        itemsTable.appendChild(row);
                    });
                }

                // المجاميع
                document.getElementById('subTotal').textContent = formatCurrency(invoiceData.subTotal || 0);
                document.getElementById('discount').textContent = formatCurrency(invoiceData.discount || 0);
                document.getElementById('total').textContent = formatCurrency(invoiceData.total || 0);
                document.getElementById('received').textContent = formatCurrency(invoiceData.received || 0);

                // الباقي
                const remaining = (invoiceData.received || 0) - (invoiceData.total || 0);
                const remainingEl = document.getElementById('remaining');
                remainingEl.textContent = formatCurrency(Math.abs(remaining));

                if (remaining > 0) {
                    remainingEl.classList.add('remaining-positive');
                    remainingEl.classList.remove('remaining-negative');
                } else if (remaining < 0) {
                    remainingEl.classList.add('remaining-negative');
                    remainingEl.classList.remove('remaining-positive');
                }

                // وقت الطباعة
                const now = new Date();
                const printTime = now.toLocaleString('ar-IQ', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                document.getElementById('printTime').textContent = 'تم الطباعة: ' + printTime;

                // طباعة تلقائية
                setTimeout(() => window.print(), 500);

            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                alert('خطأ في تحميل بيانات الفاتورة!');
            }
        } else {
            alert('لا توجد بيانات للفاتورة!');
        }
    </script>
</body>
</html>
