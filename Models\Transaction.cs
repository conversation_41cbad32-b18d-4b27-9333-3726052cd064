namespace Aredoo.Server.Models;

public class Transaction
{
    public int Id { get; set; }
    public string Type { get; set; } = "Receipt"; // Receipt, Payment, Allowance, Expense
    public string Number { get; set; } = string.Empty;
    public DateTime Date { get; set; } = DateTime.Now;
    public decimal Amount { get; set; }
    public string? FromTo { get; set; }
    public int? CustomerId { get; set; }
    public int? SupplierId { get; set; }
    public int? InvoiceId { get; set; }
    public string? Description { get; set; }
    public int CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.Now;
}

