package com.aredoo.pos.controller;

import com.aredoo.pos.model.Invoice;
import com.aredoo.pos.model.InvoiceItem;
import com.aredoo.pos.model.Product;
import com.aredoo.pos.repository.InvoiceRepository;
import com.aredoo.pos.repository.ProductRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/invoices")
@CrossOrigin(origins = "*")
public class InvoiceController {

    @Autowired
    private InvoiceRepository invoiceRepository;
    
    @Autowired
    private ProductRepository productRepository;

    @GetMapping
    public ResponseEntity<List<Invoice>> getAllInvoices(@RequestParam(defaultValue = "false") Boolean includeDeleted) {
        List<Invoice> invoices;
        if (includeDeleted) {
            invoices = invoiceRepository.findAll();
        } else {
            invoices = invoiceRepository.findByIsDeletedFalseOrderByCreatedAtDesc();
        }
        return ResponseEntity.ok(invoices);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Invoice> getInvoiceById(@PathVariable Long id) {
        Optional<Invoice> invoice = invoiceRepository.findById(id);
        return invoice.map(ResponseEntity::ok)
                     .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/number/{invoiceNumber}")
    public ResponseEntity<Invoice> getInvoiceByNumber(@PathVariable String invoiceNumber) {
        Optional<Invoice> invoice = invoiceRepository.findByInvoiceNumber(invoiceNumber);
        return invoice.map(ResponseEntity::ok)
                     .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/type/{type}")
    public ResponseEntity<List<Invoice>> getInvoicesByType(@PathVariable String type) {
        List<Invoice> invoices = invoiceRepository.findByTypeAndIsDeletedFalse(type);
        return ResponseEntity.ok(invoices);
    }

    @GetMapping("/customer/{customerId}")
    public ResponseEntity<List<Invoice>> getInvoicesByCustomer(@PathVariable Long customerId) {
        List<Invoice> invoices = invoiceRepository.findByCustomerId(customerId);
        return ResponseEntity.ok(invoices);
    }

    @GetMapping("/supplier/{supplierId}")
    public ResponseEntity<List<Invoice>> getInvoicesBySupplier(@PathVariable Long supplierId) {
        List<Invoice> invoices = invoiceRepository.findBySupplierId(supplierId);
        return ResponseEntity.ok(invoices);
    }

    @GetMapping("/unpaid")
    public ResponseEntity<List<Invoice>> getUnpaidInvoices() {
        List<Invoice> invoices = invoiceRepository.findUnpaidInvoices();
        return ResponseEntity.ok(invoices);
    }

    @GetMapping("/search")
    public ResponseEntity<List<Invoice>> searchInvoices(@RequestParam String term) {
        List<Invoice> invoices = invoiceRepository.searchInvoices(term);
        return ResponseEntity.ok(invoices);
    }

    @PostMapping
    public ResponseEntity<Invoice> createInvoice(@RequestBody Invoice invoice) {
        try {
            // Generate invoice number if not provided
            if (invoice.getInvoiceNumber() == null || invoice.getInvoiceNumber().isEmpty()) {
                String invoiceNumber = generateInvoiceNumber(invoice.getType());
                invoice.setInvoiceNumber(invoiceNumber);
            }
            
            invoice.setCreatedAt(LocalDateTime.now());
            
            // Calculate totals
            calculateInvoiceTotals(invoice);
            
            // Update product quantities for sales
            if ("Sale".equals(invoice.getType())) {
                updateProductQuantitiesForSale(invoice);
            }
            
            Invoice savedInvoice = invoiceRepository.save(invoice);
            return ResponseEntity.ok(savedInvoice);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<Invoice> updateInvoice(@PathVariable Long id, @RequestBody Invoice invoiceDetails) {
        Optional<Invoice> optionalInvoice = invoiceRepository.findById(id);
        if (optionalInvoice.isPresent()) {
            Invoice invoice = optionalInvoice.get();
            
            // Update invoice details
            invoice.setType(invoiceDetails.getType());
            invoice.setPaymentType(invoiceDetails.getPaymentType());
            invoice.setInvoiceDate(invoiceDetails.getInvoiceDate());
            invoice.setCustomer(invoiceDetails.getCustomer());
            invoice.setSupplier(invoiceDetails.getSupplier());
            invoice.setSalesRepId(invoiceDetails.getSalesRepId());
            invoice.setDiscount(invoiceDetails.getDiscount());
            invoice.setTax(invoiceDetails.getTax());
            invoice.setPaid(invoiceDetails.getPaid());
            invoice.setCurrency(invoiceDetails.getCurrency());
            invoice.setNotes(invoiceDetails.getNotes());
            invoice.setItems(invoiceDetails.getItems());
            
            // Recalculate totals
            calculateInvoiceTotals(invoice);
            
            Invoice updatedInvoice = invoiceRepository.save(invoice);
            return ResponseEntity.ok(updatedInvoice);
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteInvoice(@PathVariable Long id) {
        Optional<Invoice> invoice = invoiceRepository.findById(id);
        if (invoice.isPresent()) {
            Invoice i = invoice.get();
            i.setIsDeleted(true);
            invoiceRepository.save(i);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }

    private String generateInvoiceNumber(String type) {
        String prefix = switch (type) {
            case "Sale" -> "INV";
            case "Purchase" -> "PUR";
            case "SaleReturn" -> "SRN";
            case "PurchaseReturn" -> "PRN";
            default -> "INV";
        };
        
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return prefix + "-" + timestamp;
    }

    private void calculateInvoiceTotals(Invoice invoice) {
        BigDecimal subTotal = BigDecimal.ZERO;
        
        for (InvoiceItem item : invoice.getItems()) {
            BigDecimal itemTotal = item.getQuantity().multiply(item.getUnitPrice());
            itemTotal = itemTotal.subtract(item.getDiscount());
            item.setTotal(itemTotal);
            subTotal = subTotal.add(itemTotal);
        }
        
        invoice.setSubTotal(subTotal);
        BigDecimal total = subTotal.subtract(invoice.getDiscount()).add(invoice.getTax());
        invoice.setTotal(total);
        invoice.setRemaining(total.subtract(invoice.getPaid()));
    }

    private void updateProductQuantitiesForSale(Invoice invoice) {
        for (InvoiceItem item : invoice.getItems()) {
            if (item.getProduct() != null) {
                Product product = item.getProduct();
                int newQuantity = product.getQuantity() - item.getQuantity().intValue();
                product.setQuantity(Math.max(0, newQuantity));
                productRepository.save(product);
            }
        }
    }
}
