<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - Aredoo POS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        .login-header i {
            font-size: 60px;
            margin-bottom: 15px;
        }
        .login-body {
            padding: 40px 30px;
        }
        .form-control {
            border-radius: 10px;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            transition: all 0.3s;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 18px;
            font-weight: bold;
            color: white;
            width: 100%;
            transition: transform 0.2s;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e0e0e0;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        .form-control {
            border-left: none;
            border-radius: 10px 0 0 10px;
        }
        .login-footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #666;
            font-size: 14px;
        }
        .alert {
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <i class="bi bi-shop"></i>
            <h2 class="mb-0">أريدوو - Aredoo</h2>
            <p class="mb-0 mt-2">نظام نقاط البيع</p>
        </div>
        <div class="login-body">
            <form id="loginForm">
                <div class="mb-3">
                    <label class="form-label">اسم المستخدم</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="username" required autofocus>
                        <span class="input-group-text"><i class="bi bi-person"></i></span>
                    </div>
                </div>
                <div class="mb-4">
                    <label class="form-label">كلمة المرور</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="password" required>
                        <span class="input-group-text"><i class="bi bi-lock"></i></span>
                    </div>
                </div>
                <div id="errorMessage" class="alert alert-danger d-none" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <span id="errorText"></span>
                </div>
                <button type="submit" class="btn btn-login">
                    <i class="bi bi-box-arrow-in-left"></i> تسجيل الدخول
                </button>
            </form>
        </div>
        <div class="login-footer">
            <i class="bi bi-info-circle"></i> نظام نقاط البيع المتكامل
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                if (response.ok) {
                    const data = await response.json();
                    // Save user data to localStorage
                    localStorage.setItem('currentUser', JSON.stringify(data));

                    // Show message when a new work shift is started for today
                    if (data.isNewShift && data.shift) {
                        try {
                            const startTime = data.shift.startTime ? new Date(data.shift.startTime).toLocaleTimeString('ar-IQ') : '';
                            alert(startTime
                                ? `تم بدء فترة عمل جديدة لهذا اليوم في ${startTime}`
                                : 'تم بدء فترة عمل جديدة لهذا اليوم');
                        } catch {
                            alert('تم بدء فترة عمل جديدة لهذا اليوم');
                        }
                    }

                    // Redirect to main page
                    window.location.href = '/';
                } else {
                    const error = await response.json();
                    errorText.textContent = error.message || 'فشل تسجيل الدخول';
                    errorDiv.classList.remove('d-none');
                }
            } catch (error) {
                errorText.textContent = 'حدث خطأ في الاتصال بالخادم';
                errorDiv.classList.remove('d-none');
            }
        });
    </script>
</body>
</html>

