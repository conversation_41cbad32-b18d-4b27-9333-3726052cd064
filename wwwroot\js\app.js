// Global state
let currentUser = null;

// API base URL
const API_URL = '/api';

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        showMainApp();
    }

    // Login form handler
    document.getElementById('loginForm')?.addEventListener('submit', handleLogin);
});

// Login handler
async function handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
        const response = await fetch(`${API_URL}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password })
        });

        if (response.ok) {
            const user = await response.json();
            currentUser = user;
            localStorage.setItem('currentUser', JSON.stringify(user));

            // Show a toast when a new work shift is started for today
            if (user.isNewShift) {
                showToast('تم بدء فترة عمل جديدة لهذا اليوم', 'success');
            }

            showMainApp();
        } else {
            const error = await response.json();
            showError(error.message || 'فشل تسجيل الدخول');
        }
    } catch (error) {
        showError('حدث خطأ في الاتصال');
    }
}

// Logout handler
async function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        try {
            const user = getCurrentUser();
            if (user) {
                await fetch(`${API_URL}/auth/logout`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        userId: user.id,
                        username: user.username
                    })
                });
            }
        } catch (error) {
            console.error('Logout error:', error);
        }

        // Clear user data
        localStorage.removeItem('currentUser');

        // Redirect to login page
        window.location.href = 'login.html';
    }
}

// Show main app
function showMainApp() {
    // إخفاء صفحة تسجيل الدخول في النسخة القديمة من الواجهة (إن وجدت)
    const loginPage = document.getElementById('loginPage');
    if (loginPage) {
        loginPage.classList.add('d-none');
    }

    // إظهار التطبيق الرئيسي
    const mainApp = document.getElementById('mainApp');
    if (mainApp) {
        mainApp.classList.remove('d-none');
    }

    // تحديث اسم ودور المستخدم في الهيدر
    const user = typeof getCurrentUser === 'function' ? (getCurrentUser() || currentUser) : currentUser;
    if (user) {
        currentUser = user;
        const nameElement = document.getElementById('currentUserName');
        if (nameElement) nameElement.textContent = user.fullName;

        const roleElement = document.getElementById('currentUserRole');
        if (roleElement) roleElement.textContent = user.role;
    }

    // فتح صفحة نقطة البيع كصفحة افتراضية
    if (typeof showPage === 'function') {
        showPage('pos');
    }
}

// Show error message
function showError(message) {
    const errorDiv = document.getElementById('loginError');
    errorDiv.textContent = message;
    errorDiv.classList.remove('d-none');
    setTimeout(() => errorDiv.classList.add('d-none'), 3000);
}

// Show page
function showPage(pageName) {
    // Hide all pages
    document.querySelectorAll('.page-content').forEach(page => {
        page.classList.add('d-none');
    });

    // Show selected page
    const page = document.getElementById(pageName + 'Page');
    if (page) {
        page.classList.remove('d-none');

        // Update active menu item in navbar
        document.querySelectorAll('.app-navbar .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        const activeLink = document.querySelector(`.app-navbar .nav-link[data-page="${pageName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        // Load page content
        switch (pageName) {
            case 'pos':
                loadPOSPage();
                break;
            case 'products':
                loadProductsPage();
                break;
            case 'categories':
                loadCategoriesPage();
                break;
            case 'customers':
                loadCustomersPage();
                break;
            case 'invoices':
                loadInvoicesPage();
                break;
            case 'reports':
                loadReportsPage();
                break;
            case 'expenses':
                loadExpensesPage();
                break;
            case 'suppliers':
                loadSuppliersPage();
                break;
            case 'purchases':
                loadPurchasesPage();
                break;
            case 'settings':
                loadSettingsPage();
                break;
        }

        // Re-apply permissions after page content is loaded
        if (typeof reapplyPermissions === 'function') {
            reapplyPermissions();
        }
    }
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-IQ', {
        style: 'currency',
        currency: 'IQD',
        minimumFractionDigits: 0
    }).format(amount);
}

function formatDate(date) {
    return new Date(date).toLocaleDateString('ar-IQ');
}

function formatDateTime(date) {
    return new Date(date).toLocaleString('ar-IQ');
}

// Show toast notification
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed top-0 start-50 translate-middle-x mt-3`;
    toast.style.zIndex = '9999';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

