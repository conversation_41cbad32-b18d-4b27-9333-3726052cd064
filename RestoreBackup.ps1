# نص PowerShell لاستعادة نسخة احتياطية من نظام أريدوو
# Aredoo POS System - Restore Backup Script

param(
    [Parameter(Mandatory=$false)]
    [string]$BackupFile,
    [switch]$Force
)

Write-Host "=== Aredoo POS System - Restore Tool ===" -ForegroundColor Green
Write-Host "Backup Restore System" -ForegroundColor Green
Write-Host ""

# If no file specified, ask user to choose
if (-not $BackupFile) {
    Write-Host "Searching for backup files..." -ForegroundColor Yellow
    
    $backupFiles = Get-ChildItem -Path ".\Backups" -Filter "*.zip" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending
    
    if ($backupFiles.Count -eq 0) {
        Write-Host "❌ لم يتم العثور على أي ملفات نسخ احتياطية في مجلد Backups" -ForegroundColor Red
        Write-Host "يرجى وضع ملف النسخة الاحتياطية (.zip) في مجلد Backups أو تحديد المسار باستخدام المعامل -BackupFile" -ForegroundColor Yellow
        exit 1
    }
    
    Write-Host "الملفات المتاحة:" -ForegroundColor Cyan
    for ($i = 0; $i -lt $backupFiles.Count; $i++) {
        $file = $backupFiles[$i]
        $sizeMB = [math]::Round($file.Length / 1MB, 2)
        Write-Host "[$($i + 1)] $($file.Name) - $sizeMB MB - $($file.LastWriteTime.ToString('yyyy-MM-dd HH:mm'))" -ForegroundColor White
    }
    
    Write-Host ""
    $selection = Read-Host "اختر رقم الملف (1-$($backupFiles.Count)) أو اضغط Enter للإلغاء"
    
    if ([string]::IsNullOrWhiteSpace($selection)) {
        Write-Host "تم الإلغاء." -ForegroundColor Yellow
        exit 0
    }
    
    try {
        $index = [int]$selection - 1
        if ($index -lt 0 -or $index -ge $backupFiles.Count) {
            throw "رقم غير صالح"
        }
        $BackupFile = $backupFiles[$index].FullName
    } catch {
        Write-Host "❌ اختيار غير صالح" -ForegroundColor Red
        exit 1
    }
}

# التحقق من وجود الملف
if (-not (Test-Path $BackupFile)) {
    Write-Host "❌ لم يتم العثور على الملف: $BackupFile" -ForegroundColor Red
    exit 1
}

Write-Host "الملف المحدد: $BackupFile" -ForegroundColor Cyan

# تحذير المستخدم
if (-not $Force) {
    Write-Host ""
    Write-Host "⚠️  تحذير: سيتم استبدال البيانات الحالية!" -ForegroundColor Red
    Write-Host "سيتم إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة." -ForegroundColor Yellow
    Write-Host ""
    $confirm = Read-Host "هل تريد المتابعة؟ (y/N)"
    
    if ($confirm -ne "y" -and $confirm -ne "Y") {
        Write-Host "تم الإلغاء." -ForegroundColor Yellow
        exit 0
    }
}

try {
    # إنشاء نسخة احتياطية من الحالة الحالية
    Write-Host ""
    Write-Host "إنشاء نسخة احتياطية من الحالة الحالية..." -ForegroundColor Yellow
    $safetyBackupPath = ".\Backups\safety_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss').zip"
    
    if (Test-Path ".\Data\aredoo.db") {
        $tempDir = ".\temp_safety_backup"
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        Copy-Item ".\Data\aredoo.db" "$tempDir\aredoo.db"
        
        if (Test-Path ".\wwwroot\images") {
            Copy-Item ".\wwwroot\images" "$tempDir\images" -Recurse
        }
        
        Compress-Archive -Path "$tempDir\*" -DestinationPath $safetyBackupPath -Force
        Remove-Item $tempDir -Recurse -Force
        Write-Host "✓ تم إنشاء نسخة احتياطية للأمان: $safetyBackupPath" -ForegroundColor Green
    }
    
    # استخراج النسخة الاحتياطية
    Write-Host "استخراج النسخة الاحتياطية..." -ForegroundColor Yellow
    $tempExtractDir = ".\temp_restore"
    
    if (Test-Path $tempExtractDir) {
        Remove-Item $tempExtractDir -Recurse -Force
    }
    
    Expand-Archive -Path $BackupFile -DestinationPath $tempExtractDir -Force
    
    # التحقق من صحة النسخة الاحتياطية
    if (-not (Test-Path "$tempExtractDir\backup_info.json")) {
        throw "ملف النسخة الاحتياطية غير صالح - لا يحتوي على معلومات النسخة الاحتياطية"
    }
    
    # استعادة قاعدة البيانات
    Write-Host "استعادة قاعدة البيانات..." -ForegroundColor Yellow
    if (Test-Path "$tempExtractDir\aredoo.db") {
        if (-not (Test-Path ".\Data")) {
            New-Item -ItemType Directory -Path ".\Data" -Force | Out-Null
        }
        Copy-Item "$tempExtractDir\aredoo.db" ".\Data\aredoo.db" -Force
        Write-Host "✓ تم استعادة قاعدة البيانات" -ForegroundColor Green
    }
    
    # استعادة الصور
    Write-Host "استعادة الصور..." -ForegroundColor Yellow
    if (Test-Path "$tempExtractDir\images") {
        if (Test-Path ".\wwwroot\images") {
            Remove-Item ".\wwwroot\images" -Recurse -Force
        }
        Copy-Item "$tempExtractDir\images" ".\wwwroot\images" -Recurse -Force
        $imageCount = (Get-ChildItem ".\wwwroot\images" -Recurse -File).Count
        Write-Host "✓ تم استعادة $imageCount صورة" -ForegroundColor Green
    }
    
    # تنظيف الملفات المؤقتة
    Remove-Item $tempExtractDir -Recurse -Force
    
    Write-Host ""
    Write-Host "=== تم استعادة النسخة الاحتياطية بنجاح ===" -ForegroundColor Green
    Write-Host "نسخة الأمان محفوظة في: $safetyBackupPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "يُنصح بإعادة تشغيل النظام لتطبيق التغييرات." -ForegroundColor Yellow
    Write-Host "تم الانتهاء بنجاح! 🎉" -ForegroundColor Green
    
} catch {
    Write-Host ""
    Write-Host "❌ حدث خطأ أثناء استعادة النسخة الاحتياطية:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    # تنظيف الملفات المؤقتة في حالة الخطأ
    if (Test-Path $tempExtractDir) {
        Remove-Item $tempExtractDir -Recurse -Force
    }
    
    exit 1
}

# انتظار ضغط مفتاح للإغلاق
Write-Host ""
Write-Host "اضغط أي مفتاح للإغلاق..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
