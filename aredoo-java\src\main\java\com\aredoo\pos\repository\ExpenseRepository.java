package com.aredoo.pos.repository;

import com.aredoo.pos.model.Expense;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ExpenseRepository extends JpaRepository<Expense, Long> {
    
    List<Expense> findAllByOrderByExpenseDateDesc();
    
    List<Expense> findByCategory(String category);
    
    List<Expense> findByPaymentMethod(String paymentMethod);
    
    @Query("SELECT e FROM Expense e WHERE e.expenseDate BETWEEN :startDate AND :endDate")
    List<Expense> findByDateRange(@Param("startDate") LocalDateTime startDate, 
                                  @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT e FROM Expense e WHERE e.category = :category AND " +
           "e.expenseDate BETWEEN :startDate AND :endDate")
    List<Expense> findByCategoryAndDateRange(@Param("category") String category,
                                             @Param("startDate") LocalDateTime startDate, 
                                             @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT DISTINCT e.category FROM Expense e ORDER BY e.category")
    List<String> findAllCategories();
    
    @Query("SELECT SUM(e.amount) FROM Expense e")
    BigDecimal getTotalExpenseAmount();
    
    @Query("SELECT SUM(e.amount) FROM Expense e WHERE e.expenseDate BETWEEN :startDate AND :endDate")
    BigDecimal getTotalExpenseAmountByDateRange(@Param("startDate") LocalDateTime startDate, 
                                                @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT e FROM Expense e WHERE " +
           "LOWER(e.description) LIKE LOWER(CONCAT('%', :term, '%')) OR " +
           "LOWER(e.category) LIKE LOWER(CONCAT('%', :term, '%'))")
    List<Expense> searchExpenses(@Param("term") String term);
}
