async function loadReportsPage() {
    const page = document.getElementById('reportsPage');
    if (!page) {
        console.error('لم يتم العثور على عنصر reportsPage في الصفحة');
        return;
    }

    console.log('تحميل صفحة التقارير - إصدار 1.1');

    page.innerHTML = `
        <div class="row mb-3">
            <div class="col-12">
                <h4>
                    <i class="bi bi-graph-up"></i> التقارير والأرباح
                    <small class="text-muted fs-6">(تقارير الإصدار الجديد)</small>
                </h4>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-3">
            <div class="col-md-4 mb-3">
                <div class="card border-primary shadow-sm">
                    <div class="card-header bg-gradient bg-primary text-white">
                        <h6 class="mb-0"><i class="bi bi-calendar-day"></i> اليوم</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-2">
                                <small class="text-muted d-block">المبيعات</small>
                                <h5 id="todaySales" class="mb-0 fw-bold">0</h5>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted d-block">الربح</small>
                                <h5 id="todayProfit" class="mb-0 text-success fw-bold">0</h5>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted d-block">التكلفة</small>
                                <div id="todayCost" class="text-danger fw-semibold">0</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted d-block">الفواتير</small>
                                <div id="todayInvoices" class="fw-semibold">0</div>
                            </div>
                            <div class="col-12 mt-2 pt-2 border-top">
                                <small class="text-muted d-block">هامش الربح</small>
                                <div id="todayMargin" class="fw-bold fs-5">0%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card border-success shadow-sm">
                    <div class="card-header bg-gradient bg-success text-white">
                        <h6 class="mb-0"><i class="bi bi-calendar-week"></i> هذا الأسبوع</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-2">
                                <small class="text-muted d-block">المبيعات</small>
                                <h5 id="weekSales" class="mb-0 fw-bold">0</h5>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted d-block">الربح</small>
                                <h5 id="weekProfit" class="mb-0 text-success fw-bold">0</h5>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted d-block">التكلفة</small>
                                <div id="weekCost" class="text-danger fw-semibold">0</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted d-block">الفواتير</small>
                                <div id="weekInvoices" class="fw-semibold">0</div>
                            </div>
                            <div class="col-12 mt-2 pt-2 border-top">
                                <small class="text-muted d-block">هامش الربح</small>
                                <div id="weekMargin" class="fw-bold fs-5">0%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card border-info shadow-sm">
                    <div class="card-header bg-gradient bg-info text-white">
                        <h6 class="mb-0"><i class="bi bi-calendar-month"></i> هذا الشهر</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-2">
                                <small class="text-muted d-block">المبيعات</small>
                                <h5 id="monthSales" class="mb-0 fw-bold">0</h5>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted d-block">الربح</small>
                                <h5 id="monthProfit" class="mb-0 text-success fw-bold">0</h5>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted d-block">التكلفة</small>
                                <div id="monthCost" class="text-danger fw-semibold">0</div>
                            </div>
                            <div class="col-6 mb-2">
                                <small class="text-muted d-block">الفواتير</small>
                                <div id="monthInvoices" class="fw-semibold">0</div>
                            </div>
                            <div class="col-12 mt-2 pt-2 border-top">
                                <small class="text-muted d-block">هامش الربح</small>
                                <div id="monthMargin" class="fw-bold fs-5">0%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلتر التقارير -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-funnel"></i> فلتر التقارير</h6>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-end">
                            <div class="col-md-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="reportFromDate">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="reportToDate">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الفترة</label>
                                <select class="form-select" id="reportPeriod">
                                    <option value="daily">يومي</option>
                                    <option value="weekly">أسبوعي</option>
                                    <option value="monthly">شهري</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-primary w-100" onclick="loadAllReports()">
                                    <i class="bi bi-search"></i> عرض التقارير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المبيعات حسب المنتج مع الربح -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card border-success">
                    <div class="card-header bg-gradient bg-success text-white d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="bi bi-box-seam"></i> المبيعات حسب المنتج (مع الربح)</h6>
                        <button class="btn btn-sm btn-light" onclick="exportSalesByProduct()">
                            <i class="bi bi-file-earmark-excel"></i> تصدير
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="salesByProduct" style="max-height: 500px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- مقارنة الأرباح -->
        <div class="row mb-3">
            <div class="col-md-4 mb-3">
                <div class="card border-primary shadow-sm">
                    <div class="card-header bg-gradient bg-primary text-white">
                        <h6 class="mb-0"><i class="bi bi-calendar-day"></i> مقارنة اليوم</h6>
                    </div>
                    <div class="card-body">
                        <div id="todayComparison"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card border-success shadow-sm">
                    <div class="card-header bg-gradient bg-success text-white">
                        <h6 class="mb-0"><i class="bi bi-calendar-week"></i> مقارنة الأسبوع</h6>
                    </div>
                    <div class="card-body">
                        <div id="weekComparison"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card border-info shadow-sm">
                    <div class="card-header bg-gradient bg-info text-white">
                        <h6 class="mb-0"><i class="bi bi-calendar-month"></i> مقارنة الشهر</h6>
                    </div>
                    <div class="card-body">
                        <div id="monthComparison"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الأرباح حسب الفترة -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-graph-up-arrow"></i> الأرباح حسب الفترة</h6>
                    </div>
                    <div class="card-body">
                        <div id="profitByPeriod" style="max-height: 400px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ملخص شامل -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="bi bi-clipboard-data"></i> الملخص الشامل</h6>
                    </div>
                    <div class="card-body">
                        <div id="comprehensiveSummary"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أفضل المنتجات -->
        <div class="row mb-3">
            <div class="col-md-4 mb-3">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="bi bi-trophy"></i> الأكثر ربحاً</h6>
                    </div>
                    <div class="card-body">
                        <div id="topByProfit" style="max-height: 300px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="bi bi-cash-stack"></i> الأكثر مبيعاً (قيمة)</h6>
                    </div>
                    <div class="card-body">
                        <div id="topBySales" style="max-height: 300px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0"><i class="bi bi-box-seam"></i> الأكثر مبيعاً (كمية)</h6>
                    </div>
                    <div class="card-body">
                        <div id="topByQuantity" style="max-height: 300px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- المبيعات حسب الفئة ونوع الدفع -->
        <div class="row mb-3">
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-tags"></i> المبيعات حسب الفئة</h6>
                    </div>
                    <div class="card-body">
                        <div id="salesByCategory" style="max-height: 400px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-credit-card"></i> المبيعات حسب نوع الدفع</h6>
                    </div>
                    <div class="card-body">
                        <div id="salesByPaymentType" style="max-height: 400px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تحليل العملاء والمخزون -->
        <div class="row mb-3">
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-people"></i> تحليل العملاء</h6>
                    </div>
                    <div class="card-body">
                        <div id="customerAnalysis" style="max-height: 400px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0"><i class="bi bi-exclamation-triangle"></i> المنتجات قليلة المخزون</h6>
                    </div>
                    <div class="card-body">
                        <div id="lowStockProducts" style="max-height: 400px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل ربح المنتجات -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="bi bi-box-seam"></i> تفاصيل ربح المنتجات</h6>
                        <button class="btn btn-sm btn-success" onclick="exportProductProfit()">
                            <i class="bi bi-file-earmark-excel"></i> تصدير
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="productProfitDetails" style="max-height: 500px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Load initial data
    loadDashboardStats();
    loadAllReports();
}

async function loadDashboardStats() {
    try {
        const response = await fetch(`${API_URL}/reports/dashboard-stats`);
        const stats = await response.json();

        // Helper function to calculate and format margin
        const calculateMargin = (profit, sales) => {
            if (!sales || sales === 0) return 0;
            return ((profit / sales) * 100).toFixed(1);
        };

        const getMarginColor = (margin) => {
            if (margin >= 30) return 'text-success';
            if (margin >= 15) return 'text-warning';
            return 'text-danger';
        };

        // Today
        const todayProfit = Math.max(stats.today.profit || 0, 0);
        const todayMargin = calculateMargin(todayProfit, stats.today.sales);
        document.getElementById('todaySales').textContent = formatCurrency(stats.today.sales);
        document.getElementById('todayProfit').textContent = formatCurrency(todayProfit);
        document.getElementById('todayCost').textContent = formatCurrency(stats.today.cost);
        document.getElementById('todayInvoices').textContent = stats.today.invoices;
        const todayMarginEl = document.getElementById('todayMargin');
        todayMarginEl.textContent = `${todayMargin}%`;
        todayMarginEl.className = `fw-bold fs-5 ${getMarginColor(todayMargin)}`;

        // Week
        const weekProfit = Math.max(stats.week.profit || 0, 0);
        const weekMargin = calculateMargin(weekProfit, stats.week.sales);
        document.getElementById('weekSales').textContent = formatCurrency(stats.week.sales);
        document.getElementById('weekProfit').textContent = formatCurrency(weekProfit);
        document.getElementById('weekCost').textContent = formatCurrency(stats.week.cost);
        document.getElementById('weekInvoices').textContent = stats.week.invoices;
        const weekMarginEl = document.getElementById('weekMargin');
        weekMarginEl.textContent = `${weekMargin}%`;
        weekMarginEl.className = `fw-bold fs-5 ${getMarginColor(weekMargin)}`;

        // Month
        const monthProfit = Math.max(stats.month.profit || 0, 0);
        const monthMargin = calculateMargin(monthProfit, stats.month.sales);
        document.getElementById('monthSales').textContent = formatCurrency(stats.month.sales);
        document.getElementById('monthProfit').textContent = formatCurrency(monthProfit);
        document.getElementById('monthCost').textContent = formatCurrency(stats.month.cost);
        document.getElementById('monthInvoices').textContent = stats.month.invoices;
        const monthMarginEl = document.getElementById('monthMargin');
        monthMarginEl.textContent = `${monthMargin}%`;
        monthMarginEl.className = `fw-bold fs-5 ${getMarginColor(monthMargin)}`;
    } catch (error) {
        console.error('Error loading dashboard stats:', error);
    }
}

async function loadAllReports() {
    loadProfitComparisons();
    loadSalesByProduct();
    loadComprehensiveSummary();
    loadProfitByPeriod();
    loadTopProducts();
    loadSalesByCategory();
    loadSalesByPaymentType();
    loadCustomerAnalysis();
    loadLowStockProducts();
    loadProductProfitDetails();
}

async function loadProfitComparisons() {
    try {
        const response = await fetch(`${API_URL}/reports/comparisons`);
        const data = await response.json();

        // Helper function to create comparison HTML
        const createComparisonHTML = (current, previous, label) => {
            const currentProfit = Math.max(current.profit || 0, 0);
            const previousProfit = Math.max(previous.profit || 0, 0);
            const profitChange = currentProfit - previousProfit;
            const profitChangePercent = previousProfit > 0 ? ((profitChange / previousProfit) * 100) : 0;

            const currentMargin = current.sales > 0 ? (currentProfit / current.sales) * 100 : 0;
            const previousMargin = previous.sales > 0 ? (previousProfit / previous.sales) * 100 : 0;
            const marginChange = currentMargin - previousMargin;

            const isProfit = profitChange >= 0;
            const changeIcon = isProfit ? '📈' : '📉';
            const changeColor = isProfit ? 'text-success' : 'text-danger';

            return `
                <div class="text-center">
                    <div class="mb-3">
                        <small class="text-muted d-block">${label} الحالي</small>
                        <h4 class="text-success mb-0">${formatCurrency(currentProfit)}</h4>
                        <small class="badge ${currentMargin >= 30 ? 'bg-success' : currentMargin >= 15 ? 'bg-warning text-dark' : 'bg-danger'}">
                            ${currentMargin.toFixed(1)}%
                        </small>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted d-block">${label} السابق</small>
                        <h5 class="text-muted mb-0">${formatCurrency(previousProfit)}</h5>
                        <small class="badge bg-secondary">
                            ${previousMargin.toFixed(1)}%
                        </small>
                    </div>
                    <div class="pt-2 border-top">
                        <div class="${changeColor} fw-bold fs-5">
                            ${changeIcon} ${isProfit ? '+' : ''}${formatCurrency(profitChange)}
                        </div>
                        <small class="${changeColor}">
                            ${isProfit ? '+' : ''}${profitChangePercent.toFixed(1)}%
                        </small>
                        <br>
                        <small class="text-muted">
                            هامش: ${marginChange >= 0 ? '+' : ''}${marginChange.toFixed(1)}%
                        </small>
                    </div>
                </div>
            `;
        };

        // Today comparison
        document.getElementById('todayComparison').innerHTML =
            createComparisonHTML(data.today, data.yesterday, 'اليوم');

        // Week comparison
        document.getElementById('weekComparison').innerHTML =
            createComparisonHTML(data.thisWeek, data.lastWeek, 'الأسبوع');

        // Month comparison
        document.getElementById('monthComparison').innerHTML =
            createComparisonHTML(data.thisMonth, data.lastMonth, 'الشهر');

    } catch (error) {
        console.error('Error loading profit comparisons:', error);
    }
}

async function loadSalesByProduct() {
    try {
        const fromDate = document.getElementById('reportFromDate')?.value || '';
        const toDate = document.getElementById('reportToDate')?.value || '';

        let url = `${API_URL}/reports/sales-by-product?`;
        if (fromDate) url += `fromDate=${fromDate}&`;
        if (toDate) url += `toDate=${toDate}&`;

        const response = await fetch(url);
        const data = await response.json();

        const div = document.getElementById('salesByProduct');

        if (data.length === 0) {
            div.innerHTML = '<div class="text-center text-muted py-4">لا توجد بيانات</div>';
            return;
        }

        // Calculate totals
        const totalQuantity = data.reduce((sum, item) => sum + item.quantity, 0);
        const totalSales = data.reduce((sum, item) => sum + item.total, 0);
        const totalProfit = data.reduce((sum, item) => sum + Math.max(item.profit || 0, 0), 0);
        const avgMargin = totalSales > 0 ? (totalProfit / totalSales) * 100 : 0;

        div.innerHTML = `
            <table class="table table-hover table-sm">
                <thead class="table-success">
                    <tr>
                        <th style="width: 5%">#</th>
                        <th style="width: 30%">المنتج</th>
                        <th style="width: 10%" class="text-center">الكمية</th>
                        <th style="width: 15%" class="text-end">المبيعات</th>
                        <th style="width: 15%" class="text-end">الربح</th>
                        <th style="width: 12%" class="text-center">هامش الربح</th>
                        <th style="width: 13%" class="text-center">الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map((item, index) => {
                        const profit = Math.max(item.profit || 0, 0);
                        const margin = item.total > 0 ? (profit / item.total) * 100 : 0;
                        const marginClass = margin >= 30 ? 'bg-success' : margin >= 15 ? 'bg-warning text-dark' : 'bg-danger';
                        const statusIcon = margin >= 30 ? '🟢' : margin >= 15 ? '🟡' : '🔴';

                        return `
                        <tr>
                            <td>${index + 1}</td>
                            <td><strong>${item.productName}</strong></td>
                            <td class="text-center">
                                <span class="badge bg-secondary">${item.quantity}</span>
                            </td>
                            <td class="text-end fw-semibold">${formatCurrency(item.total)}</td>
                            <td class="text-end text-success fw-bold">${formatCurrency(profit)}</td>
                            <td class="text-center">
                                <span class="badge ${marginClass}">
                                    ${margin.toFixed(1)}%
                                </span>
                            </td>
                            <td class="text-center">${statusIcon}</td>
                        </tr>
                        `;
                    }).join('')}
                </tbody>
                <tfoot class="table-light">
                    <tr class="fw-bold">
                        <th colspan="2">الإجمالي (${data.length} منتج)</th>
                        <th class="text-center">
                            <span class="badge bg-dark">${totalQuantity}</span>
                        </th>
                        <th class="text-end">${formatCurrency(totalSales)}</th>
                        <th class="text-end text-success">${formatCurrency(totalProfit)}</th>
                        <th class="text-center">
                            <span class="badge ${avgMargin >= 30 ? 'bg-success' : avgMargin >= 15 ? 'bg-warning text-dark' : 'bg-danger'}">
                                ${avgMargin.toFixed(1)}%
                            </span>
                        </th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
        `;
    } catch (error) {
        console.error('Error loading sales by product:', error);
        document.getElementById('salesByProduct').innerHTML =
            '<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>';
    }
}

let profitChart = null;

async function loadProfitByPeriod() {
    try {
        const fromDate = document.getElementById('reportFromDate')?.value || '';
        const toDate = document.getElementById('reportToDate')?.value || '';
        const period = document.getElementById('reportPeriod')?.value || 'daily';

        let url = `${API_URL}/reports/profit-by-period?period=${period}`;
        if (fromDate) url += `&fromDate=${fromDate}`;
        if (toDate) url += `&toDate=${toDate}`;

        const response = await fetch(url);
        const data = await response.json();

        const div = document.getElementById('profitByPeriod');

        if (data.length === 0) {
            div.innerHTML = '<div class="text-center text-muted py-4">لا توجد بيانات</div>';
            return;
        }

        // Calculate totals
        const totalSales = data.reduce((sum, item) => sum + item.totalSales, 0);
        const totalCost = data.reduce((sum, item) => sum + item.totalCost, 0);
        const totalProfit = data.reduce((sum, item) => sum + Math.max(item.totalProfit || 0, 0), 0);
        const avgMargin = totalSales > 0 ? (totalProfit / totalSales) * 100 : 0;

        div.innerHTML = `
            <div class="row mb-3">
                <div class="col-12">
                    <canvas id="profitChart" style="max-height: 300px;"></canvas>
                </div>
            </div>
            <table class="table table-hover table-sm">
                <thead class="table-light">
                    <tr>
                        <th>الفترة</th>
                        <th>المبيعات</th>
                        <th>التكلفة</th>
                        <th>الربح</th>
                        <th>هامش الربح</th>
                        <th>الكمية</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map(item => `
                        <tr>
                            <td><strong>${item.displayDate}</strong></td>
                            <td>${formatCurrency(item.totalSales)}</td>
                            <td class="text-danger">${formatCurrency(item.totalCost)}</td>
                            <td class="text-success"><strong>${formatCurrency(Math.max(item.totalProfit || 0, 0))}</strong></td>
                            <td>
                                <span class="badge ${item.profitMargin > 30 ? 'bg-success' : item.profitMargin > 15 ? 'bg-warning' : 'bg-danger'}">
                                    ${Math.max(item.profitMargin || 0, 0).toFixed(1)}%
                                </span>
                            </td>
                            <td>${item.itemCount}</td>
                        </tr>
                    `).join('')}
                </tbody>
                <tfoot class="table-light">
                    <tr>
                        <th>الإجمالي</th>
                        <th>${formatCurrency(totalSales)}</th>
                        <th class="text-danger">${formatCurrency(totalCost)}</th>
                        <th class="text-success"><strong>${formatCurrency(totalProfit)}</strong></th>
                        <th>
                            <span class="badge ${avgMargin > 30 ? 'bg-success' : avgMargin > 15 ? 'bg-warning' : 'bg-danger'}">
                                ${avgMargin.toFixed(1)}%
                            </span>
                        </th>
                        <th>${data.reduce((sum, item) => sum + item.itemCount, 0)}</th>
                    </tr>
                </tfoot>
            </table>
        `;

        // Create chart
        const ctx = document.getElementById('profitChart');
        if (ctx) {
            // Destroy previous chart if exists
            if (profitChart) {
                profitChart.destroy();
            }

            profitChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.map(item => item.displayDate),
                    datasets: [
                        {
                            label: 'المبيعات',
                            data: data.map(item => item.totalSales),
                            backgroundColor: 'rgba(13, 110, 253, 0.5)',
                            borderColor: 'rgba(13, 110, 253, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'التكلفة',
                            data: data.map(item => item.totalCost),
                            backgroundColor: 'rgba(220, 53, 69, 0.5)',
                            borderColor: 'rgba(220, 53, 69, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'الربح',
                            data: data.map(item => Math.max(item.totalProfit || 0, 0)),
                            backgroundColor: 'rgba(25, 135, 84, 0.5)',
                            borderColor: 'rgba(25, 135, 84, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            rtl: true
                        },
                        tooltip: {
                            rtl: true,
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    label += formatCurrency(context.parsed.y);
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    }
                }
            });
        }
    } catch (error) {
        console.error('Error loading profit by period:', error);
    }
}

async function loadProductProfitDetails() {
    try {
        const fromDate = document.getElementById('reportFromDate')?.value || '';
        const toDate = document.getElementById('reportToDate')?.value || '';

        let url = `${API_URL}/reports/product-profit-details?`;
        if (fromDate) url += `fromDate=${fromDate}&`;
        if (toDate) url += `toDate=${toDate}&`;

        const response = await fetch(url);
        const data = await response.json();

        const div = document.getElementById('productProfitDetails');

        if (data.length === 0) {
            div.innerHTML = '<div class="text-center text-muted py-4">لا توجد بيانات</div>';
            return;
        }

        div.innerHTML = `
            <table class="table table-hover table-sm">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>المنتج</th>
                        <th>الكمية المباعة</th>
                        <th>المبيعات</th>
                        <th>التكلفة</th>
                        <th>الربح</th>
                        <th>هامش الربح</th>
                        <th>متوسط سعر البيع</th>
                        <th>متوسط سعر الشراء</th>
                        <th>عدد الفواتير</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map((item, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td><strong>${item.productName}</strong></td>
                            <td><span class="badge bg-secondary">${item.quantitySold}</span></td>
                            <td>${formatCurrency(item.totalSales)}</td>
                            <td class="text-danger">${formatCurrency(item.totalCost)}</td>
                            <td class="text-success"><strong>${formatCurrency(Math.max(item.totalProfit || 0, 0))}</strong></td>
                            <td>
                                <span class="badge ${item.profitMargin > 30 ? 'bg-success' : item.profitMargin > 15 ? 'bg-warning text-dark' : 'bg-danger'}">
                                    ${Math.max(item.profitMargin || 0, 0).toFixed(1)}%
                                </span>
                            </td>
                            <td>${formatCurrency(item.avgSalePrice)}</td>
                            <td>${formatCurrency(item.avgPurchasePrice)}</td>
                            <td><span class="badge bg-info">${item.invoiceCount}</span></td>
                        </tr>
                    `).join('')}
                </tbody>
                <tfoot class="table-light">
                    <tr>
                        <th colspan="2">الإجمالي (${data.length} منتج)</th>
                        <th><span class="badge bg-secondary">${data.reduce((sum, item) => sum + item.quantitySold, 0)}</span></th>
                        <th>${formatCurrency(data.reduce((sum, item) => sum + item.totalSales, 0))}</th>
                        <th class="text-danger">${formatCurrency(data.reduce((sum, item) => sum + item.totalCost, 0))}</th>
                        <th class="text-success"><strong>${formatCurrency(data.reduce((sum, item) => sum + Math.max(item.totalProfit || 0, 0), 0))}</strong></th>
                        <th>
                            ${(() => {
                                const totalSales = data.reduce((sum, item) => sum + item.totalSales, 0);
                                const totalProfit = data.reduce((sum, item) => sum + Math.max(item.totalProfit || 0, 0), 0);
                                const avgMargin = totalSales > 0 ? (totalProfit / totalSales) * 100 : 0;
                                return `<span class="badge ${avgMargin > 30 ? 'bg-success' : avgMargin > 15 ? 'bg-warning text-dark' : 'bg-danger'}">${avgMargin.toFixed(1)}%</span>`;
                            })()}
                        </th>
                        <th colspan="3"></th>
                    </tr>
                </tfoot>
            </table>
        `;
    } catch (error) {
        console.error('Error loading product profit details:', error);
    }
}

async function loadComprehensiveSummary() {
    try {
        const fromDate = document.getElementById('reportFromDate')?.value || '';
        const toDate = document.getElementById('reportToDate')?.value || '';

        let url = `${API_URL}/reports/comprehensive-summary?`;
        if (fromDate) url += `fromDate=${fromDate}&`;
        if (toDate) url += `toDate=${toDate}&`;

        const response = await fetch(url);
        const data = await response.json();

        const div = document.getElementById('comprehensiveSummary');
        div.innerHTML = `
            <div class="row text-center">
                <div class="col-md-3 mb-3">
                    <div class="p-3 bg-light rounded">
                        <h6 class="text-muted mb-2">إجمالي المبيعات</h6>
                        <h4 class="text-primary mb-1">${formatCurrency(data.totalSales)}</h4>
                        <small class="text-muted">${data.totalInvoices} فاتورة</small>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="p-3 bg-light rounded">
                        <h6 class="text-muted mb-2">إجمالي الربح</h6>
                        <h4 class="text-success mb-1">${formatCurrency(Math.max(data.totalProfit || 0, 0))}</h4>
                        <small class="text-muted">هامش ${Math.max(data.profitMargin || 0, 0).toFixed(1)}%</small>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="p-3 bg-light rounded">
                        <h6 class="text-muted mb-2">إجمالي التكلفة</h6>
                        <h4 class="text-danger mb-1">${formatCurrency(data.totalCost)}</h4>
                        <small class="text-muted">${data.totalItemsSold} قطعة</small>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="p-3 bg-light rounded">
                        <h6 class="text-muted mb-2">المدفوع / الباقي</h6>
                        <h4 class="text-info mb-1">${formatCurrency(data.totalPaid)}</h4>
                        <small class="text-danger">${formatCurrency(data.totalRemaining)} باقي</small>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row text-center">
                <div class="col-md-2 mb-2">
                    <small class="text-muted d-block">عدد العملاء</small>
                    <strong>${data.uniqueCustomers}</strong>
                </div>
                <div class="col-md-2 mb-2">
                    <small class="text-muted d-block">منتجات مباعة</small>
                    <strong>${data.uniqueProductsSold}</strong>
                </div>
                <div class="col-md-2 mb-2">
                    <small class="text-muted d-block">متوسط الفاتورة</small>
                    <strong>${formatCurrency(data.avgInvoiceValue)}</strong>
                </div>
                <div class="col-md-2 mb-2">
                    <small class="text-muted d-block">متوسط الربح</small>
                    <strong class="text-success">${formatCurrency(Math.max(data.avgProfit || 0, 0))}</strong>
                </div>
                <div class="col-md-2 mb-2">
                    <small class="text-muted d-block">إجمالي الخصم</small>
                    <strong class="text-warning">${formatCurrency(data.totalDiscount)}</strong>
                </div>
                <div class="col-md-2 mb-2">
                    <small class="text-muted d-block">مخزون قليل</small>
                    <strong class="text-danger">${data.lowStockCount}</strong>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('Error loading comprehensive summary:', error);
    }
}

async function loadTopProducts() {
    try {
        const fromDate = document.getElementById('reportFromDate')?.value || '';
        const toDate = document.getElementById('reportToDate')?.value || '';

        let url = `${API_URL}/reports/top-products?limit=10`;
        if (fromDate) url += `&fromDate=${fromDate}`;
        if (toDate) url += `&toDate=${toDate}`;

        const response = await fetch(url);
        const data = await response.json();

        // Top by profit
        document.getElementById('topByProfit').innerHTML = `
            <div class="list-group list-group-flush">
                ${data.topByProfit.map((item, index) => `
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <span class="badge bg-success me-2">${index + 1}</span>
                            <strong>${item.productName}</strong>
                            <br><small class="text-muted">${item.quantity} قطعة</small>
                        </div>
                        <div class="text-end">
                            <div class="text-success fw-bold">${formatCurrency(Math.max(item.totalProfit || 0, 0))}</div>
                            <small class="text-muted">${formatCurrency(item.totalSales)}</small>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        // Top by sales
        document.getElementById('topBySales').innerHTML = `
            <div class="list-group list-group-flush">
                ${data.topBySales.map((item, index) => `
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <span class="badge bg-info me-2">${index + 1}</span>
                            <strong>${item.productName}</strong>
                            <br><small class="text-muted">${item.quantity} قطعة</small>
                        </div>
                        <div class="text-end">
                            <div class="text-primary fw-bold">${formatCurrency(item.totalSales)}</div>
                            <small class="text-success">${formatCurrency(Math.max(item.totalProfit || 0, 0))}</small>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;

        // Top by quantity
        document.getElementById('topByQuantity').innerHTML = `
            <div class="list-group list-group-flush">
                ${data.topByQuantity.map((item, index) => `
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <span class="badge bg-warning text-dark me-2">${index + 1}</span>
                            <strong>${item.productName}</strong>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">${item.quantity} قطعة</div>
                            <small class="text-muted">${formatCurrency(item.totalSales)}</small>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    } catch (error) {
        console.error('Error loading top products:', error);
    }
}

async function loadSalesByCategory() {
    try {
        const fromDate = document.getElementById('reportFromDate')?.value || '';
        const toDate = document.getElementById('reportToDate')?.value || '';

        let url = `${API_URL}/reports/sales-by-category?`;
        if (fromDate) url += `fromDate=${fromDate}&`;
        if (toDate) url += `toDate=${toDate}&`;

        const response = await fetch(url);
        const data = await response.json();

        const div = document.getElementById('salesByCategory');
        div.innerHTML = `
            <table class="table table-hover table-sm mb-0">
                <thead class="table-light">
                    <tr>
                        <th>الفئة</th>
                        <th>المنتجات</th>
                        <th>الكمية</th>
                        <th>المبيعات</th>
                        <th>الربح</th>
                        <th>هامش الربح</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map(item => `
                        <tr>
                            <td><strong>${item.category}</strong></td>
                            <td><span class="badge bg-secondary">${item.productCount}</span></td>
                            <td>${item.quantitySold}</td>
                            <td>${formatCurrency(item.totalSales)}</td>
                            <td class="text-success">${formatCurrency(Math.max(item.totalProfit || 0, 0))}</td>
                            <td>
                                <span class="badge ${item.profitMargin > 30 ? 'bg-success' : item.profitMargin > 15 ? 'bg-warning text-dark' : 'bg-danger'}">
                                    ${Math.max(item.profitMargin || 0, 0).toFixed(1)}%
                                </span>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    } catch (error) {
        console.error('Error loading sales by category:', error);
    }
}

async function loadSalesByPaymentType() {
    try {
        const fromDate = document.getElementById('reportFromDate')?.value || '';
        const toDate = document.getElementById('reportToDate')?.value || '';

        let url = `${API_URL}/reports/sales-by-payment-type?`;
        if (fromDate) url += `fromDate=${fromDate}&`;
        if (toDate) url += `toDate=${toDate}&`;

        const response = await fetch(url);
        const data = await response.json();

        const div = document.getElementById('salesByPaymentType');
        div.innerHTML = `
            <table class="table table-hover table-sm mb-0">
                <thead class="table-light">
                    <tr>
                        <th>نوع الدفع</th>
                        <th>الفواتير</th>
                        <th>المبيعات</th>
                        <th>الربح</th>
                        <th>المدفوع</th>
                        <th>الباقي</th>
                        <th>الخصم</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map(item => {
                        const profit = Math.max(item.totalProfit || 0, 0);
                        const margin = item.totalSales > 0 ? (profit / item.totalSales) * 100 : 0;
                        return `
                        <tr>
                            <td><strong>${item.paymentTypeName}</strong></td>
                            <td><span class="badge bg-info">${item.invoiceCount}</span></td>
                            <td>${formatCurrency(item.totalSales)}</td>
                            <td>
                                <span class="text-success fw-bold">${formatCurrency(profit)}</span>
                                <br>
                                <small class="badge ${margin >= 30 ? 'bg-success' : margin >= 15 ? 'bg-warning text-dark' : 'bg-danger'}">
                                    ${margin.toFixed(1)}%
                                </small>
                            </td>
                            <td class="text-success">${formatCurrency(item.totalPaid)}</td>
                            <td class="text-danger">${formatCurrency(item.totalRemaining)}</td>
                            <td class="text-warning">${formatCurrency(item.totalDiscount)}</td>
                        </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        `;
    } catch (error) {
        console.error('Error loading sales by payment type:', error);
    }
}

async function loadCustomerAnalysis() {
    try {
        const fromDate = document.getElementById('reportFromDate')?.value || '';
        const toDate = document.getElementById('reportToDate')?.value || '';

        let url = `${API_URL}/reports/customer-analysis?`;
        if (fromDate) url += `fromDate=${fromDate}&`;
        if (toDate) url += `toDate=${toDate}&`;

        const response = await fetch(url);
        const data = await response.json();

        const div = document.getElementById('customerAnalysis');
        div.innerHTML = `
            <table class="table table-hover table-sm mb-0">
                <thead class="table-light">
                    <tr>
                        <th>العميل</th>
                        <th>الفواتير</th>
                        <th>المبيعات</th>
                        <th>الباقي</th>
                        <th>متوسط الفاتورة</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.slice(0, 10).map(item => `
                        <tr>
                            <td><strong>${item.customerName}</strong></td>
                            <td><span class="badge bg-info">${item.invoiceCount}</span></td>
                            <td>${formatCurrency(item.totalSales)}</td>
                            <td class="text-danger">${formatCurrency(item.totalRemaining)}</td>
                            <td>${formatCurrency(item.avgInvoiceValue)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    } catch (error) {
        console.error('Error loading customer analysis:', error);
    }
}

async function loadLowStockProducts() {
    try {
        const response = await fetch(`${API_URL}/reports/low-stock-products?threshold=10`);
        const data = await response.json();

        const div = document.getElementById('lowStockProducts');

        if (data.length === 0) {
            div.innerHTML = '<div class="text-center text-muted py-4">جميع المنتجات متوفرة</div>';
            return;
        }

        div.innerHTML = `
            <table class="table table-hover table-sm mb-0">
                <thead class="table-light">
                    <tr>
                        <th>المنتج</th>
                        <th>الفئة</th>
                        <th>الكمية</th>
                        <th>الحالة</th>
                        <th>قيمة المخزون</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map(item => `
                        <tr>
                            <td>
                                <strong>${item.name}</strong>
                                <br><small class="text-muted">${item.code}</small>
                            </td>
                            <td>${item.category || '-'}</td>
                            <td><span class="badge ${item.quantity === 0 ? 'bg-danger' : item.quantity <= 5 ? 'bg-warning text-dark' : 'bg-secondary'}">${item.quantity}</span></td>
                            <td>
                                <span class="badge ${item.status === 'نفذ' ? 'bg-danger' : item.status === 'حرج' ? 'bg-warning text-dark' : 'bg-secondary'}">
                                    ${item.status}
                                </span>
                            </td>
                            <td>${formatCurrency(item.stockValue)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    } catch (error) {
        console.error('Error loading low stock products:', error);
    }
}

function exportSalesByProduct() {
    const fromDate = document.getElementById('reportFromDate')?.value || '';
    const toDate = document.getElementById('reportToDate')?.value || '';

    let url = `${API_URL}/reports/sales-by-product?`;
    if (fromDate) url += `fromDate=${fromDate}&`;
    if (toDate) url += `toDate=${toDate}&`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            // Create CSV content
            let csv = 'المنتج,الكمية,المبيعات,الربح,هامش الربح %\n';

            data.forEach(item => {
                const profit = Math.max(item.profit || 0, 0);
                const margin = item.total > 0 ? (profit / item.total) * 100 : 0;
                csv += `"${item.productName}",${item.quantity},${item.total},${profit},${margin.toFixed(2)}\n`;
            });

            // Download CSV
            const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `sales_by_product_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            showToast('تم تصدير التقرير بنجاح', 'success');
        })
        .catch(error => {
            console.error('Error exporting:', error);
            showToast('فشل تصدير التقرير', 'error');
        });
}

function exportProductProfit() {
    const fromDate = document.getElementById('reportFromDate')?.value || '';
    const toDate = document.getElementById('reportToDate')?.value || '';

    let url = `${API_URL}/reports/product-profit-details?`;
    if (fromDate) url += `fromDate=${fromDate}&`;
    if (toDate) url += `toDate=${toDate}&`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            // Create CSV content
            let csv = 'المنتج,الكمية المباعة,المبيعات,التكلفة,الربح,هامش الربح %,متوسط سعر البيع,متوسط سعر الشراء,عدد الفواتير\n';

            data.forEach(item => {
                csv += `"${item.productName}",${item.quantitySold},${item.totalSales},${item.totalCost},${Math.max(item.totalProfit || 0, 0)},${Math.max(item.profitMargin || 0, 0).toFixed(2)},${item.avgSalePrice},${item.avgPurchasePrice},${item.invoiceCount}\n`;
            });

            // Download CSV
            const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `product_profit_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            showToast('تم تصدير التقرير بنجاح', 'success');
        })
        .catch(error => {
            console.error('Error exporting:', error);
            showToast('فشل تصدير التقرير', 'error');
        });
}

