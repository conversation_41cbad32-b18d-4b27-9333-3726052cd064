# أريدوو - Aredoo POS System (Java Version)

نظام إدارة المبيعات والمخزون - نسخة Java

## المتطلبات

- Java 17 أو أحدث
- لا يتطلب تثبيت Maven أو Gradle (يستخدم Gradle Wrapper)

## التشغيل

### الطريقة الأولى: استخدام ملف Batch المحسن (الأفضل)
```bash
start-aredoo-improved.bat
```

### الطريقة الثانية: استخدام <PERSON>ven (إذا كان مثبتاً)
```bash
mvn spring-boot:run
```

### الطريقة الثالثة: استخدام ملف JAR جاهز
```bash
java -jar aredoo-pos-1.0.0.jar
```

### الطريقة الرابعة: اختبار التطبيق
```bash
test-app.bat
```

## الوصول للتطبيق

- **العنوان**: http://localhost:5000
- **المستخدم الافتراضي**: admin
- **كلمة المرور**: 1234

## المميزات

- ✅ نقطة البيع (POS)
- ✅ إدارة المنتجات والمخزون
- ✅ إدارة العملاء والموردين
- ✅ إدارة الفواتير والمبيعات
- ✅ إدارة المشتريات
- ✅ إدارة المصاريف
- ✅ التقارير والإحصائيات
- ✅ إدارة المستخدمين والصلاحيات
- ✅ واجهة عربية متجاوبة

## قاعدة البيانات

يستخدم التطبيق قاعدة بيانات H2 المحفوظة في:
```
data/aredoo.mv.db
```

## الهيكل

```
aredoo-java/
├── src/main/java/com/aredoo/pos/
│   ├── model/          # نماذج البيانات
│   ├── repository/     # طبقة قاعدة البيانات
│   ├── controller/     # واجهات API
│   ├── config/         # إعدادات التطبيق
│   └── AredooPosApplication.java
├── src/main/resources/
│   ├── static/         # الواجهة الأمامية
│   └── application.properties
├── pom.xml
└── start-aredoo.bat
```

## التطوير

لتشغيل التطبيق في وضع التطوير:
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

## الدعم

للدعم والاستفسارات، يرجى التواصل مع فريق التطوير.
