$body = @{
    code = 'T001'
    name = 'Test Product'
    nameAr = 'Test Product'
    barcode = '1234567890123'
    category = 'Other'
    unit = 'piece'
    purchasePrice = 10
    salePrice = 15
    wholesalePrice = 12
    quantity = 5
    minQuantity = 1
    image = $null
    isActive = $true
} | ConvertTo-Json

Invoke-WebRequest -Uri 'http://localhost:5000/api/products' -Method Post -Body $body -ContentType 'application/json' -UseBasicParsing |
    Select-Object StatusCode, Content |
    Format-List

