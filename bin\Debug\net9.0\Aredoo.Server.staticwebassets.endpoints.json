{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "css/style.css", "AssetFile": "css/style.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000131492439"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7604"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg=\""}, {"Name": "ETag", "Value": "W/\"WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:07:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY="}]}, {"Route": "css/style.css", "AssetFile": "css/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "39958"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:06:43 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY="}]}, {"Route": "css/style.css.gz", "AssetFile": "css/style.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7604"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:07:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg="}]}, {"Route": "css/style.zdfn4todj4.css", "AssetFile": "css/style.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000131492439"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7604"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg=\""}, {"Name": "ETag", "Value": "W/\"WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:07:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zdfn4todj4"}, {"Name": "integrity", "Value": "sha256-WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY="}, {"Name": "label", "Value": "css/style.css"}]}, {"Route": "css/style.zdfn4todj4.css", "AssetFile": "css/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "39958"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:06:43 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zdfn4todj4"}, {"Name": "integrity", "Value": "sha256-WXp+PkfnkqGq7laJogkMEMowOu0V2IUls8a0oTQzRtY="}, {"Name": "label", "Value": "css/style.css"}]}, {"Route": "css/style.zdfn4todj4.css.gz", "AssetFile": "css/style.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7604"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:07:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zdfn4todj4"}, {"Name": "integrity", "Value": "sha256-y+C+NGyTs1Bmv9JKA0wuI+utf8buSUwkKfizsp7zRkg="}, {"Name": "label", "Value": "css/style.css.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000703234880"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1421"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA=\""}, {"Name": "ETag", "Value": "W/\"Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:07:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7002"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:06:52 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1421"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:07:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA="}]}, {"Route": "index.j1wgh6mdca.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000703234880"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1421"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA=\""}, {"Name": "ETag", "Value": "W/\"Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:07:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j1wgh6mdca"}, {"Name": "integrity", "Value": "sha256-Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.j1wgh6mdca.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7002"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:06:52 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j1wgh6mdca"}, {"Name": "integrity", "Value": "sha256-Ny8I2y2ZbLFWevWn+RfX+Sd5w3y+xAvPm0oSn/7tiU4="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.j1wgh6mdca.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1421"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:07:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j1wgh6mdca"}, {"Name": "integrity", "Value": "sha256-Q1rMoUlGhutqC43l/Swj21FGxwOnw1gAhxocbbGJodA="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "js/app.1posqj9azf.js", "AssetFile": "js/app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000475963827"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2100"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc=\""}, {"Name": "ETag", "Value": "W/\"+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs=\""}, {"Name": "Last-Modified", "Value": "Tue, 18 Nov 2025 15:15:32 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1posqj9azf"}, {"Name": "integrity", "Value": "sha256-+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.1posqj9azf.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6298"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Nov 2025 15:11:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1posqj9azf"}, {"Name": "integrity", "Value": "sha256-+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs="}, {"Name": "label", "Value": "js/app.js"}]}, {"Route": "js/app.1posqj9azf.js.gz", "AssetFile": "js/app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2100"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc=\""}, {"Name": "Last-Modified", "Value": "Tue, 18 Nov 2025 15:15:32 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1posqj9azf"}, {"Name": "integrity", "Value": "sha256-IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc="}, {"Name": "label", "Value": "js/app.js.gz"}]}, {"Route": "js/app.js", "AssetFile": "js/app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000475963827"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2100"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc=\""}, {"Name": "ETag", "Value": "W/\"+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs=\""}, {"Name": "Last-Modified", "Value": "Tue, 18 Nov 2025 15:15:32 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs="}]}, {"Route": "js/app.js", "AssetFile": "js/app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6298"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Nov 2025 15:11:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+/1x5VNLgxPunpkoS/EDShG/ly1rYma2k0yHNCclphs="}]}, {"Route": "js/app.js.gz", "AssetFile": "js/app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2100"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc=\""}, {"Name": "Last-Modified", "Value": "Tue, 18 Nov 2025 15:15:32 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IeIe2aS54ONtnLY1VQrOEwcC51f9qysdgbQPQ55G4Mc="}]}, {"Route": "js/auth.iyy8yegl4k.js", "AssetFile": "js/auth.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000880281690"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "ETag", "Value": "W/\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 12:05:28 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyy8yegl4k"}, {"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}, {"Name": "label", "Value": "js/auth.js"}]}, {"Route": "js/auth.iyy8yegl4k.js", "AssetFile": "js/auth.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 15:25:05 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyy8yegl4k"}, {"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}, {"Name": "label", "Value": "js/auth.js"}]}, {"Route": "js/auth.iyy8yegl4k.js.gz", "AssetFile": "js/auth.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 12:05:28 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iyy8yegl4k"}, {"Name": "integrity", "Value": "sha256-X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc="}, {"Name": "label", "Value": "js/auth.js.gz"}]}, {"Route": "js/auth.js", "AssetFile": "js/auth.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000880281690"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "ETag", "Value": "W/\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 12:05:28 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}]}, {"Route": "js/auth.js", "AssetFile": "js/auth.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3765"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 15:25:05 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BgOd7qBTLRy7dzyxPqEW6Omnd0CX0IdsrCvlDPXUhZ0="}]}, {"Route": "js/auth.js.gz", "AssetFile": "js/auth.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1135"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 12:05:28 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X1ewhRpMg9hm1uHTyallJ9iQ51TLSvAnxSVQzzTuPXc="}]}, {"Route": "js/categories.js", "AssetFile": "js/categories.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000418235048"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "ETag", "Value": "W/\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:58:49 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}]}, {"Route": "js/categories.js", "AssetFile": "js/categories.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10343"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:53:33 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}]}, {"Route": "js/categories.js.gz", "AssetFile": "js/categories.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:58:49 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM="}]}, {"Route": "js/categories.q4k5ude2ax.js", "AssetFile": "js/categories.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000418235048"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "ETag", "Value": "W/\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:58:49 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q4k5ude2ax"}, {"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}, {"Name": "label", "Value": "js/categories.js"}]}, {"Route": "js/categories.q4k5ude2ax.js", "AssetFile": "js/categories.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10343"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:53:33 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q4k5ude2ax"}, {"Name": "integrity", "Value": "sha256-Koc8PVyw43R00aJKcUbEUUijKMculVbJ5zNWWEwG6k4="}, {"Name": "label", "Value": "js/categories.js"}]}, {"Route": "js/categories.q4k5ude2ax.js.gz", "AssetFile": "js/categories.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2390"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:58:49 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q4k5ude2ax"}, {"Name": "integrity", "Value": "sha256-N6iWk11MMpMR5bWNIgF2rpy7c5VWtfFmlp8/1GpiXsM="}, {"Name": "label", "Value": "js/categories.js.gz"}]}, {"Route": "js/customers.hy3h9tabsi.js", "AssetFile": "js/customers.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000372439479"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "ETag", "Value": "W/\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:58:49 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy3h9tabsi"}, {"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}, {"Name": "label", "Value": "js/customers.js"}]}, {"Route": "js/customers.hy3h9tabsi.js", "AssetFile": "js/customers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "14539"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:52:53 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy3h9tabsi"}, {"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}, {"Name": "label", "Value": "js/customers.js"}]}, {"Route": "js/customers.hy3h9tabsi.js.gz", "AssetFile": "js/customers.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:58:49 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hy3h9tabsi"}, {"Name": "integrity", "Value": "sha256-sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4="}, {"Name": "label", "Value": "js/customers.js.gz"}]}, {"Route": "js/customers.js", "AssetFile": "js/customers.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000372439479"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "ETag", "Value": "W/\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:58:49 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}]}, {"Route": "js/customers.js", "AssetFile": "js/customers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "14539"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:52:53 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tzTxYS1aIkKFk0vRt0yUP84RHEEB5NEex26KWPnBxIc="}]}, {"Route": "js/customers.js.gz", "AssetFile": "js/customers.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2684"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 13:58:49 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sQDtQA0as2S4H9P6T1yThBtM/JkRDnUKqDavznczWO4="}]}, {"Route": "js/expenses.js", "AssetFile": "js/expenses.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000290782204"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3438"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A=\""}, {"Name": "ETag", "Value": "W/\"FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs=\""}, {"Name": "Last-Modified", "Value": "Tue, 18 Nov 2025 15:01:30 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs="}]}, {"Route": "js/expenses.js", "AssetFile": "js/expenses.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "18166"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 18 Nov 2025 15:01:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs="}]}, {"Route": "js/expenses.js.gz", "AssetFile": "js/expenses.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3438"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A=\""}, {"Name": "Last-Modified", "Value": "Tue, 18 Nov 2025 15:01:30 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A="}]}, {"Route": "js/expenses.sw1pmnb1tp.js", "AssetFile": "js/expenses.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000290782204"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3438"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A=\""}, {"Name": "ETag", "Value": "W/\"FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs=\""}, {"Name": "Last-Modified", "Value": "Tue, 18 Nov 2025 15:01:30 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sw1pmnb1tp"}, {"Name": "integrity", "Value": "sha256-FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs="}, {"Name": "label", "Value": "js/expenses.js"}]}, {"Route": "js/expenses.sw1pmnb1tp.js", "AssetFile": "js/expenses.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "18166"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 18 Nov 2025 15:01:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sw1pmnb1tp"}, {"Name": "integrity", "Value": "sha256-FKgwCz13SOOZVbBKGsOlK1NphH4brqQY0N1fqBQ8QIs="}, {"Name": "label", "Value": "js/expenses.js"}]}, {"Route": "js/expenses.sw1pmnb1tp.js.gz", "AssetFile": "js/expenses.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3438"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A=\""}, {"Name": "Last-Modified", "Value": "Tue, 18 Nov 2025 15:01:30 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sw1pmnb1tp"}, {"Name": "integrity", "Value": "sha256-0x7sxXZp5/3Pq8eQxZvOOUu4iOcv28hKJy22PKDxe8A="}, {"Name": "label", "Value": "js/expenses.js.gz"}]}, {"Route": "js/invoices.7ene3dnx54.js", "AssetFile": "js/invoices.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000203417413"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "ETag", "Value": "W/\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:38:50 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ene3dnx54"}, {"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}, {"Name": "label", "Value": "js/invoices.js"}]}, {"Route": "js/invoices.7ene3dnx54.js", "AssetFile": "js/invoices.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "30213"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:27:48 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ene3dnx54"}, {"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}, {"Name": "label", "Value": "js/invoices.js"}]}, {"Route": "js/invoices.7ene3dnx54.js.gz", "AssetFile": "js/invoices.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:38:50 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ene3dnx54"}, {"Name": "integrity", "Value": "sha256-oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI="}, {"Name": "label", "Value": "js/invoices.js.gz"}]}, {"Route": "js/invoices.js", "AssetFile": "js/invoices.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000203417413"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "ETag", "Value": "W/\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:38:50 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}]}, {"Route": "js/invoices.js", "AssetFile": "js/invoices.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "30213"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:27:48 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-71TnfUGCG8ljJ08G8+TlQrKKMTfph7Y79cXe44JqaeY="}]}, {"Route": "js/invoices.js.gz", "AssetFile": "js/invoices.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4915"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI=\""}, {"Name": "Last-Modified", "Value": "Wed, 12 Nov 2025 19:38:50 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oD1rdceUukCaKMBclVV6IfP7OmK3qKtMec1g6qkeahI="}]}, {"Route": "js/pos.js", "AssetFile": "js/pos.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000129550460"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7718"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8=\""}, {"Name": "ETag", "Value": "W/\"9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:07:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c="}]}, {"Route": "js/pos.js", "AssetFile": "js/pos.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "37680"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:06:29 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c="}]}, {"Route": "js/pos.js.gz", "AssetFile": "js/pos.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7718"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:07:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8="}]}, {"Route": "js/pos.pjnfkijmjf.js", "AssetFile": "js/pos.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000129550460"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7718"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8=\""}, {"Name": "ETag", "Value": "W/\"9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:07:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pjnfkijmjf"}, {"Name": "integrity", "Value": "sha256-9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c="}, {"Name": "label", "Value": "js/pos.js"}]}, {"Route": "js/pos.pjnfkijmjf.js", "AssetFile": "js/pos.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "37680"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:06:29 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pjnfkijmjf"}, {"Name": "integrity", "Value": "sha256-9fBx2HYGq8pnWX4y638wJhSf2lSjcp8kaLo0vbBXo6c="}, {"Name": "label", "Value": "js/pos.js"}]}, {"Route": "js/pos.pjnfkijmjf.js.gz", "AssetFile": "js/pos.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "7718"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 18:07:55 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pjnfkijmjf"}, {"Name": "integrity", "Value": "sha256-Y9AAQFdpxbnxeOLCYmeDn/xDQvyT15WTuIgo2q+uaA8="}, {"Name": "label", "Value": "js/pos.js.gz"}]}, {"Route": "js/products.9n0jr0b026.js", "AssetFile": "js/products.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000120700060"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "ETag", "Value": "W/\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:38 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0jr0b026"}, {"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}, {"Name": "label", "Value": "js/products.js"}]}, {"Route": "js/products.9n0jr0b026.js", "AssetFile": "js/products.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "44477"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0jr0b026"}, {"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}, {"Name": "label", "Value": "js/products.js"}]}, {"Route": "js/products.9n0jr0b026.js.gz", "AssetFile": "js/products.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:38 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9n0jr0b026"}, {"Name": "integrity", "Value": "sha256-cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU="}, {"Name": "label", "Value": "js/products.js.gz"}]}, {"Route": "js/products.js", "AssetFile": "js/products.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000120700060"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "ETag", "Value": "W/\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:38 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}]}, {"Route": "js/products.js", "AssetFile": "js/products.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "44477"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:35:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+8qubZF6nobRa4Vi1AmGxa9wo6oQgti3zpBsyuYf0P0="}]}, {"Route": "js/products.js.gz", "AssetFile": "js/products.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "8284"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU=\""}, {"Name": "Last-Modified", "Value": "Sat, 15 Nov 2025 18:43:38 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cPg6IgylWnAzCh9A8c9/YRCcEovvfuRtUrG3eTwrdvU="}]}, {"Route": "js/purchases.js", "AssetFile": "js/purchases.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000259403372"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3854"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI=\""}, {"Name": "ETag", "Value": "W/\"/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:02:04 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8="}]}, {"Route": "js/purchases.js", "AssetFile": "js/purchases.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "19719"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 18 Nov 2025 15:42:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8="}]}, {"Route": "js/purchases.js.gz", "AssetFile": "js/purchases.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3854"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:02:04 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI="}]}, {"Route": "js/purchases.uxkkg3wsx6.js", "AssetFile": "js/purchases.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000259403372"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3854"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI=\""}, {"Name": "ETag", "Value": "W/\"/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:02:04 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uxkkg3wsx6"}, {"Name": "integrity", "Value": "sha256-/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8="}, {"Name": "label", "Value": "js/purchases.js"}]}, {"Route": "js/purchases.uxkkg3wsx6.js", "AssetFile": "js/purchases.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "19719"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 18 Nov 2025 15:42:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uxkkg3wsx6"}, {"Name": "integrity", "Value": "sha256-/lz99RppGma8f0Q9M/FfxUZAZyuStL95Lx7uSeAikc8="}, {"Name": "label", "Value": "js/purchases.js"}]}, {"Route": "js/purchases.uxkkg3wsx6.js.gz", "AssetFile": "js/purchases.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3854"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:02:04 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "uxkkg3wsx6"}, {"Name": "integrity", "Value": "sha256-L2DAn2hNrvcZKW4yRq71tVHjOcqPKCI67MD5j5IHvmI="}, {"Name": "label", "Value": "js/purchases.js.gz"}]}, {"Route": "js/reports.2zj8ofrn42.js", "AssetFile": "js/reports.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000147601476"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6774"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk=\""}, {"Name": "ETag", "Value": "W/\"4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Nov 2025 14:10:46 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zj8ofrn42"}, {"Name": "integrity", "Value": "sha256-4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ="}, {"Name": "label", "Value": "js/reports.js"}]}, {"Route": "js/reports.2zj8ofrn42.js", "AssetFile": "js/reports.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "57134"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Nov 2025 14:05:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zj8ofrn42"}, {"Name": "integrity", "Value": "sha256-4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ="}, {"Name": "label", "Value": "js/reports.js"}]}, {"Route": "js/reports.2zj8ofrn42.js.gz", "AssetFile": "js/reports.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6774"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Nov 2025 14:10:46 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2zj8ofrn42"}, {"Name": "integrity", "Value": "sha256-oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk="}, {"Name": "label", "Value": "js/reports.js.gz"}]}, {"Route": "js/reports.js", "AssetFile": "js/reports.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000147601476"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6774"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk=\""}, {"Name": "ETag", "Value": "W/\"4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Nov 2025 14:10:46 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ="}]}, {"Route": "js/reports.js", "AssetFile": "js/reports.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "57134"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Nov 2025 14:05:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4sSIKiYZB5nuL7KWTQ1ImL4vKWYy7+KL+TfYKCHThQQ="}]}, {"Route": "js/reports.js.gz", "AssetFile": "js/reports.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6774"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 18 Nov 2025 14:10:46 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oMYbV5AVYI0xejYZoQHZnI3REekGeYg+bDNc3rpK6pk="}]}, {"Route": "js/settings.js", "AssetFile": "js/settings.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000076068766"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13145"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg=\""}, {"Name": "ETag", "Value": "W/\"LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:41:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU="}]}, {"Route": "js/settings.js", "AssetFile": "js/settings.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "90954"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:40:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU="}]}, {"Route": "js/settings.js.gz", "AssetFile": "js/settings.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13145"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:41:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg="}]}, {"Route": "js/settings.kgsze9z630.js", "AssetFile": "js/settings.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000076068766"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13145"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg=\""}, {"Name": "ETag", "Value": "W/\"LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:41:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kgsze9z630"}, {"Name": "integrity", "Value": "sha256-LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU="}, {"Name": "label", "Value": "js/settings.js"}]}, {"Route": "js/settings.kgsze9z630.js", "AssetFile": "js/settings.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "90954"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:40:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kgsze9z630"}, {"Name": "integrity", "Value": "sha256-LEPouUi0qenCXl3Oxe6EXY+crHhr0tJljs8+eaVn9KU="}, {"Name": "label", "Value": "js/settings.js"}]}, {"Route": "js/settings.kgsze9z630.js.gz", "AssetFile": "js/settings.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "13145"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:41:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kgsze9z630"}, {"Name": "integrity", "Value": "sha256-uc88cmSCVyx4BQE3QaHAfuGDykSaV72SH+Z0KBHEbvg="}, {"Name": "label", "Value": "js/settings.js.gz"}]}, {"Route": "js/suppliers.js", "AssetFile": "js/suppliers.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000415627598"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2405"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE=\""}, {"Name": "ETag", "Value": "W/\"eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:02:04 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso="}]}, {"Route": "js/suppliers.js", "AssetFile": "js/suppliers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "10417"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 18 Nov 2025 15:30:17 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso="}]}, {"Route": "js/suppliers.js.gz", "AssetFile": "js/suppliers.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2405"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:02:04 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE="}]}, {"Route": "js/suppliers.sb2z2q5wz2.js", "AssetFile": "js/suppliers.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000415627598"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2405"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE=\""}, {"Name": "ETag", "Value": "W/\"eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:02:04 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sb2z2q5wz2"}, {"Name": "integrity", "Value": "sha256-eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso="}, {"Name": "label", "Value": "js/suppliers.js"}]}, {"Route": "js/suppliers.sb2z2q5wz2.js", "AssetFile": "js/suppliers.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "10417"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 18 Nov 2025 15:30:17 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sb2z2q5wz2"}, {"Name": "integrity", "Value": "sha256-eM9g8qJqseSo9j4vYUbEBrdPIVvXd/JVkK87clenhso="}, {"Name": "label", "Value": "js/suppliers.js"}]}, {"Route": "js/suppliers.sb2z2q5wz2.js.gz", "AssetFile": "js/suppliers.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2405"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE=\""}, {"Name": "Last-Modified", "Value": "Wed, 19 Nov 2025 17:02:04 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sb2z2q5wz2"}, {"Name": "integrity", "Value": "sha256-G4zjpvKHBa9FT9vBtheUVUNyGnh082JCUyN20sqgGgE="}, {"Name": "label", "Value": "js/suppliers.js.gz"}]}, {"Route": "login.html", "AssetFile": "login.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000487329435"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "ETag", "Value": "W/\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:47:01 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}]}, {"Route": "login.html", "AssetFile": "login.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6732"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:38:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}]}, {"Route": "login.html.gz", "AssetFile": "login.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:47:01 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ="}]}, {"Route": "login.nqonojhrly.html", "AssetFile": "login.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000487329435"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "ETag", "Value": "W/\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:47:01 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}, {"Name": "label", "Value": "login.html"}]}, {"Route": "login.nqonojhrly.html", "AssetFile": "login.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6732"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:38:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-//A/S39TuA5cSKbMbjsl1JRU9b7HIjVN296TaFGyuVQ="}, {"Name": "label", "Value": "login.html"}]}, {"Route": "login.nqonojhrly.html.gz", "AssetFile": "login.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2051"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 15:47:01 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"Name": "integrity", "Value": "sha256-701FY2qeWqjsmnoGZjvaKkz6cNDd6advdrRHWskDBIQ="}, {"Name": "label", "Value": "login.html.gz"}]}, {"Route": "print-invoice.2cv1skr50a.html", "AssetFile": "print-invoice.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000250752257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "ETag", "Value": "W/\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 12:05:28 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cv1skr50a"}, {"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}, {"Name": "label", "Value": "print-invoice.html"}]}, {"Route": "print-invoice.2cv1skr50a.html", "AssetFile": "print-invoice.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "17980"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 19:09:19 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cv1skr50a"}, {"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}, {"Name": "label", "Value": "print-invoice.html"}]}, {"Route": "print-invoice.2cv1skr50a.html.gz", "AssetFile": "print-invoice.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 12:05:28 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2cv1skr50a"}, {"Name": "integrity", "Value": "sha256-bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE="}, {"Name": "label", "Value": "print-invoice.html.gz"}]}, {"Route": "print-invoice.html", "AssetFile": "print-invoice.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000250752257"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "ETag", "Value": "W/\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 12:05:28 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}]}, {"Route": "print-invoice.html", "AssetFile": "print-invoice.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "17980"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0=\""}, {"Name": "Last-Modified", "Value": "Thu, 13 Nov 2025 19:09:19 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4rDJFqzM1/xDgive6hVpr2emaleD2M6REC+V1x2F5n0="}]}, {"Route": "print-invoice.html.gz", "AssetFile": "print-invoice.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "3987"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE=\""}, {"Name": "Last-Modified", "Value": "Fri, 14 Nov 2025 12:05:28 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bY+g8QXg8qqfEq+eFD9G4c8LYOxegqtfR+1wpiu+AiE="}]}]}