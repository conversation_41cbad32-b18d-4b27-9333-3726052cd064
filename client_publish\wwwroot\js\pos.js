let cart = [];
let currentInvoice = {
    type: 'Sale',
    paymentType: 'Cash',
    customerId: null,
    discount: 0,
    paid: 0
};

async function loadPOSPage() {
    const page = document.getElementById('posPage');
    page.innerHTML = `
        <div class="pos-layout row g-3">
            <!-- المنتجات على اليمين -->
            <div class="col-lg-9 pos-right-panel">
                <!-- عنوان نقطة البيع -->
                <div class="card pos-card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center flex-wrap gap-2">
                            <div>
                                <h5 class="pos-title mb-1">
                                    <i class="bi bi-cart-plus"></i> نقطة البيع
                                </h5>
                                <p class="text-muted small mb-0">
                                    اختر الفئة أو ابحث عن منتج ثم أضفه إلى السلة
                                </p>
                            </div>
                            <div class="text-end d-none d-md-block">
                                <span class="badge bg-light text-dark">
                                    <i class="bi bi-clock-history"></i> جاهز لفاتورة جديدة
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الفئات + البحث -->
                <div class="card pos-card mb-3">
                    <div class="card-body">
                        <!-- البحث في الأعلى -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label class="form-label mb-2">
                                    <i class="bi bi-search"></i> بحث سريع عن منتج
                                </label>
                                <div class="search-box">
                                    <input type="text" class="form-control" id="productSearch"
                                           placeholder="ابحث عن منتج (الاسم، الكود، الباركود)..."
                                           autocomplete="off">
                                    <div id="searchResults" class="search-results d-none"></div>
                                </div>
                            </div>
                        </div>

                        <!-- الفئات تحت البحث -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="mb-2 categories-title">
                                    <i class="bi bi-grid-3x3-gap-fill"></i>
                                    <span>فئات المنتجات</span>
                                </h6>
                                <div id="categoriesRow">
                                    <div class="col text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- عرض المنتجات -->
                <div class="card pos-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">
                                <i class="bi bi-box-seam"></i> المنتجات
                            </h6>
                            <small class="text-muted d-none d-sm-inline">
                                انقر على المنتج لإضافته إلى السلة
                            </small>
                        </div>
                        <div id="productsDisplay" class="products-display">
                            <div class="text-center text-muted py-5">
                                <i class="bi bi-box" style="font-size: 3rem;"></i>
                                <p class="mb-0">اختر فئة لعرض المنتجات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- السلة على اليسار -->
            <div class="col-lg-3 pos-left-panel">
                <!-- بيانات الفاتورة -->
                <div class="card pos-card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">
                                <i class="bi bi-receipt"></i> بيانات الفاتورة
                            </h6>
                            <span class="badge bg-light text-dark d-none d-sm-inline">
                                <i class="bi bi-info-circle"></i> اختر نوع الدفع والعميل
                            </span>
                        </div>
                        <div class="row g-2">
                            <div class="col-12">
                                <label class="form-label">
                                    <i class="bi bi-credit-card"></i> نوع الدفع
                                </label>
                                <select class="form-select" id="paymentType" onchange="updatePaymentType()">
                                    <option value="Cash">نقدي</option>
                                    <option value="Credit">آجل</option>
                                    <option value="Wholesale">جملة</option>
                                    <option value="Installment">قسط</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">
                                    <i class="bi bi-person"></i> العميل (اختياري)
                                </label>
                                <select class="form-select form-select-sm" id="customerId">
                                    <option value="">عميل نقدي</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- السلة -->
                <div class="card pos-card mb-3 pos-cart-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0">
                                <i class="bi bi-cart"></i> السلة
                            </h6>
                            <small class="text-muted">
                                <span id="cartCount">0</span> منتج
                            </small>
                        </div>
                        <div id="cartItems" class="cart-items-container">
                            <div class="text-center text-muted py-4">
                                <i class="bi bi-cart-x" style="font-size: 2.5rem;"></i>
                                <p class="mb-0">السلة فارغة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص الفاتورة -->
                <div class="card pos-card pos-summary-card">
                    <div class="card-body">
                        <h6 class="mb-3">
                            <i class="bi bi-calculator"></i> ملخص الفاتورة
                        </h6>

                        <div class="invoice-summary">
                            <div class="summary-row">
                                <span>المجموع الفرعي:</span>
                                <span id="subTotal">0 د.ع</span>
                            </div>
                            <div class="summary-row">
                                <span>الخصم:</span>
                                <input type="number" class="form-control form-control-sm discount-input"
                                       id="discount" placeholder="0" value="0" min="0"
                                       onchange="updateTotals()" oninput="updateTotals()">
                            </div>
                            <div class="summary-row total-row">
                                <span><i class="bi bi-calculator"></i> السعر الكلي:</span>
                                <span class="total-amount" id="total">0 د.ع</span>
                            </div>

                            <div class="summary-row">
                                <span>المستلم:</span>
                                <input type="number" class="form-control form-control-sm received-input-simple"
                                       id="received" placeholder="0" value="0" min="0"
                                       onchange="updateTotals()" oninput="updateTotals()">
                            </div>

                            <div class="summary-row remaining-row-simple" id="remainingRow">
                                <span>الباقي:</span>
                                <span id="remaining" class="remaining-value">0 د.ع</span>
                            </div>
                        </div>

                        <div class="mt-3 pos-actions">
                            <button class="btn btn-success w-100 mb-2" onclick="saveInvoice(false)">
                                <i class="bi bi-check-circle"></i> حفظ الفاتورة
                            </button>
                            <button class="btn btn-primary w-100 mb-2" onclick="saveInvoice(true)">
                                <i class="bi bi-printer"></i> حفظ وطباعة
                            </button>
                            <button class="btn btn-outline-danger w-100" onclick="clearCart()">
                                <i class="bi bi-x-circle"></i> إلغاء الفاتورة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Load customers and categories
    loadCustomers();
    loadPOSCategories();

    // Setup product search
    document.getElementById('productSearch').addEventListener('input', searchProducts);
}

let selectedCategory = null;

async function loadPOSCategories() {
    try {
        const response = await fetch(`${API_URL}/categories`);
        const categories = await response.json();

        const row = document.getElementById('categoriesRow');

        if (categories.length === 0) {
            row.innerHTML = '<div class="col-12 text-center text-muted">لا توجد فئات</div>';
            return;
        }

        row.innerHTML = `
            <div class="category-card ${!selectedCategory ? 'active' : ''}"
                 onclick="filterByCategory(null)">
                <i class="bi bi-grid-fill"></i>
                <div class="category-name">الكل</div>
            </div>
        ` + categories.map(c => `
            <div class="category-card ${selectedCategory === c.name ? 'active' : ''}"
                 onclick="filterByCategory('${c.name}')">
                <i class="bi ${c.icon || 'bi-box-seam'}"></i>
                <div class="category-name">${c.name}</div>
            </div>
        `).join('');
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

function adjustColor(color, amount) {
    const clamp = (val) => Math.min(Math.max(val, 0), 255);
    const num = parseInt(color.replace('#', ''), 16);
    const r = clamp((num >> 16) + amount);
    const g = clamp(((num >> 8) & 0x00FF) + amount);
    const b = clamp((num & 0x0000FF) + amount);
    return '#' + ((r << 16) | (g << 8) | b).toString(16).padStart(6, '0');
}

async function filterByCategory(category) {
    selectedCategory = category;
    loadPOSCategories();

    const searchInput = document.getElementById('productSearch');
    const productsDisplay = document.getElementById('productsDisplay');

    // إذا كان هناك نص بحث، قم بالبحث مع الفئة
    if (searchInput.value.trim().length >= 2) {
        searchProducts({ target: searchInput });
    } else if (category) {
        // إذا تم اختيار فئة بدون بحث، اعرض جميع منتجات الفئة
        await showCategoryProducts(category);
    } else {
        // إذا تم اختيار "الكل"، اعرض جميع المنتجات
        await showAllProducts();
    }
}

async function showCategoryProducts(category) {
    try {
        const response = await fetch(`${API_URL}/products`);
        let products = await response.json();

        // تصفية المنتجات حسب الفئة (مع تنظيف المسافات)
        const normalizedCategory = category.trim();
        products = products.filter(p => {
            const normalizedProductCategory = (p.category || '').trim();
            return normalizedProductCategory === normalizedCategory && p.quantity > 0;
        });

        const productsDisplay = document.getElementById('productsDisplay');

        if (products.length === 0) {
            productsDisplay.innerHTML = '<div class="text-center text-muted py-5"><i class="bi bi-box" style="font-size: 3rem;"></i><p>لا توجد منتجات في هذه الفئة</p></div>';
            return;
        }

        // عرض المنتجات في Grid
        productsDisplay.innerHTML = products.map(p => `
            <div class="product-card-modern" onclick="addToCart(${p.id}, '${p.name.replace(/'/g, "\\'")}', ${p.salePrice})">
                <div class="product-image-modern">
                    ${p.image ?
                        `<img src="${p.image}" alt="${p.name}">` :
                        `<div class="no-image"><i class="bi bi-box-seam"></i></div>`
                    }
                    <span class="stock-badge-modern">${p.quantity}</span>
                </div>
                <div class="product-info-modern">
                    <div class="product-name-modern">${p.name}</div>
                    <div class="product-price-modern">${formatCurrency(p.salePrice)}</div>
                </div>
            </div>
        `).join('');
    } catch (error) {
        console.error('Error loading category products:', error);
    }
}

async function showAllProducts() {
    try {
        const response = await fetch(`${API_URL}/products`);
        let products = await response.json();

        // تصفية المنتجات التي لها كمية متوفرة فقط
        products = products.filter(p => p.quantity > 0);

        const productsDisplay = document.getElementById('productsDisplay');

        if (products.length === 0) {
            productsDisplay.innerHTML = '<div class="text-center text-muted py-5"><i class="bi bi-box" style="font-size: 3rem;"></i><p>لا توجد منتجات متوفرة</p></div>';
            return;
        }

        // عرض المنتجات في Grid الحديث
        productsDisplay.innerHTML = products.map(p => `
            <div class="product-card-modern" onclick="addToCart(${p.id}, '${p.name.replace(/'/g, "\\'")}', ${p.salePrice})">
                <div class="product-image-modern">
                    ${p.image ?
                        `<img src="${p.image}" alt="${p.name}">` :
                        `<div class="no-image"><i class="bi bi-box-seam"></i></div>`
                    }
                    <span class="stock-badge-modern">${p.quantity}</span>
                </div>
                <div class="product-info-modern">
                    <div class="product-name-modern">${p.name}</div>
                    <div class="product-price-modern">${formatCurrency(p.salePrice)}</div>
                </div>
            </div>
        `).join('');
    } catch (error) {
        console.error('Error loading all products:', error);
    }
}

async function loadCustomers() {
    try {
        const response = await fetch(`${API_URL}/customers`);
        const customers = await response.json();
        
        const select = document.getElementById('customerId');
        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('Error loading customers:', error);
    }
}

async function searchProducts(e) {
    const term = e.target.value.trim();
    const resultsDiv = document.getElementById('searchResults');

    if (term.length < 2) {
        resultsDiv.classList.add('d-none');
        return;
    }

    try {
        const response = await fetch(`${API_URL}/products/search?term=${encodeURIComponent(term)}`);
        let products = await response.json();

        // تصفية حسب الفئة المختارة
        if (selectedCategory) {
            products = products.filter(p => p.category === selectedCategory);
        }

        if (products.length === 0) {
            resultsDiv.classList.add('d-none');
            return;
        }

        resultsDiv.innerHTML = products.map(p => `
            <div class="search-result-item" onclick="addToCart(${p.id}, '${p.name}', ${p.salePrice})">
                ${p.image ? `<img src="${p.image}" alt="${p.name}" class="product-thumb me-2">` : ''}
                <div class="flex-grow-1">
                    <strong>${p.name}</strong> - ${p.code}
                    ${p.category ? `<span class="badge bg-info ms-2">${p.category}</span>` : ''}<br>
                    <small>السعر: ${formatCurrency(p.salePrice)} | المخزون: ${p.quantity}</small>
                </div>
            </div>
        `).join('');

        resultsDiv.classList.remove('d-none');
    } catch (error) {
        console.error('Error searching products:', error);
    }
}

function addToCart(productId, productName, price) {
    const existingItem = cart.find(item => item.productId === productId);

    if (existingItem) {
        existingItem.quantity++;
        existingItem.total = existingItem.quantity * existingItem.unitPrice;
    } else {
        cart.push({
            productId,
            productName,
            quantity: 1,
            unitPrice: price,
            discount: 0,
            total: price
        });
    }

    document.getElementById('productSearch').value = '';
    document.getElementById('searchResults').classList.add('d-none');

    renderCart();
    updateTotals();
}

function renderCart() {
    const cartDiv = document.getElementById('cartItems');
    const cartCountEl = document.getElementById('cartCount');
    if (cartCountEl) {
        // إجمالي عدد القطع في السلة (مجموع الكميات)، وليس عدد الأسطر فقط
        const totalItems = cart.reduce((sum, item) => sum + (item.quantity || 0), 0);
        cartCountEl.textContent = totalItems;
    }

    if (cart.length === 0) {
        cartDiv.innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="bi bi-cart-x" style="font-size: 3rem;"></i>
                <p>السلة فارغة</p>
            </div>
        `;
        return;
    }

    cartDiv.innerHTML = cart.map((item, index) => `
        <div class="cart-item-modern">
            <div class="cart-item-header-modern">
                <div class="cart-item-name-modern">${item.productName}</div>
                <button class="cart-item-remove-modern" onclick="removeFromCart(${index})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
            <div class="cart-item-body-modern">
                <div class="cart-item-qty-modern">
                    <button class="qty-btn-modern" onclick="decreaseQuantity(${index})">
                        <i class="bi bi-dash"></i>
                    </button>
                    <input type="number" class="qty-input-modern"
                           value="${item.quantity}" min="1"
                           onchange="updateQuantity(${index}, this.value)">
                    <button class="qty-btn-modern" onclick="increaseQuantity(${index})">
                        <i class="bi bi-plus"></i>
                    </button>
                </div>
                <div class="cart-item-price-modern">
                    <div class="unit-price-modern">${formatCurrency(item.unitPrice)}</div>
                    <div class="total-price-modern">${formatCurrency(item.total)}</div>
                </div>
            </div>
        </div>
    `).join('');
}

function updateQuantity(index, quantity) {
    cart[index].quantity = parseFloat(quantity);
    cart[index].total = cart[index].quantity * cart[index].unitPrice - cart[index].discount;
    renderCart();
    updateTotals();
}

function updatePrice(index, price) {
    cart[index].unitPrice = parseFloat(price);
    cart[index].total = cart[index].quantity * cart[index].unitPrice - cart[index].discount;
    renderCart();
    updateTotals();
}

function increaseQuantity(index) {
    cart[index].quantity++;
    cart[index].total = cart[index].quantity * cart[index].unitPrice - cart[index].discount;
    renderCart();
    updateTotals();
}

function decreaseQuantity(index) {
    if (cart[index].quantity > 1) {
        cart[index].quantity--;
        cart[index].total = cart[index].quantity * cart[index].unitPrice - cart[index].discount;
        renderCart();
        updateTotals();
    }
}

function removeFromCart(index) {
    cart.splice(index, 1);
    renderCart();
    updateTotals();
}

function updateTotals() {
    const subTotal = cart.reduce((sum, item) => sum + item.total, 0);
    const discount = parseFloat(document.getElementById('discount')?.value || 0);
    const total = subTotal - discount;
    const received = parseFloat(document.getElementById('received')?.value || 0);
    const remaining = received - total; // الباقي = المستلم - السعر الكلي

    document.getElementById('subTotal').textContent = formatCurrency(subTotal);
    document.getElementById('total').textContent = formatCurrency(total);

    const remainingEl = document.getElementById('remaining');
    const remainingRow = document.getElementById('remainingRow');

    if (!remainingEl || !remainingRow) return;

    // إذا لم يُكتب أي مبلغ في المستلم: لا نعرض شيئاً في سطر الباقي
    if (!received || received === 0) {
        remainingEl.textContent = '';
        remainingEl.style.color = '#6c757d';
        remainingRow.style.backgroundColor = 'transparent';
        return;
    }

    // عند وجود مبلغ مستلم نعرض "سعر المستلم" + "الباقي"
    if (remaining >= 0) {
        // المستلم أكبر أو يساوي السعر الكلي => يوجد باقي للعميل أو صفر
        remainingEl.textContent = `سعر المستلم: ${formatCurrency(received)} - الباقي: ${formatCurrency(remaining)}`;
        remainingEl.style.color = '#198754';
        remainingRow.style.backgroundColor = '#d1f4e0';
    } else {
        // المستلم أقل من السعر الكلي => ما زال متبقي على العميل
        const diff = Math.abs(remaining);
        remainingEl.textContent = `سعر المستلم: ${formatCurrency(received)} - الباقي: ${formatCurrency(diff)}`;
        remainingEl.style.color = '#dc3545';
        remainingRow.style.backgroundColor = '#f8d7da';
    }
}

function updatePaymentType() {
    currentInvoice.paymentType = document.getElementById('paymentType').value;
}

function clearCart() {
    if (confirm('هل تريد إلغاء الفاتورة؟')) {
        cart = [];
        renderCart();
        updateTotals();
        document.getElementById('productSearch').value = '';
        document.getElementById('customerId').value = '';
        document.getElementById('discount').value = '0';
        document.getElementById('received').value = '0';
    }
}

async function saveInvoice(printAfterSave = false) {
    if (cart.length === 0) {
        showToast('السلة فارغة', 'warning');
        return;
    }

    const subTotal = cart.reduce((sum, item) => sum + item.total, 0);
    const discount = parseFloat(document.getElementById('discount')?.value || 0);
    const total = subTotal - discount;
    const received = parseFloat(document.getElementById('received')?.value || 0);
    const paymentType = document.getElementById('paymentType')?.value || 'Cash';

    // حقل "المستلم" فقط لحساب الباقي أمام الكاشير ولا يؤثر على التقارير
    // في قاعدة البيانات نعتبر الفاتورة مدفوعة بالكامل بقيمة المطلوب
    const paid = total;

    const customerSelect = document.getElementById('customerId');
    const customerIdValue = customerSelect?.value;
    const customerName = customerSelect?.selectedOptions[0]?.text || 'عميل نقدي';

    const invoice = {
        type: 'Sale',
        paymentType: paymentType,
        customerId: customerIdValue ? parseInt(customerIdValue) : null,
        discount: discount,
        tax: 0,
        paid: paid,
        createdBy: currentUser?.id || 1,
        items: cart.map(item => ({
            productId: item.productId,
            productName: item.productName,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            discount: item.discount || 0,
            total: item.total
        }))
    };

    try {
        const response = await fetch(`${API_URL}/invoices`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(invoice)
        });

        if (response.ok) {
            const result = await response.json();

            // حفظ بيانات الفاتورة للطباعة قبل تفريغ السلة
            const invoiceData = {
                id: result.id,
                invoiceNumber: result.invoiceNumber,
                date: new Date().toLocaleDateString('ar-IQ'),
                time: new Date().toLocaleTimeString('ar-IQ'),
                items: [...cart], // نسخة من السلة
                subTotal: subTotal,
                discount: discount,
                total: total,
                received: received,
                remaining: received - total,
                paymentType: paymentType,
                customerName: customerName
            };

            // تفريغ السلة وبدء فاتورة جديدة
            clearCart();

            showToast('✅ تم حفظ الفاتورة بنجاح - رقم: ' + result.invoiceNumber, 'success');

            // طباعة إذا طُلب ذلك
            if (printAfterSave) {
                setTimeout(() => printInvoice(invoiceData), 300);
            }
        } else {
            const error = await response.text();
            console.error('Server error:', error);
            showToast('❌ فشل حفظ الفاتورة', 'danger');
        }
    } catch (error) {
        console.error('Error saving invoice:', error);
        showToast('❌ حدث خطأ في حفظ الفاتورة: ' + error.message, 'danger');
    }
}

function clearCart() {
    // تفريغ السلة وبدء فاتورة جديدة
    cart = [];

    // إعادة تعيين جميع الحقول
    const discountInput = document.getElementById('discount');
    const receivedInput = document.getElementById('received');
    const customerSelect = document.getElementById('customerId');

    if (discountInput) discountInput.value = 0;
    if (receivedInput) receivedInput.value = 0;
    if (customerSelect) customerSelect.value = '';

    // تحديث العرض
    renderCart();
    updateTotals();

    console.log('✅ تم تفريغ السلة وبدء فاتورة جديدة');
}

function printInvoice(invoiceData) {
    // الطباعة دائماً عبر المتصفح (Web Print) بعرض 80mm
    printToBrowser(invoiceData);
}

function printToBrowser(invoiceData) {
    console.log('🖨️ طباعة فاتورة - استخدام الملف الجديد print-invoice.html');
    console.log('📄 بيانات الفاتورة:', invoiceData);

    // تحميل الإعدادات العامة
    const settings = JSON.parse(localStorage.getItem('settings') || '{}');

    // تحميل إعدادات الطباعة
    const printerSettings = JSON.parse(localStorage.getItem('printerSettings') || '{}');

    // إضافة معلومات الشركة وإعدادات الطباعة للبيانات
    const printData = {
        ...invoiceData,
        companyName: settings.companyName || 'أريدوو - Aredoo',
        companyAddress: settings.companyAddress || settings.address || 'العنوان',
        companyPhone: settings.companyPhone || settings.phone || 'رقم الهاتف',
        // إعدادات الطباعة
        paperWidth: printerSettings.paperWidth || '80',
        fontSize: printerSettings.fontSize || 'medium',
        fontFamily: printerSettings.fontFamily || 'Cairo',
        headerColor: printerSettings.headerColor || '#667eea',
        printLogo: printerSettings.printLogo !== false,
        printBarcode: printerSettings.printBarcode !== false,
        printHeader: printerSettings.printHeader !== false,
        printFooter: printerSettings.printFooter !== false,
        marginTop: printerSettings.marginTop || '5',
        marginBottom: printerSettings.marginBottom || '5',
        marginLeft: printerSettings.marginLeft || '5',
        marginRight: printerSettings.marginRight || '5'
    };

    // تحويل البيانات إلى JSON وترميزها
    const dataString = encodeURIComponent(JSON.stringify(printData));

    // فتح صفحة الطباعة الجديدة
    window.open(`print-invoice.html?data=${dataString}`, '_blank', 'width=800,height=600');
}

// طباعة عبر طابعة الشبكة (IP)
async function printToNetworkPrinter(invoiceData, settings) {
    const ip = settings.ip;
    const port = settings.port || 9100;

    if (!ip) {
        showToast('❌ لم يتم تكوين عنوان IP للطابعة', 'danger');
        return;
    }

    try {
        // إنشاء نص الفاتورة بصيغة ESC/POS
        const escPosData = generateESCPOS(invoiceData, settings);

        // إرسال البيانات للطابعة
        const response = await fetch(`http://${ip}:${port}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/octet-stream'
            },
            body: escPosData
        });

        if (response.ok) {
            showToast('✅ تم إرسال الفاتورة للطابعة', 'success');
        } else {
            throw new Error('فشل الاتصال بالطابعة');
        }
    } catch (error) {
        console.error('Print error:', error);
        showToast('❌ فشل الطباعة. جرب الطباعة عبر المتصفح', 'danger');
        // الرجوع للطباعة عبر المتصفح
        printToBrowser(invoiceData);
    }
}

// طباعة عبر USB
async function printToUSBPrinter(invoiceData, settings) {
    if (!navigator.usb) {
        showToast('❌ المتصفح لا يدعم USB. استخدم Chrome أو Edge', 'danger');
        printToBrowser(invoiceData);
        return;
    }

    try {
        // طلب الوصول لجهاز USB
        const device = await navigator.usb.requestDevice({
            filters: settings.usbVendorId ? [{
                vendorId: parseInt(settings.usbVendorId)
            }] : []
        });

        await device.open();
        await device.selectConfiguration(1);
        await device.claimInterface(0);

        // إنشاء بيانات ESC/POS
        const escPosData = generateESCPOS(invoiceData, settings);

        // إرسال البيانات
        await device.transferOut(1, escPosData);

        await device.close();

        showToast('✅ تم الطباعة بنجاح', 'success');
    } catch (error) {
        console.error('USB Print error:', error);
        showToast('❌ فشل الطباعة عبر USB. جرب الطباعة عبر المتصفح', 'danger');
        printToBrowser(invoiceData);
    }
}

// طباعة عبر البلوتوث
async function printToBluetoothPrinter(invoiceData, settings) {
    if (!navigator.bluetooth) {
        showToast('❌ المتصفح لا يدعم البلوتوث. استخدم Chrome أو Edge', 'danger');
        printToBrowser(invoiceData);
        return;
    }

    try {
        // البحث عن الطابعة
        const device = await navigator.bluetooth.requestDevice({
            filters: settings.bluetoothName ? [{
                name: settings.bluetoothName
            }] : [],
            optionalServices: ['000018f0-0000-1000-8000-00805f9b34fb']
        });

        const server = await device.gatt.connect();
        const service = await server.getPrimaryService('000018f0-0000-1000-8000-00805f9b34fb');
        const characteristic = await service.getCharacteristic('00002af1-0000-1000-8000-00805f9b34fb');

        // إنشاء بيانات ESC/POS
        const escPosData = generateESCPOS(invoiceData, settings);

        // إرسال البيانات
        await characteristic.writeValue(escPosData);

        await device.gatt.disconnect();

        showToast('✅ تم الطباعة بنجاح', 'success');
    } catch (error) {
        console.error('Bluetooth Print error:', error);
        showToast('❌ فشل الطباعة عبر البلوتوث. جرب الطباعة عبر المتصفح', 'danger');
        printToBrowser(invoiceData);
    }
}

// إنشاء بيانات ESC/POS للطابعات الحرارية
function generateESCPOS(invoiceData, settings) {
    const encoder = new TextEncoder();
    const ESC = 0x1B;
    const GS = 0x1D;

    let commands = [];

    // تهيئة الطابعة
    commands.push(ESC, 0x40);

    // محاذاة للوسط
    commands.push(ESC, 0x61, 0x01);

    // خط كبير وعريض
    commands.push(ESC, 0x21, 0x30);
    commands.push(...encoder.encode('نظام أريدوو\n'));
    commands.push(ESC, 0x21, 0x00);

    commands.push(...encoder.encode('فاتورة بيع\n'));
    commands.push(...encoder.encode(`رقم: ${invoiceData.invoiceNumber}\n`));
    commands.push(...encoder.encode(`التاريخ: ${invoiceData.date}\n`));
    commands.push(...encoder.encode(`الوقت: ${invoiceData.time}\n`));
    commands.push(...encoder.encode('--------------------------------\n'));

    // محاذاة لليمين
    commands.push(ESC, 0x61, 0x02);

    // المنتجات
    invoiceData.items.forEach(item => {
        commands.push(...encoder.encode(`${item.name}\n`));
        commands.push(...encoder.encode(`${item.quantity} × ${formatCurrency(item.price)} = ${formatCurrency(item.total)}\n`));
    });

    commands.push(...encoder.encode('--------------------------------\n'));

    // المجاميع
    commands.push(...encoder.encode(`المجموع: ${formatCurrency(invoiceData.subTotal)}\n`));
    if (invoiceData.discount > 0) {
        commands.push(...encoder.encode(`الخصم: ${formatCurrency(invoiceData.discount)}\n`));
    }

    // خط عريض للمطلوب
    commands.push(ESC, 0x21, 0x20);
    commands.push(...encoder.encode(`المطلوب: ${formatCurrency(invoiceData.total)}\n`));
    commands.push(ESC, 0x21, 0x00);

    commands.push(...encoder.encode(`المستلم: ${formatCurrency(invoiceData.received)}\n`));
    commands.push(...encoder.encode(`الباقي: ${formatCurrency(invoiceData.remaining)}\n`));

    commands.push(...encoder.encode('--------------------------------\n'));

    // محاذاة للوسط
    commands.push(ESC, 0x61, 0x01);
    commands.push(...encoder.encode('شكراً لزيارتكم\n'));

    // قص الورق
    commands.push(GS, 0x56, 0x00);

    return new Uint8Array(commands);
}
