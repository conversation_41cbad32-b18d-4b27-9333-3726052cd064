package com.aredoo.pos.repository;

import com.aredoo.pos.model.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    
    List<Product> findByIsActiveTrue();
    
    List<Product> findByIsActiveTrueOrderByName();
    
    Optional<Product> findByCode(String code);
    
    Optional<Product> findByBarcode(String barcode);
    
    List<Product> findByCategory(String category);
    
    List<Product> findByCategoryAndIsActiveTrue(String category);
    
    @Query("SELECT p FROM Product p WHERE p.isActive = true AND " +
           "(LOWER(p.name) LIKE LOWER(CONCAT('%', :term, '%')) OR " +
           "LOWER(p.nameAr) LIKE LOWER(CONCAT('%', :term, '%')) OR " +
           "LOWER(p.code) LIKE LOWER(CONCAT('%', :term, '%')) OR " +
           "LOWER(p.barcode) LIKE LOWER(CONCAT('%', :term, '%')))")
    List<Product> searchProducts(@Param("term") String term);
    
    @Query("SELECT p FROM Product p WHERE p.quantity <= p.minQuantity AND p.isActive = true")
    List<Product> findLowStockProducts();
    
    @Query("SELECT DISTINCT p.category FROM Product p WHERE p.isActive = true ORDER BY p.category")
    List<String> findAllCategories();
    
    @Query("SELECT COUNT(p) FROM Product p WHERE p.isActive = true")
    long countActiveProducts();
    
    @Query("SELECT SUM(p.quantity * p.purchasePrice) FROM Product p WHERE p.isActive = true")
    Double getTotalInventoryValue();
}
