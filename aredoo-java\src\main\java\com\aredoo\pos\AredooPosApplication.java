package com.aredoo.pos;

import com.aredoo.pos.model.User;
import com.aredoo.pos.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.awt.Desktop;
import java.net.URI;
import java.util.Optional;

@SpringBootApplication
public class AredooPosApplication implements CommandLineRunner {

    @Autowired
    private UserRepository userRepository;
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    public static void main(String[] args) {
        System.out.println("═══════════════════════════════════════════════════════");
        System.out.println("  أريدوو - Aredoo POS System");
        System.out.println("  نظام إدارة المبيعات والمخزون");
        System.out.println("═══════════════════════════════════════════════════════");
        System.out.println("  🌐 العنوان: http://localhost:5000");
        System.out.println("  📁 قاعدة البيانات: data/aredoo.mv.db");
        System.out.println("  👤 المستخدم الافتراضي: admin");
        System.out.println("  🔑 كلمة المرور: 1234");
        System.out.println("═══════════════════════════════════════════════════════");
        System.out.println("  اضغط Ctrl+C للإيقاف");
        System.out.println("═══════════════════════════════════════════════════════");
        
        SpringApplication app = new SpringApplication(AredooPosApplication.class);
        app.run(args);
        
        // Auto-open browser
        try {
            if (Desktop.isDesktopSupported()) {
                Desktop.getDesktop().browse(new URI("http://localhost:5000"));
                System.out.println("✓ تم فتح المتصفح على http://localhost:5000");
            }
        } catch (Exception e) {
            System.out.println("تنبيه: لم يتم فتح المتصفح تلقائياً. افتح http://localhost:5000 يدوياً");
            System.out.println("الخطأ: " + e.getMessage());
        }
    }

    @Override
    public void run(String... args) throws Exception {
        // Initialize default admin user if not exists
        initializeDefaultUser();
    }

    private void initializeDefaultUser() {
        Optional<User> adminUser = userRepository.findByUsername("admin");
        
        if (adminUser.isEmpty()) {
            User defaultAdmin = new User();
            defaultAdmin.setUsername("admin");
            defaultAdmin.setPassword(passwordEncoder.encode("1234"));
            defaultAdmin.setFullName("مدير النظام");
            defaultAdmin.setRole("Admin");
            defaultAdmin.setEmail("<EMAIL>");
            defaultAdmin.setIsActive(true);
            
            userRepository.save(defaultAdmin);
            System.out.println("✓ تم إنشاء المستخدم الافتراضي: admin / 1234");
        }
        
        // Create sample cashier user
        Optional<User> cashierUser = userRepository.findByUsername("cashier");
        if (cashierUser.isEmpty()) {
            User defaultCashier = new User();
            defaultCashier.setUsername("cashier");
            defaultCashier.setPassword(passwordEncoder.encode("1234"));
            defaultCashier.setFullName("أمين الصندوق");
            defaultCashier.setRole("Cashier");
            defaultCashier.setEmail("<EMAIL>");
            defaultCashier.setIsActive(true);
            
            userRepository.save(defaultCashier);
            System.out.println("✓ تم إنشاء مستخدم أمين الصندوق: cashier / 1234");
        }
    }
}
