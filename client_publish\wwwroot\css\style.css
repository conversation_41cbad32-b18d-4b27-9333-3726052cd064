@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

* {
    font-family: 'Cairo', sans-serif;
}

body {
    font-size: 15px; /* تكبير بسيط للخط لتحسين القراءة داخل نقطة البيع */
}

.app-body {
    min-height: 100vh;
    background: radial-gradient(circle at top left, #e3f2fd 0%, #f8f9fa 45%, #eae2ff 100%);
    background-attachment: fixed;
}

.page-content {
    animation: fadeIn 0.3s;
    background-color: #ffffff;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(15, 23, 42, 0.08);
    padding: 1.25rem;
    margin-bottom: 1.5rem;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.btn {
    border-radius: 5px;
    padding: 8px 20px;
}

.table {
    font-size: 13px;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

/* Customer Modal Styles */
#customerModal .modal-dialog {
    max-height: 90vh;
    margin: 1.75rem auto;
}

#customerModal .modal-content {
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

#customerModal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    flex-shrink: 0;
}

#customerModal .modal-body {
    overflow-y: auto;
    max-height: calc(90vh - 120px);
    flex: 1 1 auto;
}

#customerModal .modal-footer {
    flex-shrink: 0;
}

#customerModal h6 {
    color: #667eea;
    font-weight: 600;
}

#customerModal .form-label {
    font-weight: 500;
    color: #495057;
}

#customerModal .form-label i {
    color: #667eea;
    margin-left: 5px;
}

#customerModal .form-control:focus,
#customerModal .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

#customerModal small.text-muted {
    font-size: 0.75rem;
}

/* Product Modal Styles */
#productModal .modal-dialog {
    max-height: 90vh;
    margin: 1.75rem auto;
}

#productModal .modal-content {
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

#productModal .modal-body {
    overflow-y: auto;
    max-height: calc(90vh - 120px);
    flex: 1 1 auto;
}

#productModal .modal-footer {
    flex-shrink: 0;
}

/* Category Modal Styles */
#categoryModal .modal-dialog {
    max-height: 90vh;
    margin: 1.75rem auto;
}

#categoryModal .modal-content {
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

#categoryModal .modal-body {
    overflow-y: auto;
    max-height: calc(90vh - 120px);
    flex: 1 1 auto;
}

#categoryModal .modal-footer {
    flex-shrink: 0;
}

/* جميع النوافذ المنبثقة */
.modal-dialog {
    max-height: 90vh;
}

.modal-content {
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.modal-body {
    overflow-y: auto;
    flex: 1 1 auto;
}

.modal-header,
.modal-footer {
    flex-shrink: 0;
}

/* منطقة عرض المنتجات */
.products-display {
    min-height: 50vh;
    max-height: 60vh;
    overflow-y: auto;
}

/* شبكة عرض المنتجات حسب الفئة - شبكة مربعات */
.category-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.75rem;
    padding: 0.5rem;
    max-height: 70vh;
    overflow-y: auto;
}

/* حاوية السلة */
.cart-items-container {
    max-height: 35vh;
    overflow-y: auto;
}

/* تجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .products-display {
        min-height: 40vh;
        max-height: 50vh;
    }

    .cart-items-container {
        max-height: 30vh;
    }

    .category-products-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }
}

/* تجاوب مع الشاشات الكبيرة */
@media (min-width: 1400px) {
    .products-display {
        min-height: 55vh;
        max-height: 65vh;
    }

    .cart-items-container {
        max-height: 40vh;
    }
}

/* بطاقة المنتج - شبكة مربعات */
.category-products-grid .product-card {
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
}

.category-products-grid .product-card:hover {
    border-color: #0d6efd;
    box-shadow: 0 4px 12px rgba(13,110,253,0.25);
    transform: translateY(-3px);
}

/* صورة المنتج - مربع كبير */
.category-products-grid .product-card-image {
    position: relative;
    width: 100%;
    padding-bottom: 100%;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    overflow: hidden;
}

.category-products-grid .product-card-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.category-products-grid .product-card-no-image {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 3rem;
    color: #dee2e6;
}

/* شارة المنتج */
.category-products-grid .product-badge {
    position: absolute;
    top: 0.3rem;
    right: 0.3rem;
    background: #dc3545;
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.65rem;
    font-weight: 600;
    z-index: 1;
}

/* محتوى البطاقة - عمودي */
.category-products-grid .product-card-body {
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    background: white;
}

.category-products-grid .product-card-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.category-products-grid .product-card-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #212529;
    margin: 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 2.6em;
}

.category-products-grid .product-card-code {
    font-size: 0.75rem;
    color: #6c757d;
}

.category-products-grid .product-card-price {
    font-size: 1.15rem;
    font-weight: 700;
    color: #198754;
    text-align: center;
    padding: 0.5rem;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-radius: 6px;
    margin-top: auto;
}

.category-products-grid .product-card-stock {
    font-size: 0.75rem;
    color: #495057;
    text-align: center;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    white-space: nowrap;
    min-width: 80px;
    padding: 0.5rem 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    font-weight: 600;
}

.category-products-grid .product-card-stock i {
    color: #0d6efd;
    font-size: 0.9rem;
}

/* تجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .category-products-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }

    .category-products-grid .product-card-body {
        padding: 0.5rem;
    }

    .category-products-grid .product-card-image {
        padding-bottom: 70%;
    }
}

/* تجاوب مع الشاشات الكبيرة جداً */
@media (min-width: 1920px) {
    .category-products-grid .product-card-image {
        padding-bottom: 80%;
    }
}

.invoice-item {
    padding: 0.35rem 0.2rem;
    border-bottom: 1px solid #f1f3f5;
    font-size: 0.9rem; /* توحيد قياس النص داخل السلة */
}


/* تنسيق أرقام الكمية والسعر والإجمالي في السلة */
.pos-cart-card .invoice-item .col-2,
.pos-cart-card .invoice-item .col-4 {
    display: flex;
    align-items: center;
}

.pos-cart-card .invoice-item input.form-control-sm {
    font-size: 0.85rem;
    padding-top: 0.15rem;
    padding-bottom: 0.15rem;
    text-align: center;
}

.pos-cart-card .invoice-item strong {
    font-size: 0.9rem;
}

.pos-cart-card .invoice-item .col-2:nth-child(4) strong {
    font-size: 0.95rem; /* إجمالي السطر أوضح قليلاً */
}

.pos-cart-card .invoice-item .btn.btn-sm {
    padding: 0.15rem 0.4rem;
    font-size: 0.8rem;
}

.invoice-item:hover {
    background-color: #f8f9fa;
}

.total-display {
    font-size: 2rem;
    font-weight: 700;
    color: #198754;
}



.discount-input {
    width: 120px;
    text-align: center;
    font-weight: 600;
    border: 2px solid #dee2e6;
    border-radius: 8px;
}

.discount-input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13,110,253,0.25);
}

/* حقل المبلغ المستلم البسيط */
.received-input-simple {
    width: 120px;
    text-align: center;
    font-weight: 600;
    border: 2px solid #0d6efd;
    border-radius: 6px;
}

.received-input-simple:focus {
    border-color: #0a58ca;
    box-shadow: 0 0 0 0.2rem rgba(13,110,253,0.25);
}

/* صف الباقي البسيط */
.remaining-row-simple {
    background-color: #e9ecef;
    font-weight: 600;
    border-radius: 6px;
    margin-top: 8px;
    padding: 10px 15px !important;
    transition: all 0.3s;
}

.remaining-value {
    font-size: 1.1rem;
    font-weight: 700;
}

/* صور المنتجات */
.product-thumb {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.product-thumb-placeholder {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
    color: #adb5bd;
}

.product-image-upload {
    position: relative;
}

.product-image-preview {
    width: 200px;
    height: 200px;
    object-fit: cover;
    border-radius: 12px;
    border: 3px solid #0d6efd;
    margin-bottom: 10px;
}

.product-image-placeholder {
    width: 200px;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 12px;
    border: 3px dashed #dee2e6;
    cursor: pointer;
    transition: all 0.3s;
}

.product-image-placeholder:hover {
    border-color: #0d6efd;
    background-color: #e7f1ff;
}

/* بطاقات الفئات في نقطة البيع - تصميم محسن */
.category-card {
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(255,255,255,0.2);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 0.8rem 1rem;
    text-align: center;
    margin-bottom: 0;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

/* تأثير الإضاءة للفئات العادية */
.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    transform: translateY(-3px) scale(1.03);
    box-shadow: 0 8px 20px rgba(0,0,0,0.25);
    border-color: rgba(255,255,255,0.4);
}

.category-card.active {
    border-color: #ffc107;
    box-shadow: 0 6px 20px rgba(255,193,7,0.4);
    transform: translateY(-2px) scale(1.05);
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%) !important;
}

.category-card i {
    font-size: clamp(1.4rem, 3vw, 1.6rem);
    margin-bottom: 0.4rem;
    display: block;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.category-card .category-name {
    font-weight: 600;
    font-size: clamp(0.8rem, 1.8vw, 0.9rem);
    line-height: 1.2;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    letter-spacing: 0.3px;
}

/* تجاوب الفئات مع الشاشات المختلفة */
@media (max-width: 992px) {
    #categoriesRow {
        padding: 0.6rem;
        gap: 0.4rem;
    }

    #categoriesRow .category-card {
        padding: 0.5rem 0.8rem;
        min-width: 80px;
    }
}

@media (max-width: 768px) {
    #categoriesRow {
        padding: 0.5rem;
        gap: 0.3rem;
        border-radius: 10px;
    }

    #categoriesRow .category-card {
        padding: 0.4rem 0.6rem;
        min-width: 75px;
        border-radius: 8px;
    }

    #categoriesRow .category-card i {
        font-size: 1.1rem;
        margin-bottom: 0.2rem;
    }

    #categoriesRow .category-card .category-name {
        font-size: 0.65rem;
    }
}

@media (max-width: 576px) {
    #categoriesRow {
        padding: 0.4rem;
        gap: 0.25rem;
    }

    #categoriesRow .category-card {
        padding: 0.3rem 0.5rem;
        min-width: 70px;
        border-radius: 6px;
    }

    #categoriesRow .category-card i {
        font-size: 1rem;
        margin-bottom: 0.15rem;
    }

    #categoriesRow .category-card .category-name {
        font-size: 0.6rem;
        letter-spacing: 0.2px;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
    #categoriesRow {
        padding: 1.5rem;
        gap: 1rem;
        border-radius: 25px;
    }

    #categoriesRow .category-card {
        padding: 1.2rem 1.8rem;
        min-width: 140px;
        border-radius: 22px;
    }

    #categoriesRow .category-card i {
        font-size: 2.2rem;
        margin-bottom: 0.8rem;
    }

    #categoriesRow .category-card .category-name {
        font-size: 1rem;
        letter-spacing: 1px;
    }
}

@media (min-width: 1400px) {
    #categoriesRow {
        padding: 2rem;
        gap: 1.2rem;
    }

    #categoriesRow .category-card {
        padding: 1.4rem 2rem;
        min-width: 160px;
        border-radius: 25px;
    }

    #categoriesRow .category-card i {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    #categoriesRow .category-card .category-name {
        font-size: 1.1rem;
        letter-spacing: 1.2px;
    }
}

#received {
    border: 2px solid #0d6efd;
    background-color: #e7f3ff;
}

#received:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.badge-status {
    padding: 5px 10px;
    border-radius: 5px;
}

.search-box {
    position: relative;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    max-height: 350px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    margin-top: 5px;
}

.search-result-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
}

.stat-card h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.stat-card p {
    margin: 0;
    opacity: 0.9;
}

.print-area {
    display: none;
}

@media print {
    body * {
        visibility: hidden;
    }
    .print-area, .print-area * {
        visibility: visible;
    }
    .print-area {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
}

.low-stock {
    background-color: #fff3cd;
}

.negative-stock {
    background-color: #f8d7da;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.modal-header {
    background-color: #0d6efd;
    color: white;
}

.modal-header .btn-close {
    filter: invert(1);
}

.quick-action-btn {
    width: 100%;
    padding: 15px;
    font-size: 1.1rem;
    margin-bottom: 10px;
}

/* Invoice details styles */
.invoice-details-row {
    background-color: #f8f9fa;
}

.invoice-details-container {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.invoice-row:hover {
    background-color: #f8f9fa;
}

.invoice-row .btn-link {
    text-decoration: none;
    color: #0d6efd;
}

.invoice-row .btn-link:hover {
    color: #0a58ca;
}

.invoice-row .bi-chevron-left,
.invoice-row .bi-chevron-down {
    transition: transform 0.3s ease;
}

.invoice-details-container .card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.invoice-details-container .card-header {
    font-weight: 600;
}

.invoice-details-container .table-sm td,
.invoice-details-container .table-sm th {
    padding: 0.5rem;
    font-size: 0.9rem;
}


/* App layout & navbar */
.app-navbar {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 50%, #0d6efd 100%);
    backdrop-filter: blur(6px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    position: relative;
    z-index: 1000;
}

.app-navbar .navbar-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    letter-spacing: 0.02em;
}

.app-navbar .navbar-brand i {
    font-size: 1.6rem;
}

/* POS page – clean light workspace design */
.pos-layout {
    margin-top: 0.75rem;
    padding: 1.1rem 1.2rem;
    border-radius: 18px;
    background: linear-gradient(135deg, #f9fafb 0%, #eef2ff 40%, #f9fafb 100%);
    border: 1px solid #e5e7eb;
    align-items: flex-start;
}

.pos-right-panel,
.pos-left-panel {
    padding: 0.4rem;
}

.pos-right-panel .pos-card,
.pos-left-panel .pos-card {
    border-radius: 16px;
    border: 1px solid #e5e7eb;
    background-color: #ffffff;
    box-shadow: 0 14px 30px rgba(15, 23, 42, 0.06);
}

.pos-right-panel .pos-card + .pos-card {
    margin-top: 0.75rem;
}

.pos-title {
    font-weight: 700;
    letter-spacing: 0.02em;
    color: #0f172a;
    font-size: 1.15rem;
}

.pos-title i {
    color: #0d6efd;
    margin-left: 0.4rem;
}

.pos-right-panel h6,
.pos-left-panel h6 {
    color: #111827;
    font-size: 0.95rem;
}

.pos-right-panel .text-muted,
.pos-left-panel .text-muted {
    color: #6b7280 !important;
}

/* Categories stripe */
#categoriesRow {
    margin-top: 0.25rem;
    padding: 0.35rem 0.15rem;
    border-radius: 999px;
    background-color: #f3f4f6;
    overflow-x: auto;
    white-space: nowrap;
}

/* عنوان الفئات */
.categories-title {
    color: #2d3748;
    font-weight: 700;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.categories-title i {
    color: #667eea;
    font-size: 1.3rem;
    filter: drop-shadow(0 2px 4px rgba(102,126,234,0.3));
}

.categories-title span {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تصميم الفئات المحسن - أكثر تناسقاً */
#categoriesRow {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.8rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 15px;
    margin-bottom: 1rem;
    box-shadow:
        inset 0 1px 3px rgba(0,0,0,0.06),
        0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid rgba(255,255,255,0.8);
    position: relative;
    overflow: hidden;
}

/* تأثير الخلفية المتحركة */
#categoriesRow::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
    pointer-events: none;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

#categoriesRow .category-card {
    position: relative;
    border-radius: 12px;
    padding: 0.6rem 1rem;
    min-width: 85px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.12);
    border: 1px solid rgba(255,255,255,0.2);
    font-size: 0.8rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    overflow: hidden;
    background: #0d6efd;
    color: white;
}

/* تأثير الإضاءة */
#categoriesRow .category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

#categoriesRow .category-card:hover::before {
    left: 100%;
}

#categoriesRow .category-card:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 4px 15px rgba(0,0,0,0.2),
        0 0 10px rgba(255,255,255,0.3) inset;
    border-color: rgba(255,255,255,0.6);
}

#categoriesRow .category-card.active {
    transform: translateY(-1px) scale(1.04);
    box-shadow:
        0 6px 20px rgba(255,193,7,0.4),
        0 0 15px rgba(255,255,255,0.4) inset;
    border-color: #ffc107;
    background: #ffc107 !important;
    animation: pulse-glow-small 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow-small {
    0% {
        box-shadow:
            0 6px 20px rgba(255,193,7,0.4),
            0 0 15px rgba(255,255,255,0.4) inset;
    }
    100% {
        box-shadow:
            0 8px 25px rgba(255,193,7,0.6),
            0 0 20px rgba(255,255,255,0.6) inset;
    }
}

#categoriesRow .category-card i {
    font-size: 1.3rem;
    margin-bottom: 0.3rem;
    display: block;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3));
    transition: all 0.3s ease;
}

#categoriesRow .category-card:hover i {
    transform: scale(1.05) rotate(3deg);
}

#categoriesRow .category-card.active i {
    transform: scale(1.1);
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.4));
}

#categoriesRow .category-card .category-name {
    font-size: 0.7rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    letter-spacing: 0.3px;
    transition: all 0.3s ease;
}

#categoriesRow .category-card:hover .category-name {
    transform: translateY(-0.5px);
}

/* Product grid */
.products-display {
    margin-top: 0.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); /* 22627272f29 273431272d 274428372742272a 394449 2744342734272a 2744284a274629 */
    gap: 1rem;
}

.products-display .product-card {
    border-radius: 14px;
    border: 1px solid #e5e7eb;
    background-color: #ffffff;
    color: #111827;
    padding: 0.8rem 0.9rem; /* 272d332733 23432831 4444434437 482744444533 */
    min-height: 150px;
}


/* عدّاد عدد المنتجات في السلة */
.pos-cart-card #cartCount {
    font-weight: 700;
    font-size: 0.9rem;
    margin-inline: 0.1rem;
}

.pos-cart-card .text-muted {
    font-size: 0.8rem;
}

/* تحسين products-display للتصميم الحديث */
.products-display {
    padding: 1rem !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    gap: 1.25rem !important;
}


.products-display .product-card:hover {
    transform: translateY(-3px);
    border-color: #0d6efd;
    box-shadow: 0 16px 30px rgba(37, 99, 235, 0.18);
}

/* Cart / ticket panel */
.pos-cart-card .cart-items-container {
    border-radius: 10px;
    background-color: #f9fafb;
    border: 1px dashed #e5e7eb;
    padding: 0.9rem;
    max-height: 360px; /* 2c274e29 274449292745274a2a 44284845 27454a4227464a272a 2d4a46 2744334429 */
    overflow-y: auto;
}

.pos-cart-card .cart-items-container::-webkit-scrollbar {
    width: 6px;
}

.pos-cart-card .cart-items-container::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 999px;
}

.pos-actions .btn {
    font-weight: 600;
    border-radius: 999px;
    padding-block: 0.55rem;
}

.pos-actions .btn i {
    margin-left: 0.35rem;
}

/* Invoice summary */
.pos-left-panel .pos-summary-card {
    border-top: 2px solid #0d6efd;
}

.pos-left-panel .pos-summary-card .invoice-summary {
    background-color: #f9fafb;
    border-radius: 12px;
    padding: 0.75rem 0.9rem;
}

.pos-left-panel .pos-summary-card .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.35rem 0;
    font-size: 0.9rem;
    color: #374151;
}

.pos-left-panel .pos-summary-card .summary-row + .summary-row {
    border-top: 1px dashed #e5e7eb;
}

.pos-left-panel .pos-summary-card .summary-row.total-row {
    font-weight: 700;
    font-size: 1rem;
    color: #111827;
}

.pos-left-panel .pos-summary-card .total-amount {
    color: #0d6efd;
    font-size: 1.1rem;
}

/* Sticky cart column on desktop */
@media (min-width: 992px) {
    .pos-left-panel {
        position: sticky;
        top: 88px;
    }
}

/* Mobile tweaks */
@media (max-width: 991.98px) {
    .pos-layout {
        padding: 0.9rem;
        border-radius: 14px;
    }

    .pos-right-panel,
    .pos-left-panel {
        padding: 0;
    }

    .pos-cart-card .cart-items-container {
        max-height: 260px;
    }
}


.app-navbar .nav-link {
    position: relative;
    font-weight: 500;
    padding: 0.45rem 1rem;
    margin-inline: 0.1rem;
    border-radius: 999px;
    color: rgba(255, 255, 255, 0.85);
    transition: background-color 0.2s ease, color 0.2s ease, transform 0.15s ease;
}

.app-navbar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.16);
    color: #ffffff;
    transform: translateY(-1px);
}

.app-navbar .nav-link.active {
    background-color: #ffc107;
    color: #212529;
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.4);
}

/* تحسين زر المستخدم في الـ navbar */
.app-navbar .dropdown-toggle {
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 999px;
    padding: 0.5rem 1.2rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.app-navbar .dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.app-navbar .dropdown-toggle i {
    font-size: 1.3rem;
    margin-left: 0.4rem;
}

.app-navbar .dropdown-menu {
    min-width: 220px;
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    padding: 0.5rem;
    margin-top: 0.5rem;
    z-index: 9999 !important;
    position: absolute !important;
}

.app-navbar .dropdown-item {
    border-radius: 8px;
    padding: 0.6rem 1rem;
    margin-bottom: 0.25rem;
    transition: all 0.2s ease;
    font-weight: 500;
}

.app-navbar .dropdown-item i {
    margin-left: 0.5rem;
    font-size: 1.1rem;
}

.app-navbar .dropdown-item:hover:not(.disabled) {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateX(-3px);
}

.app-navbar .dropdown-item.disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 0.5rem;
}

/* تحسين زر تسجيل الخروج */
.app-navbar .dropdown-item[onclick*="logout"] {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    font-weight: 600;
    margin-top: 0.25rem;
    border: none;
}

.app-navbar .dropdown-item[onclick*="logout"]:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.app-navbar .dropdown-item[onclick*="logout"] i {
    color: white;
}

.app-container {
    padding-bottom: 1.5rem;
}

@media (max-width: 768px) {
    .page-content {
        padding: 0.9rem;
        border-radius: 12px;
    }
}

.invoice-details-container .badge {
    font-size: 0.85em;
    padding: 0.35em 0.65em;
}

.invoice-details-container .table-responsive {
    max-height: 400px;
    overflow-y: auto;
}

/* ========================================
   تصميم نقطة البيع الحديث
   ======================================== */

.modern-pos-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 0;
    height: calc(100vh - 80px);
    background: #f5f5f5;
    margin: -1.25rem;
    padding: 0;
}

/* ========================================
   الفئات - Tabs Style
   ======================================== */

.categories-tabs-container {
    background: white;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.categories-tabs {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.category-tab {
    background: #f0f0f0;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    transition: all 0.2s;
}

.category-tab:hover {
    background: #e0e0e0;
}

.category-tab.active {
    background: #7c3aed;
    color: white;
}

/* ========================================
   عرض المنتجات - Grid Modern
   ======================================== */

.products-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1.25rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 0;
    min-height: 500px;
}

/* بطاقة المنتج الحديثة */
.product-card-modern {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    height: 300px;
}

.product-card-modern:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.product-image-modern {
    position: relative;
    width: 100%;
    height: 180px;
    background: #f0f0f0;
    overflow: hidden;
    flex-shrink: 0;
}

.product-image-modern img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image-modern .no-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: #d0d0d0;
}

.stock-badge-modern {
    position: absolute;
    top: 0.5rem;
    left: 0.5rem;
    background: rgba(0, 0, 0, 0.75);
    color: white;
    padding: 0.25rem 0.6rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 600;
}

.product-info-modern {
    padding: 0.75rem;
    text-align: center;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.product-name-modern {
    font-size: 0.95rem;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

.product-price-modern {
    font-size: 1rem;
    font-weight: 600;
    color: #2563eb;
    margin-top: 0.25rem;
}

/* ========================================
   قسم السلة - التصميم الحديث
   ======================================== */

/* عنصر السلة الحديث */
.cart-item-modern {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 10px;
    transition: all 0.2s ease;
}

.cart-item-modern:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border-color: #d1d5db;
}

/* رأس عنصر السلة */
.cart-item-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.cart-item-name-modern {
    font-size: 15px;
    font-weight: 600;
    color: #1f2937;
    flex: 1;
    padding-left: 8px;
}

.cart-item-remove-modern {
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
}

.cart-item-remove-modern:hover {
    background: #dc2626;
    transform: scale(1.05);
}

/* جسم عنصر السلة */
.cart-item-body-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

/* قسم الكمية */
.cart-item-qty-modern {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #f9fafb;
    border-radius: 8px;
    padding: 4px;
}

.qty-btn-modern {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    color: #6b7280;
    font-size: 14px;
}

.qty-btn-modern:hover {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.qty-input-modern {
    width: 45px;
    height: 28px;
    text-align: center;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    background: white;
}

.qty-input-modern:focus {
    outline: none;
    border-color: #3b82f6;
}

/* قسم السعر */
.cart-item-price-modern {
    text-align: left;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.unit-price-modern {
    font-size: 12px;
    color: #6b7280;
}

.total-price-modern {
    font-size: 16px;
    font-weight: 700;
    color: #10b981;
}

/* ملخص الفاتورة */
.cart-summary {
    background: white;
    padding: 1rem;
    border-top: 2px solid #e9ecef;
    border-bottom: 2px solid #e9ecef;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    font-size: 0.95rem;
}

.summary-row.total-row {
    border-top: 2px solid #dee2e6;
    margin-top: 0.5rem;
    padding-top: 0.75rem;
    font-size: 1.1rem;
    font-weight: 700;
}

.summary-value {
    font-weight: 600;
    color: #212529;
}

.total-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #198754;
}

/* نقاط الولاء */
.loyalty-section {
    background: white;
    padding: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.loyalty-header {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 0.75rem;
}

.loyalty-points {
    display: flex;
    gap: 1rem;
}

.points-won,
.points-total {
    flex: 1;
    text-align: center;
}

.points-label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.points-value {
    font-size: 1.25rem;
    font-weight: 700;
}

.points-won .points-value {
    color: #198754;
}

.points-total .points-value {
    color: #0d6efd;
}

/* أزرار الإجراءات */
.action-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: white;
}

.action-btn {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 0.6rem 0.5rem;
    font-size: 0.75rem;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    text-align: center;
}

.action-btn i {
    font-size: 1.1rem;
    color: #6c757d;
}

.action-btn:hover {
    background: #f8f9fa;
    border-color: #0d6efd;
    color: #0d6efd;
}

.action-btn:hover i {
    color: #0d6efd;
}

.action-btn.full-width {
    grid-column: 1 / -1;
    flex-direction: row;
    justify-content: center;
    gap: 0.5rem;
}

/* معلومات العميل */
.customer-section {
    background: white;
    padding: 1rem;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.customer-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.customer-info {
    flex: 1;
}

.customer-name {
    font-weight: 600;
    font-size: 1rem;
    color: #212529;
    margin-bottom: 0.5rem;
}

/* لوحة المفاتيح الرقمية */
.numpad-section {
    background: white;
    padding: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.numpad-display {
    margin-bottom: 0.75rem;
}

.numpad-input {
    width: 100%;
    padding: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    text-align: left;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    background: #f8f9fa;
    color: #212529;
}

.numpad-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
}

.numpad-btn {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: #212529;
    cursor: pointer;
    transition: all 0.2s;
}

.numpad-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.numpad-btn:active {
    transform: scale(0.95);
}

.numpad-btn.numpad-label {
    background: #e7f1ff;
    color: #0d6efd;
    font-size: 0.75rem;
    font-weight: 600;
}

.numpad-btn.numpad-label:hover {
    background: #cfe2ff;
}

.numpad-btn.numpad-clear {
    background: #fff3cd;
    color: #856404;
}

.numpad-btn.numpad-clear:hover {
    background: #ffe69c;
}

/* زر الدفع */
.payment-section {
    background: #6f42c1;
    padding: 1rem;
}

.payment-btn {
    width: 100%;
    background: white;
    border: none;
    border-radius: 8px;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 700;
    color: #6f42c1;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    transition: all 0.2s;
}

.payment-btn:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.payment-btn i {
    font-size: 1.5rem;
}



/* تجاوب مع الشاشات الصغيرة */
@media (max-width: 1400px) {
    .products-grid-modern {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }

    .product-card-modern {
        height: 280px;
    }

    .product-image-modern {
        height: 160px;
    }
}

@media (max-width: 1200px) {
    .products-grid-modern {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1rem;
    }

    .product-card-modern {
        height: 260px;
    }

    .product-image-modern {
        height: 150px;
    }
}

@media (max-width: 768px) {
    .products-grid-modern {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 0.75rem;
        padding: 1rem;
    }

    .product-card-modern {
        height: 240px;
    }

    .product-image-modern {
        height: 140px;
    }

    .product-name-modern {
        font-size: 0.85rem;
    }

    .product-price-modern {
        font-size: 0.9rem;
    }

    .stock-badge-modern {
        font-size: 0.75rem;
        padding: 0.2rem 0.5rem;
    }

    .category-tab {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .products-grid-modern {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.5rem;
        padding: 0.75rem;
    }

    .product-card-modern {
        height: 220px;
    }

    .product-image-modern {
        height: 120px;
    }

    .product-image-modern .no-image {
        font-size: 3rem;
    }

    .product-name-modern {
        font-size: 0.8rem;
    }

    .product-price-modern {
        font-size: 0.85rem;
    }

    .stock-badge-modern {
        font-size: 0.7rem;
        padding: 0.15rem 0.4rem;
    }

    .product-info-modern {
        padding: 0.5rem;
    }

    .category-tab {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
}

