try {
    $body = '{"confirmation":"RESET"}'
    Write-Host "Sending request with body: $body"
    
    # First, let's try to get more detailed error information
    $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/settings/reset' -Method POST -ContentType 'application/json' -Body $body
    
    Write-Host "Success!"
    Write-Host "Response: $($response.Content)"
}
catch {
    Write-Host "Error occurred:"
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)"
    Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
    Write-Host "Error Message: $($_.Exception.Message)"
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
        $reader.Close()
    }
}
