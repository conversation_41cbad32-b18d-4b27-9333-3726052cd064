package com.aredoo.pos.controller;

import com.aredoo.pos.model.Supplier;
import com.aredoo.pos.repository.SupplierRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/suppliers")
@CrossOrigin(origins = "*")
public class SupplierController {

    @Autowired
    private SupplierRepository supplierRepository;

    @GetMapping
    public ResponseEntity<List<Supplier>> getAllSuppliers(@RequestParam(defaultValue = "true") Boolean activeOnly) {
        List<Supplier> suppliers;
        if (activeOnly) {
            suppliers = supplierRepository.findByIsActiveTrueOrderByName();
        } else {
            suppliers = supplierRepository.findAll();
        }
        return ResponseEntity.ok(suppliers);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Supplier> getSupplierById(@PathVariable Long id) {
        Optional<Supplier> supplier = supplierRepository.findById(id);
        return supplier.map(ResponseEntity::ok)
                      .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    public ResponseEntity<List<Supplier>> searchSuppliers(@RequestParam String term) {
        List<Supplier> suppliers = supplierRepository.searchSuppliers(term);
        return ResponseEntity.ok(suppliers);
    }

    @GetMapping("/phone/{phone}")
    public ResponseEntity<Supplier> getSupplierByPhone(@PathVariable String phone) {
        Optional<Supplier> supplier = supplierRepository.findByPhone(phone);
        return supplier.map(ResponseEntity::ok)
                      .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/with-balance")
    public ResponseEntity<List<Supplier>> getSuppliersWithBalance() {
        List<Supplier> suppliers = supplierRepository.findSuppliersWithBalance();
        return ResponseEntity.ok(suppliers);
    }

    @PostMapping
    public ResponseEntity<Supplier> createSupplier(@RequestBody Supplier supplier) {
        try {
            supplier.setCreatedAt(LocalDateTime.now());
            Supplier savedSupplier = supplierRepository.save(supplier);
            return ResponseEntity.ok(savedSupplier);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<Supplier> updateSupplier(@PathVariable Long id, @RequestBody Supplier supplierDetails) {
        Optional<Supplier> optionalSupplier = supplierRepository.findById(id);
        if (optionalSupplier.isPresent()) {
            Supplier supplier = optionalSupplier.get();
            supplier.setName(supplierDetails.getName());
            supplier.setPhone(supplierDetails.getPhone());
            supplier.setEmail(supplierDetails.getEmail());
            supplier.setAddress(supplierDetails.getAddress());
            supplier.setCity(supplierDetails.getCity());
            supplier.setContactPerson(supplierDetails.getContactPerson());
            supplier.setBalance(supplierDetails.getBalance());
            supplier.setIsActive(supplierDetails.getIsActive());
            supplier.setUpdatedAt(LocalDateTime.now());
            
            Supplier updatedSupplier = supplierRepository.save(supplier);
            return ResponseEntity.ok(updatedSupplier);
        }
        return ResponseEntity.notFound().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSupplier(@PathVariable Long id) {
        Optional<Supplier> supplier = supplierRepository.findById(id);
        if (supplier.isPresent()) {
            Supplier s = supplier.get();
            s.setIsActive(false);
            s.setUpdatedAt(LocalDateTime.now());
            supplierRepository.save(s);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }
}
