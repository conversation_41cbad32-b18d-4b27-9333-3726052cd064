package com.aredoo.pos.repository;

import com.aredoo.pos.model.Supplier;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SupplierRepository extends JpaRepository<Supplier, Long> {
    
    List<Supplier> findByIsActiveTrue();
    
    List<Supplier> findByIsActiveTrueOrderByName();
    
    Optional<Supplier> findByPhone(String phone);
    
    Optional<Supplier> findByEmail(String email);
    
    @Query("SELECT s FROM Supplier s WHERE s.isActive = true AND " +
           "(LOWER(s.name) LIKE LOWER(CONCAT('%', :term, '%')) OR " +
           "LOWER(s.phone) LIKE LOWER(CONCAT('%', :term, '%')) OR " +
           "LOWER(s.email) LIKE LOWER(CONCAT('%', :term, '%')))")
    List<Supplier> searchSuppliers(@Param("term") String term);
    
    @Query("SELECT s FROM Supplier s WHERE s.balance > 0 AND s.isActive = true")
    List<Supplier> findSuppliersWithBalance();
    
    @Query("SELECT COUNT(s) FROM Supplier s WHERE s.isActive = true")
    long countActiveSuppliers();
    
    @Query("SELECT SUM(s.balance) FROM Supplier s WHERE s.isActive = true")
    Double getTotalSupplierBalance();
}
