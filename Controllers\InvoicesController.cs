using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Aredoo.Server.Data;
using Aredoo.Server.Models;

namespace Aredoo.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class InvoicesController : ControllerBase
{
    private readonly AredooDbContext _context;

    public InvoicesController(AredooDbContext context)
    {
        _context = context;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll(
        [FromQuery] string? type = null,
        [FromQuery] DateTime? fromDate = null,
        [FromQuery] DateTime? toDate = null)
    {
        var query = _context.Invoices
            .Include(i => i.Customer)
            .Include(i => i.Supplier)
            .Include(i => i.Items)
            .Where(i => !i.IsDeleted)
            .AsQueryable();

        if (!string.IsNullOrEmpty(type))
            query = query.Where(i => i.Type == type);

        if (fromDate.HasValue)
            query = query.Where(i => i.InvoiceDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(i => i.InvoiceDate <= toDate.Value);

        var invoices = await query
            .OrderByDescending(i => i.InvoiceDate)
            .ToListAsync();

        return Ok(invoices);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(int id)
    {
        var invoice = await _context.Invoices
            .Include(i => i.Customer)
            .Include(i => i.Supplier)
            .Include(i => i.Items)
                .ThenInclude(ii => ii.Product)
            .FirstOrDefaultAsync(i => i.Id == id);

        if (invoice == null)
            return NotFound();

        return Ok(invoice);
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] Invoice invoice)
    {
        // Generate invoice number
        var lastInvoice = await _context.Invoices
            .Where(i => i.Type == invoice.Type)
            .OrderByDescending(i => i.Id)
            .FirstOrDefaultAsync();

        var nextNumber = (lastInvoice?.Id ?? 0) + 1;
        invoice.InvoiceNumber = $"{invoice.Type[0]}{DateTime.Now:yyyyMMdd}{nextNumber:D4}";
        invoice.CreatedAt = DateTime.Now;

        // Calculate totals
        invoice.SubTotal = invoice.Items.Sum(i => i.Total);
        invoice.Total = invoice.SubTotal - invoice.Discount + invoice.Tax;
        invoice.Remaining = invoice.Total - invoice.Paid;

        // Update product quantities
        foreach (var item in invoice.Items)
        {
            var product = await _context.Products.FindAsync(item.ProductId);
            if (product != null)
            {
                if (invoice.Type == "Sale" || invoice.Type == "PurchaseReturn")
                    product.Quantity -= (int)item.Quantity;
                else if (invoice.Type == "Purchase" || invoice.Type == "SaleReturn")
                    product.Quantity += (int)item.Quantity;

                // Store product name if not provided
                if (string.IsNullOrEmpty(item.ProductName))
                    item.ProductName = product.Name;

                item.PurchasePrice = product.PurchasePrice;
                item.Profit = (item.UnitPrice - product.PurchasePrice) * item.Quantity;
            }
        }

        // Update customer/supplier balance
        if (invoice.CustomerId.HasValue && invoice.Remaining > 0)
        {
            var customer = await _context.Customers.FindAsync(invoice.CustomerId.Value);
            if (customer != null)
                customer.Balance += invoice.Remaining;
        }

        if (invoice.SupplierId.HasValue && invoice.Remaining > 0)
        {
            var supplier = await _context.Suppliers.FindAsync(invoice.SupplierId.Value);
            if (supplier != null)
                supplier.Balance += invoice.Remaining;
        }

        _context.Invoices.Add(invoice);
        await _context.SaveChangesAsync();

        // Return simple response without circular references
        return Ok(new
        {
            id = invoice.Id,
            invoiceNumber = invoice.InvoiceNumber,
            total = invoice.Total,
            paid = invoice.Paid,
            remaining = invoice.Remaining,
            message = "تم حفظ الفاتورة بنجاح"
        });
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(int id, [FromBody] Invoice invoice)
    {
        if (id != invoice.Id)
            return BadRequest();

        var existingInvoice = await _context.Invoices
            .Include(i => i.Items)
            .FirstOrDefaultAsync(i => i.Id == id);

        if (existingInvoice == null)
            return NotFound();

        // Restore old quantities
        foreach (var item in existingInvoice.Items)
        {
            var product = await _context.Products.FindAsync(item.ProductId);
            if (product != null)
            {
                if (existingInvoice.Type == "Sale" || existingInvoice.Type == "PurchaseReturn")
                    product.Quantity += (int)item.Quantity;
                else if (existingInvoice.Type == "Purchase" || existingInvoice.Type == "SaleReturn")
                    product.Quantity -= (int)item.Quantity;
            }
        }

        // Update with new data
        _context.Entry(existingInvoice).CurrentValues.SetValues(invoice);
        _context.InvoiceItems.RemoveRange(existingInvoice.Items);

        foreach (var item in invoice.Items)
        {
            existingInvoice.Items.Add(item);
            var product = await _context.Products.FindAsync(item.ProductId);
            if (product != null)
            {
                if (invoice.Type == "Sale" || invoice.Type == "PurchaseReturn")
                    product.Quantity -= (int)item.Quantity;
                else if (invoice.Type == "Purchase" || invoice.Type == "SaleReturn")
                    product.Quantity += (int)item.Quantity;

                // Store product name if not provided
                if (string.IsNullOrEmpty(item.ProductName))
                    item.ProductName = product.Name;

                // Calculate profit for sale invoices
                item.PurchasePrice = product.PurchasePrice;
                item.Profit = (item.UnitPrice - product.PurchasePrice) * item.Quantity;
            }
        }

        await _context.SaveChangesAsync();
        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        var invoice = await _context.Invoices
            .Include(i => i.Items)
            .FirstOrDefaultAsync(i => i.Id == id);

        if (invoice == null)
            return NotFound();

        // Restore quantities
        foreach (var item in invoice.Items)
        {
            var product = await _context.Products.FindAsync(item.ProductId);
            if (product != null)
            {
                if (invoice.Type == "Sale" || invoice.Type == "PurchaseReturn")
                    product.Quantity += (int)item.Quantity;
                else if (invoice.Type == "Purchase" || invoice.Type == "SaleReturn")
                    product.Quantity -= (int)item.Quantity;
            }
        }

        invoice.IsDeleted = true;
        await _context.SaveChangesAsync();

        return NoContent();
    }

    [HttpGet("next-number")]
    public async Task<IActionResult> GetNextNumber([FromQuery] string type)
    {
        var lastInvoice = await _context.Invoices
            .Where(i => i.Type == type)
            .OrderByDescending(i => i.Id)
            .FirstOrDefaultAsync();

        var nextNumber = (lastInvoice?.Id ?? 0) + 1;
        var invoiceNumber = $"{type[0]}{DateTime.Now:yyyyMMdd}{nextNumber:D4}";

        return Ok(new { number = invoiceNumber });
    }

    [HttpGet("check-profit-data")]
    public async Task<IActionResult> CheckProfitData()
    {
        var saleItems = await _context.InvoiceItems
            .Include(ii => ii.Product)
            .Include(ii => ii.Invoice)
            .Where(ii => !ii.Invoice!.IsDeleted && ii.Invoice.Type == "Sale")
            .ToListAsync();

        var stats = new
        {
            totalItems = saleItems.Count,
            itemsWithProfit = saleItems.Count(ii => ii.Profit > 0),
            itemsWithZeroProfit = saleItems.Count(ii => ii.Profit == 0),
            itemsWithPurchasePrice = saleItems.Count(ii => ii.PurchasePrice > 0),
            itemsWithZeroPurchasePrice = saleItems.Count(ii => ii.PurchasePrice == 0),
            productsWithoutPurchasePrice = saleItems
                .Where(ii => ii.Product != null && ii.Product.PurchasePrice == 0)
                .Select(ii => new { ii.Product!.Name, ii.Product.Code })
                .Distinct()
                .Take(10)
                .ToList(),
            totalProfit = saleItems.Sum(ii => ii.Profit),
            totalSales = saleItems.Sum(ii => ii.Total),
            sampleItems = saleItems.Take(5).Select(ii => new
            {
                ii.ProductName,
                ii.UnitPrice,
                ii.PurchasePrice,
                ii.Quantity,
                ii.Discount,
                ii.Total,
                ii.Profit,
                CalculatedProfit = (ii.UnitPrice - ii.PurchasePrice) * ii.Quantity
            }).ToList()
        };

        return Ok(stats);
    }

    [HttpPost("recalculate-profit")]
    public async Task<IActionResult> RecalculateProfit()
    {
        // Get all invoice items
        var allItems = await _context.InvoiceItems
            .Include(ii => ii.Product)
            .Include(ii => ii.Invoice)
            .Where(ii => !ii.Invoice!.IsDeleted)
            .ToListAsync();

        int updatedCount = 0;
        int errorCount = 0;
        int fixedUnitPrice = 0;
        int fixedProductName = 0;

        foreach (var item in allItems)
        {
            try
            {
                if (item.Product != null)
                {
                    // Fix ProductName if it's empty
                    if (string.IsNullOrEmpty(item.ProductName))
                    {
                        item.ProductName = item.Product.Name;
                        fixedProductName++;
                    }

                    // Fix UnitPrice if it's 0 (use Total / Quantity)
                    if (item.UnitPrice == 0 && item.Total > 0 && item.Quantity > 0)
                    {
                        item.UnitPrice = item.Total / item.Quantity;
                        fixedUnitPrice++;
                    }

                    // Update purchase price from product
                    item.PurchasePrice = item.Product.PurchasePrice;

                    // Recalculate profit
                    item.Profit = (item.UnitPrice - item.Product.PurchasePrice) * item.Quantity;

                    updatedCount++;
                }
                else
                {
                    errorCount++;
                }
            }
            catch
            {
                errorCount++;
            }
        }

        await _context.SaveChangesAsync();

        return Ok(new
        {
            message = "تم إعادة حساب الأرباح بنجاح",
            totalItems = allItems.Count,
            updatedCount,
            errorCount,
            fixedUnitPrice,
            fixedProductName
        });
    }
}

