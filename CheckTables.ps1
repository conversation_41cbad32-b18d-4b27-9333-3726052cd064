try {
    # Install SQLite module if not available
    if (!(Get-Module -ListAvailable -Name SQLite)) {
        Write-Host "Installing SQLite module..."
        Install-Module -Name SQLite -Force -Scope CurrentUser
    }
    
    Import-Module SQLite
    
    $dbPath = ".\data\aredoo.db"
    Write-Host "Checking database at: $dbPath"
    
    if (Test-Path $dbPath) {
        $connection = New-SQLiteConnection -DataSource $dbPath
        
        # Get all table names
        $tables = Invoke-SqliteQuery -SQLiteConnection $connection -Query "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;"
        
        Write-Host "Tables in database:"
        foreach ($table in $tables) {
            Write-Host "- $($table.name)"
        }
        
        $connection.Close()
    } else {
        Write-Host "Database file not found at: $dbPath"
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Trying alternative method..."
    
    # Alternative using sqlite3.exe if available
    if (Get-Command sqlite3 -ErrorAction SilentlyContinue) {
        Write-Host "Using sqlite3.exe..."
        sqlite3 ".\data\aredoo.db" ".tables"
    } else {
        Write-Host "SQLite tools not available. Please install SQLite or check table names manually."
    }
}
