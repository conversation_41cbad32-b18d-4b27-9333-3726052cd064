package com.aredoo.pos.repository;

import com.aredoo.pos.model.Invoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface InvoiceRepository extends JpaRepository<Invoice, Long> {
    
    List<Invoice> findByIsDeletedFalse();
    
    List<Invoice> findByIsDeletedFalseOrderByCreatedAtDesc();
    
    Optional<Invoice> findByInvoiceNumber(String invoiceNumber);
    
    List<Invoice> findByType(String type);
    
    List<Invoice> findByTypeAndIsDeletedFalse(String type);
    
    List<Invoice> findByPaymentType(String paymentType);
    
    List<Invoice> findByCustomerId(Long customerId);
    
    List<Invoice> findBySupplierId(Long supplierId);
    
    @Query("SELECT i FROM Invoice i WHERE i.isDeleted = false AND " +
           "i.invoiceDate BETWEEN :startDate AND :endDate")
    List<Invoice> findByDateRange(@Param("startDate") LocalDateTime startDate, 
                                  @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT i FROM Invoice i WHERE i.isDeleted = false AND " +
           "i.type = :type AND i.invoiceDate BETWEEN :startDate AND :endDate")
    List<Invoice> findByTypeAndDateRange(@Param("type") String type,
                                         @Param("startDate") LocalDateTime startDate, 
                                         @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT i FROM Invoice i WHERE i.remaining > 0 AND i.isDeleted = false")
    List<Invoice> findUnpaidInvoices();
    
    @Query("SELECT COUNT(i) FROM Invoice i WHERE i.type = 'Sale' AND i.isDeleted = false")
    long countSaleInvoices();
    
    @Query("SELECT SUM(i.total) FROM Invoice i WHERE i.type = 'Sale' AND i.isDeleted = false")
    BigDecimal getTotalSalesAmount();
    
    @Query("SELECT SUM(i.remaining) FROM Invoice i WHERE i.isDeleted = false")
    BigDecimal getTotalRemainingAmount();
    
    @Query("SELECT i FROM Invoice i WHERE i.isDeleted = false AND " +
           "(i.invoiceNumber LIKE CONCAT('%', :term, '%') OR " +
           "i.customer.name LIKE CONCAT('%', :term, '%') OR " +
           "i.supplier.name LIKE CONCAT('%', :term, '%'))")
    List<Invoice> searchInvoices(@Param("term") String term);
}
