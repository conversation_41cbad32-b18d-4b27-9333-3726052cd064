using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Aredoo.Server.Data;
using Aredoo.Server.Models;
using System.IO.Compression;

namespace Aredoo.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SettingsController : ControllerBase
{
    private readonly AredooDbContext _context;

    public SettingsController(AredooDbContext context)
    {
        _context = context;
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var settings = await _context.Settings.FirstOrDefaultAsync();
        if (settings == null)
        {
            settings = new Settings { Id = 1 };
            _context.Settings.Add(settings);
            await _context.SaveChangesAsync();
        }

        return Ok(settings);
    }

    [HttpPut]
    public async Task<IActionResult> Update([FromBody] Settings settings)
    {
        settings.UpdatedAt = DateTime.Now;
        _context.Entry(settings).State = EntityState.Modified;
        await _context.SaveChangesAsync();

        return NoContent();
    }

    [HttpPost("backup")]
    public async Task<IActionResult> Backup()
    {
        try
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var backupDir = Path.Combine(Directory.GetCurrentDirectory(), "data", "backups", DateTime.Now.ToString("yyyy-MM-dd"));
            Directory.CreateDirectory(backupDir);

            // نسخ قاعدة البيانات
            var sourceDbFile = Path.Combine(Directory.GetCurrentDirectory(), "data", "aredoo.db");
            var destDbFile = Path.Combine(backupDir, $"aredoo_backup_{timestamp}.db");
            System.IO.File.Copy(sourceDbFile, destDbFile, true);

            // إنشاء نسخة احتياطية كاملة (ZIP)
            var fullBackupPath = await CreateFullBackup(timestamp);

            return Ok(new {
                message = "تم إنشاء النسخة الاحتياطية بنجاح",
                dbPath = destDbFile,
                fullBackupPath = fullBackupPath,
                timestamp = timestamp
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "فشل إنشاء النسخة الاحتياطية", error = ex.Message });
        }
    }

    private async Task<string> CreateFullBackup(string timestamp)
    {
        var backupDir = Path.Combine(Directory.GetCurrentDirectory(), "data", "backups", "full");
        var externalBackupDir = Path.Combine(Directory.GetCurrentDirectory(), "Backups");

        Directory.CreateDirectory(backupDir);
        Directory.CreateDirectory(externalBackupDir);

        var zipPath = Path.Combine(backupDir, $"aredoo_full_backup_{timestamp}.zip");
        var externalZipPath = Path.Combine(externalBackupDir, $"aredoo_backup_{timestamp}.zip");
        var dbPath = Path.Combine(Directory.GetCurrentDirectory(), "data", "aredoo.db");

        using (var zip = new System.IO.Compression.ZipArchive(System.IO.File.Create(zipPath), System.IO.Compression.ZipArchiveMode.Create))
        {
            // إضافة قاعدة البيانات
            if (System.IO.File.Exists(dbPath))
            {
                zip.CreateEntryFromFile(dbPath, "aredoo.db");
            }

            // إضافة الصور
            var imagesDir = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "images");
            if (Directory.Exists(imagesDir))
            {
                AddDirectoryToZip(zip, imagesDir, "wwwroot/images/");
            }

            // إضافة ملفات الإعدادات
            var settingsFiles = new[] { "appsettings.json", "appsettings.Development.json" };
            foreach (var settingsFile in settingsFiles)
            {
                var settingsPath = Path.Combine(Directory.GetCurrentDirectory(), settingsFile);
                if (System.IO.File.Exists(settingsPath))
                {
                    zip.CreateEntryFromFile(settingsPath, settingsFile);
                }
            }

            // إضافة معلومات النسخة الاحتياطية
            var backupInfo = new
            {
                CreatedAt = DateTime.Now,
                Version = "1.0",
                DatabaseSize = new FileInfo(dbPath).Length,
                Description = "نسخة احتياطية كاملة من نظام أريدوو"
            };

            var infoEntry = zip.CreateEntry("backup_info.json");
            using (var stream = infoEntry.Open())
            using (var writer = new StreamWriter(stream))
            {
                await writer.WriteAsync(System.Text.Json.JsonSerializer.Serialize(backupInfo, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }));
            }
        }

        // إنشاء نسخة في مجلد Backups أيضاً للوصول السهل
        System.IO.File.Copy(zipPath, externalZipPath, true);

        return zipPath;
    }

    private void AddDirectoryToZip(System.IO.Compression.ZipArchive zip, string sourceDir, string entryPrefix)
    {
        var files = Directory.GetFiles(sourceDir, "*", SearchOption.AllDirectories);
        foreach (var file in files)
        {
            var relativePath = Path.GetRelativePath(sourceDir, file).Replace('\\', '/');
            zip.CreateEntryFromFile(file, entryPrefix + relativePath);
        }
    }

	    [HttpGet("backups")]
	    public IActionResult GetBackups()
	    {
	        var backupsRoot = Path.Combine(Directory.GetCurrentDirectory(), "data", "backups");
	        if (!Directory.Exists(backupsRoot))
	        {
	            return Ok(Array.Empty<BackupInfoDto>());
	        }

	        var files = Directory
	            .GetDirectories(backupsRoot)
	            .SelectMany(dir => Directory.GetFiles(dir, "*.db", SearchOption.TopDirectoryOnly))
	            .Select(path =>
	            {
	                var info = new FileInfo(path);
	                var relativePath = Path.GetRelativePath(backupsRoot, path).Replace('\\', '/');
	                return new BackupInfoDto(
	                    info.Name,
	                    relativePath,
	                    info.Length,
	                    info.LastWriteTime
	                );
	            })
	            .OrderByDescending(b => b.CreatedAt)
	            .ToList();

	        return Ok(files);
	    }

	    [HttpPost("restore")]
	    public IActionResult Restore([FromBody] RestoreBackupRequest request)
	    {
	        if (string.IsNullOrWhiteSpace(request.RelativePath))
	        {
	            return BadRequest(new { message = "مسار النسخة الاحتياطية مطلوب" });
	        }

	        var backupsRoot = Path.Combine(Directory.GetCurrentDirectory(), "data", "backups");
	        var backupFullPath = Path.GetFullPath(Path.Combine(backupsRoot, request.RelativePath));

	        if (!backupFullPath.StartsWith(backupsRoot, StringComparison.OrdinalIgnoreCase))
	        {
	            return BadRequest(new { message = "مسار النسخة الاحتياطية غير صالح" });
	        }

	        if (!System.IO.File.Exists(backupFullPath))
	        {
	            return NotFound(new { message = "لم يتم العثور على ملف النسخة الاحتياطية المحدد" });
	        }

	        var dataDir = Path.Combine(Directory.GetCurrentDirectory(), "data");
	        Directory.CreateDirectory(dataDir);
	        var dbPath = Path.Combine(dataDir, "aredoo.db");

	        // عمل نسخة احتياطية من القاعدة الحالية قبل الاستبدال
	        var safetyBackupDir = Path.Combine(backupsRoot, "before-restore");
	        Directory.CreateDirectory(safetyBackupDir);
	        if (System.IO.File.Exists(dbPath))
	        {
	            var safetyFile = Path.Combine(safetyBackupDir, $"aredoo_before_restore_{DateTime.Now:yyyyMMdd_HHmmss}.db");
	            System.IO.File.Copy(dbPath, safetyFile, true);
	        }

	        // إغلاق اتصال EF مع قاعدة البيانات قبل نسخ الملف
	        _context.Database.CloseConnection();

	        System.IO.File.Copy(backupFullPath, dbPath, true);

	        return Ok(new { message = "تم استعادة النسخة الاحتياطية بنجاح. يُفضّل إعادة تشغيل النظام." });
	    }


    [HttpPost("reset")]
    public async Task<IActionResult> Reset([FromBody] ResetRequest request)
    {
        if (request.Confirmation != "RESET")
            return BadRequest(new { message = "يجب تأكيد العملية" });

        Console.WriteLine("Reset request received");

        // Create backup first
        try
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var backupDir = Path.Combine(Directory.GetCurrentDirectory(), "data", "backups", DateTime.Now.ToString("yyyy-MM-dd"));
            Directory.CreateDirectory(backupDir);

            var sourceDbFile = Path.Combine(Directory.GetCurrentDirectory(), "data", "aredoo.db");
            if (System.IO.File.Exists(sourceDbFile))
            {
                var destDbFile = Path.Combine(backupDir, $"aredoo_backup_before_reset_{timestamp}.db");
                System.IO.File.Copy(sourceDbFile, destDbFile, true);
                Console.WriteLine("Backup created successfully");
            }
        }
        catch (Exception backupEx)
        {
            Console.WriteLine($"Backup failed: {backupEx.Message}");
        }

        // Direct SQL approach - bypass Entity Framework completely
        try
        {
            Console.WriteLine("Starting reset process...");

            // Use direct SQL commands to clear data
            using (var connection = _context.Database.GetDbConnection())
            {
                await connection.OpenAsync();
                Console.WriteLine("Database connection opened");

                // Disable foreign key constraints
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "PRAGMA foreign_keys = OFF";
                    await command.ExecuteNonQueryAsync();
                    Console.WriteLine("Foreign keys disabled");
                }

                // Clear tables in order
                var tablesToClear = new[]
                {
                    "InvoiceItems", "PurchaseItems", "Installments", "Transactions",
                    "Attendances", "WorkShifts", "Expenses", "AuditLogs",
                    "Invoices", "Purchases", "Products", "Categories",
                    "Customers", "Suppliers", "Employees"
                };

                foreach (var table in tablesToClear)
                {
                    try
                    {
                        using (var command = connection.CreateCommand())
                        {
                            command.CommandText = $"DELETE FROM {table}";
                            var rowsAffected = await command.ExecuteNonQueryAsync();
                            Console.WriteLine($"Cleared {rowsAffected} rows from {table}");
                        }
                    }
                    catch (Exception tableEx)
                    {
                        Console.WriteLine($"Error clearing {table}: {tableEx.Message}");
                    }
                }

                // Re-enable foreign key constraints
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "PRAGMA foreign_keys = ON";
                    await command.ExecuteNonQueryAsync();
                    Console.WriteLine("Foreign keys re-enabled");
                }
            }

            Console.WriteLine("Reset completed successfully");
            return Ok(new { message = "تم تصفير النظام بنجاح" });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Reset error: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
            }

            var fullError = ex.ToString();
            Console.WriteLine($"Full exception: {fullError}");

            return BadRequest(new {
                message = "فشل تصفير النظام",
                error = ex.Message,
                innerError = ex.InnerException?.Message
            });
        }
    }

    [HttpGet("download-backup/{fileName}")]
    public IActionResult DownloadBackup(string fileName)
    {
        try
        {
            var fullBackupDir = Path.Combine(Directory.GetCurrentDirectory(), "data", "backups", "full");
            var externalBackupDir = Path.Combine(Directory.GetCurrentDirectory(), "Backups");

            string filePath = null;

            // Check internal backup directory first
            var internalPath = Path.Combine(fullBackupDir, fileName);
            if (System.IO.File.Exists(internalPath))
            {
                filePath = internalPath;
            }
            else
            {
                // Check external backup directory
                var externalPath = Path.Combine(externalBackupDir, fileName);
                if (System.IO.File.Exists(externalPath))
                {
                    filePath = externalPath;
                }
            }

            if (filePath == null || !fileName.EndsWith(".zip"))
            {
                return NotFound(new { message = "لم يتم العثور على ملف النسخة الاحتياطية" });
            }

            var fileBytes = System.IO.File.ReadAllBytes(filePath);
            return File(fileBytes, "application/zip", fileName);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "فشل تحميل النسخة الاحتياطية", error = ex.Message });
        }
    }

    [HttpPost("restore-full")]
    public async Task<IActionResult> RestoreFullBackup(IFormFile backupFile)
    {
        if (backupFile == null || !backupFile.FileName.EndsWith(".zip"))
        {
            return BadRequest(new { message = "يجب رفع ملف نسخة احتياطية صالح (.zip)" });
        }

        try
        {
            // إنشاء نسخة احتياطية من الحالة الحالية
            await Backup();

            var tempDir = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(tempDir);

            var tempZipPath = Path.Combine(tempDir, backupFile.FileName);
            using (var stream = new FileStream(tempZipPath, FileMode.Create))
            {
                await backupFile.CopyToAsync(stream);
            }

            // استخراج النسخة الاحتياطية
            using (var zip = System.IO.Compression.ZipFile.OpenRead(tempZipPath))
            {
                // التحقق من صحة النسخة الاحتياطية
                var backupInfoEntry = zip.GetEntry("backup_info.json");
                if (backupInfoEntry == null)
                {
                    return BadRequest(new { message = "ملف النسخة الاحتياطية غير صالح" });
                }

                // إغلاق اتصال قاعدة البيانات
                _context.Database.CloseConnection();

                // استعادة قاعدة البيانات
                var dbEntry = zip.GetEntry("aredoo.db") ?? zip.GetEntry("data/aredoo.db");
                if (dbEntry != null)
                {
                    var dbPath = Path.Combine(Directory.GetCurrentDirectory(), "data", "aredoo.db");

                    // التأكد من وجود مجلد data
                    Directory.CreateDirectory(Path.GetDirectoryName(dbPath));

                    dbEntry.ExtractToFile(dbPath, true);
                    Console.WriteLine($"Database restored from: {dbEntry.FullName}");
                }
                else
                {
                    Console.WriteLine("Warning: Database file not found in backup");
                }

                // استعادة الصور
                var imageEntries = zip.Entries.Where(e => e.FullName.StartsWith("wwwroot/images/"));
                foreach (var entry in imageEntries)
                {
                    var imagePath = Path.Combine(Directory.GetCurrentDirectory(), entry.FullName);
                    Directory.CreateDirectory(Path.GetDirectoryName(imagePath));
                    entry.ExtractToFile(imagePath, true);
                }
            }

            // تنظيف الملفات المؤقتة
            Directory.Delete(tempDir, true);

            // إعادة تهيئة اتصال قاعدة البيانات
            try
            {
                _context.Database.EnsureCreated();
                await _context.Database.OpenConnectionAsync();
                await _context.Database.CloseConnectionAsync();
            }
            catch (Exception dbEx)
            {
                Console.WriteLine($"Database reconnection warning: {dbEx.Message}");
            }

            return Ok(new {
                message = "تم استعادة النسخة الاحتياطية الكاملة بنجاح",
                requiresRestart = true,
                note = "تم تحديث قاعدة البيانات بنجاح"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = "فشل استعادة النسخة الاحتياطية", error = ex.Message });
        }
    }

    [HttpGet("full-backups")]
    public IActionResult GetFullBackups()
    {
        var fullBackupDir = Path.Combine(Directory.GetCurrentDirectory(), "data", "backups", "full");
        var externalBackupDir = Path.Combine(Directory.GetCurrentDirectory(), "Backups");

        var allFiles = new List<FullBackupInfoDto>();

        // Check internal backup directory
        if (Directory.Exists(fullBackupDir))
        {
            var internalFiles = Directory
                .GetFiles(fullBackupDir, "*.zip", SearchOption.TopDirectoryOnly)
                .Select(path =>
                {
                    var info = new FileInfo(path);
                    return new FullBackupInfoDto(
                        info.Name,
                        info.Length,
                        info.LastWriteTime,
                        FormatFileSize(info.Length)
                    );
                });
            allFiles.AddRange(internalFiles);
        }

        // Check external backup directory (Backups folder)
        if (Directory.Exists(externalBackupDir))
        {
            var externalFiles = Directory
                .GetFiles(externalBackupDir, "*.zip", SearchOption.TopDirectoryOnly)
                .Select(path =>
                {
                    var info = new FileInfo(path);
                    return new FullBackupInfoDto(
                        info.Name,
                        info.Length,
                        info.LastWriteTime,
                        FormatFileSize(info.Length)
                    );
                });
            allFiles.AddRange(externalFiles);
        }

        if (allFiles.Count == 0)
        {
            return Ok(Array.Empty<FullBackupInfoDto>());
        }

        var sortedFiles = allFiles
            .OrderByDescending(b => b.CreatedAt)
            .ToList();

        return Ok(sortedFiles);
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}

public record ResetRequest(string Confirmation);
public record RestoreBackupRequest(string RelativePath);
public record BackupInfoDto(string FileName, string RelativePath, long SizeBytes, DateTime CreatedAt);
public record FullBackupInfoDto(string FileName, long SizeBytes, DateTime CreatedAt, string FormattedSize);


