try {
    Write-Host "Testing restore backup functionality..."
    
    # Path to backup file
    $backupPath = ".\Backups\aredoo_backup_20251119_193800.zip"
    
    if (-not (Test-Path $backupPath)) {
        Write-Host "Backup file not found: $backupPath" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Found backup file: $backupPath"
    Write-Host "File size: $((Get-Item $backupPath).Length) bytes"
    
    # Create multipart form data
    $boundary = [System.Guid]::NewGuid().ToString()
    $LF = "`r`n"
    
    # Read file content
    $fileBytes = [System.IO.File]::ReadAllBytes($backupPath)
    $fileName = [System.IO.Path]::GetFileName($backupPath)
    
    # Create form data
    $bodyLines = (
        "--$boundary",
        "Content-Disposition: form-data; name=`"backupFile`"; filename=`"$fileName`"",
        "Content-Type: application/zip$LF",
        [System.Text.Encoding]::GetEncoding("iso-8859-1").GetString($fileBytes),
        "--$boundary--$LF"
    ) -join $LF
    
    Write-Host "Sending restore request..."
    
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/api/settings/restore-full' `
        -Method POST `
        -ContentType "multipart/form-data; boundary=$boundary" `
        -Body $bodyLines
    
    Write-Host "Success!" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json -Depth 3)"
    
} catch {
    Write-Host "Error occurred:" -ForegroundColor Red
    Write-Host "Exception: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody" -ForegroundColor Red
    }
}
