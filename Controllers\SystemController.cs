using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Hosting;

namespace Aredoo.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SystemController : ControllerBase
    {
        private readonly IHostApplicationLifetime _lifetime;

        public SystemController(IHostApplicationLifetime lifetime)
        {
            _lifetime = lifetime;
        }

        /// <summary>
        /// إيقاف خادم Aredoo بشكل آمن من خلال واجهة الويب.
        /// </summary>
        [HttpPost("shutdown")]
        public IActionResult Shutdown()
        {
            // نعيد الرد أولاً، ثم نطلب من التطبيق الإيقاف بشكل آمن
            _lifetime.StopApplication();

            return Ok(new
            {
                message = "جاري إيقاف الخادم، يمكنك إغلاق هذه النافذة بعد لحظات."
            });
        }
    }
}

