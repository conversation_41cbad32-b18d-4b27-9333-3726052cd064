﻿// Purchases Management

let purchaseCart = [];
let selectedSupplier = null;

async function loadPurchasesPage() {
    const page = document.getElementById('purchasesPage');
    
    page.innerHTML = `
        <div class="row">
            <div class="col-md-8">
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-cart-plus"></i> فاتورة شراء جديدة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">المورد *</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="supplierSearchInput" placeholder="ابحث عن مورد..." autocomplete="off">
                                    <button class="btn btn-outline-secondary" onclick="showAddSupplierModal()">
                                        <i class="bi bi-plus"></i>
                                    </button>
                                </div>
                                <div id="supplierSearchResults" class="list-group mt-1" style="position: absolute; z-index: 1000; max-height: 200px; overflow-y: auto; display: none;"></div>
                                <div id="selectedSupplierInfo" class="mt-2"></div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">تاريخ الشراء</label>
                                <input type="date" class="form-control" id="purchaseDate" value="">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">رقم الفاتورة</label>
                                <input type="text" class="form-control" id="purchaseInvoiceNumber" placeholder="تلقائي">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">المنتج</label>
                                <input type="text" class="form-control" id="productSearchInput" placeholder="ابحث عن منتج..." autocomplete="off">
                                <div id="productSearchResults" class="list-group mt-1" style="position: absolute; z-index: 1000; max-height: 200px; overflow-y: auto; display: none;"></div>
                            </div>
                        </div>

                        <div id="purchaseCartContainer"></div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="purchaseNotes" rows="2"></textarea>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>المجموع الفرعي:</span>
                                            <strong id="purchaseSubTotal">0 د.ع</strong>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الخصم:</span>
                                            <input type="number" class="form-control form-control-sm w-50" id="purchaseDiscount" value="0" min="0" onchange="calculatePurchaseTotal()">
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>الضريبة:</span>
                                            <input type="number" class="form-control form-control-sm w-50" id="purchaseTax" value="0" min="0" onchange="calculatePurchaseTotal()">
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between mb-3">
                                            <strong>الإجمالي:</strong>
                                            <strong id="purchaseTotal" class="text-primary">0 د.ع</strong>
                                        </div>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>المدفوع:</span>
                                            <input type="number" class="form-control form-control-sm w-50" id="purchasePaid" value="0" min="0" onchange="calculatePurchaseTotal()">
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>المتبقي:</span>
                                            <strong id="purchaseRemaining" class="text-danger">0 د.ع</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <label class="form-label">طريقة الدفع</label>
                                <select class="form-select" id="purchasePaymentType">
                                    <option value="Cash">نقدي</option>
                                    <option value="Credit">آجل</option>
                                    <option value="Installment">تقسيط</option>
                                </select>
                            </div>
                        </div>

                        <div class="mt-3">
                            <button class="btn btn-success btn-lg w-100" onclick="savePurchase()">
                                <i class="bi bi-check-circle"></i> حفظ فاتورة الشراء
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-list-ul"></i> فواتير الشراء</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <input type="date" class="form-control mb-2" id="purchasesFromDate" onchange="loadPurchasesList()">
                            <input type="date" class="form-control" id="purchasesToDate" onchange="loadPurchasesList()">
                        </div>
                        <div id="purchasesListContainer" style="max-height: 600px; overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Set today's date
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('purchaseDate').value = today;

    setupSupplierSearch();
    setupProductSearch();
    updatePurchaseCart();
    await loadPurchasesList();
}

function setupSupplierSearch() {
    const input = document.getElementById('supplierSearchInput');
    const results = document.getElementById('supplierSearchResults');

    input.addEventListener('input', async (e) => {
        const query = e.target.value;
        if (query.length < 2) {
            results.style.display = 'none';
            return;
        }

        try {
            const response = await fetch(`/api/suppliers/search?query=${query}`);
            const suppliers = await response.json();

            if (suppliers.length === 0) {
                results.style.display = 'none';
                return;
            }

            results.innerHTML = suppliers.map(s => `
                <a href="#" class="list-group-item list-group-item-action" onclick="selectSupplier(${s.id}, '${s.name}', '${s.code}'); return false;">
                    <strong>${s.name}</strong> - ${s.code} - ${s.phone || ''}
                </a>
            `).join('');
            results.style.display = 'block';
        } catch (error) {
            console.error('Error searching suppliers:', error);
        }
    });

    document.addEventListener('click', (e) => {
        if (!input.contains(e.target) && !results.contains(e.target)) {
            results.style.display = 'none';
        }
    });
}

function selectSupplier(id, name, code) {
    selectedSupplier = { id, name, code };
    document.getElementById('supplierSearchInput').value = name;
    document.getElementById('supplierSearchResults').style.display = 'none';
    document.getElementById('selectedSupplierInfo').innerHTML = `
        <div class="alert alert-info">
            <strong>المورد المختار:</strong> ${name} (${code})
        </div>
    `;
}

function setupProductSearch() {
    const input = document.getElementById('productSearchInput');
    const results = document.getElementById('productSearchResults');

    input.addEventListener('input', async (e) => {
        const query = e.target.value;
        if (query.length < 2) {
            results.style.display = 'none';
            return;
        }

        try {
            const response = await fetch(`/api/products/search?term=${query}`);
            const products = await response.json();

            if (products.length === 0) {
                results.style.display = 'none';
                return;
            }

            results.innerHTML = products.map(p => `
                <a href="#" class="list-group-item list-group-item-action" onclick="addToPurchaseCart(${p.id}, '${p.name}', ${p.purchasePrice}); return false;">
                    <strong>${p.name}</strong> - ${p.code} - المخزون: ${p.quantity}
                </a>
            `).join('');
            results.style.display = 'block';
        } catch (error) {
            console.error('Error searching products:', error);
        }
    });

    document.addEventListener('click', (e) => {
        if (!input.contains(e.target) && !results.contains(e.target)) {
            results.style.display = 'none';
        }
    });
}

function addToPurchaseCart(productId, productName, purchasePrice) {
    const existing = purchaseCart.find(item => item.productId === productId);
    if (existing) {
        existing.quantity++;
    } else {
        purchaseCart.push({
            productId,
            productName,
            unitPrice: purchasePrice,
            quantity: 1,
            discount: 0
        });
    }

    document.getElementById('productSearchInput').value = '';
    document.getElementById('productSearchResults').style.display = 'none';
    updatePurchaseCart();
}

function updatePurchaseCart() {
    const container = document.getElementById('purchaseCartContainer');
    
    if (purchaseCart.length === 0) {
        container.innerHTML = '<div class="alert alert-info">السلة فارغة</div>';
        calculatePurchaseTotal();
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>الخصم</th>
                        <th>المجموع</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
    `;

    purchaseCart.forEach((item, index) => {
        const total = (item.unitPrice * item.quantity) - item.discount;
        html += `
            <tr>
                <td>${item.productName}</td>
                <td><input type="number" class="form-control form-control-sm" value="${item.unitPrice}" min="0" onchange="updatePurchaseItemPrice(${index}, this.value)"></td>
                <td><input type="number" class="form-control form-control-sm" value="${item.quantity}" min="1" onchange="updatePurchaseItemQuantity(${index}, this.value)"></td>
                <td><input type="number" class="form-control form-control-sm" value="${item.discount}" min="0" onchange="updatePurchaseItemDiscount(${index}, this.value)"></td>
                <td>${total.toLocaleString('en-IQ')} د.ع</td>
                <td><button class="btn btn-sm btn-danger" onclick="removeFromPurchaseCart(${index})"><i class="bi bi-trash"></i></button></td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;
    calculatePurchaseTotal();
}

function updatePurchaseItemPrice(index, price) {
    purchaseCart[index].unitPrice = parseFloat(price) || 0;
    updatePurchaseCart();
}

function updatePurchaseItemQuantity(index, quantity) {
    purchaseCart[index].quantity = parseFloat(quantity) || 1;
    updatePurchaseCart();
}

function updatePurchaseItemDiscount(index, discount) {
    purchaseCart[index].discount = parseFloat(discount) || 0;
    updatePurchaseCart();
}

function removeFromPurchaseCart(index) {
    purchaseCart.splice(index, 1);
    updatePurchaseCart();
}

function calculatePurchaseTotal() {
    const subTotal = purchaseCart.reduce((sum, item) => sum + (item.unitPrice * item.quantity) - item.discount, 0);
    const discount = parseFloat(document.getElementById('purchaseDiscount')?.value || 0);
    const tax = parseFloat(document.getElementById('purchaseTax')?.value || 0);
    const total = subTotal - discount + tax;
    const paid = parseFloat(document.getElementById('purchasePaid')?.value || 0);
    const remaining = total - paid;

    document.getElementById('purchaseSubTotal').textContent = formatCurrency(subTotal);
    document.getElementById('purchaseTotal').textContent = formatCurrency(total);
    document.getElementById('purchaseRemaining').textContent = formatCurrency(remaining);
}

async function savePurchase() {
    if (!selectedSupplier) {
        showToast('الرجاء اختيار المورد', 'warning');
        return;
    }

    if (purchaseCart.length === 0) {
        showToast('الرجاء إضافة منتجات للفاتورة', 'warning');
        return;
    }

    const purchaseDate = document.getElementById('purchaseDate').value;
    if (!purchaseDate) {
        showToast('الرجاء اختيار تاريخ الشراء', 'warning');
        return;
    }

    const subTotal = purchaseCart.reduce((sum, item) => sum + (item.unitPrice * item.quantity) - item.discount, 0);
    const discount = parseFloat(document.getElementById('purchaseDiscount').value || 0);
    const tax = parseFloat(document.getElementById('purchaseTax').value || 0);
    const total = subTotal - discount + tax;
    const paid = parseFloat(document.getElementById('purchasePaid').value || 0);

    const purchase = {
        invoiceNumber: document.getElementById('purchaseInvoiceNumber').value || '',
        purchaseDate: purchaseDate,
        supplierId: selectedSupplier.id,
        subTotal: subTotal,
        discount: discount,
        tax: tax,
        total: total,
        paid: paid,
        remaining: total - paid,
        paymentType: document.getElementById('purchasePaymentType').value || 'Cash',
        notes: document.getElementById('purchaseNotes').value || '',
        createdBy: currentUser?.id || 1,
        items: purchaseCart.map(item => ({
            productId: item.productId,
            productName: item.productName,
            quantity: parseFloat(item.quantity),
            unitPrice: parseFloat(item.unitPrice),
            discount: parseFloat(item.discount),
            total: (parseFloat(item.unitPrice) * parseFloat(item.quantity)) - parseFloat(item.discount)
        }))
    };

    console.log('Purchase data to send:', JSON.stringify(purchase, null, 2));

    try {
        const response = await fetch(`/api/purchases`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(purchase)
        });

        if (response.ok) {
            showToast('تم حفظ فاتورة الشراء بنجاح', 'success');
            clearPurchaseForm();
            await loadPurchasesList();
        } else {
            const errorText = await response.text();
            console.error('Server error:', errorText);
            showToast('خطأ في حفظ فاتورة الشراء: ' + response.status, 'error');
        }
    } catch (error) {
        console.error('Error saving purchase:', error);
        showToast('خطأ في حفظ فاتورة الشراء: ' + error.message, 'error');
    }
}

function clearPurchaseForm() {
    purchaseCart = [];
    selectedSupplier = null;
    document.getElementById('supplierSearchInput').value = '';
    document.getElementById('selectedSupplierInfo').innerHTML = '';
    document.getElementById('purchaseInvoiceNumber').value = '';
    document.getElementById('purchaseDiscount').value = '0';
    document.getElementById('purchaseTax').value = '0';
    document.getElementById('purchasePaid').value = '0';
    document.getElementById('purchaseNotes').value = '';
    document.getElementById('purchasePaymentType').value = 'Cash';
    updatePurchaseCart();
}

async function loadPurchasesList() {
    try {
        const fromDate = document.getElementById('purchasesFromDate')?.value || '';
        const toDate = document.getElementById('purchasesToDate')?.value || '';

        let url = `/api/purchases`;
        const params = [];
        if (fromDate) params.push(`fromDate=${fromDate}`);
        if (toDate) params.push(`toDate=${toDate}`);
        if (params.length > 0) url += `?${params.join('&')}`;

        const response = await fetch(url);
        const purchases = await response.json();

        const container = document.getElementById('purchasesListContainer');
        if (purchases.length === 0) {
            container.innerHTML = '<div class="alert alert-info">لا توجد فواتير</div>';
            return;
        }

        let html = '';
        purchases.forEach(purchase => {
            const date = new Date(purchase.purchaseDate).toLocaleDateString('ar-IQ');
            const statusClass = purchase.remaining > 0 ? 'text-danger' : 'text-success';
            
            html += `
                <div class="card mb-2">
                    <div class="card-body p-2">
                        <div class="d-flex justify-content-between">
                            <small><strong></strong></small>
                            <small></small>
                        </div>
                        <div><small></small></div>
                        <div class="d-flex justify-content-between">
                            <small>الإجمالي: </small>
                            <small class="">المتبقي: </small>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    } catch (error) {
        console.error('Error loading purchases:', error);
        showToast('خطأ في تحميل فواتير الشراء', 'error');
    }
}
