async function loadCategoriesPage() {
    const page = document.getElementById('categoriesPage');
    page.innerHTML = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-grid-3x3-gap"></i> إدارة الفئات</h5>
                <button class="btn btn-primary" onclick="showCategoryModal()" data-permission-module="Categories" data-permission-action="add">
                    <i class="bi bi-plus-circle"></i> فئة جديدة
                </button>
            </div>
            <div class="card-body">
                <div class="row" id="categoriesGrid"></div>
            </div>
        </div>
        
        <!-- Modal إضافة/تعديل فئة -->
        <div class="modal fade" id="categoryModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="categoryModalTitle">
                            <i class="bi bi-grid-3x3-gap"></i> فئة جديدة
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="categoryForm">
                            <input type="hidden" id="categoryId">
                            
                            <div class="mb-3">
                                <label class="form-label">اسم الفئة *</label>
                                <input type="text" class="form-control" id="categoryName" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" id="categoryDescription" rows="2"></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الأيقونة</label>
                                    <select class="form-select" id="categoryIcon">
                                        <option value="bi-box-seam">📦 صندوق</option>
                                        <option value="bi-cup-straw">🥤 مشروبات</option>
                                        <option value="bi-basket">🧺 سلة</option>
                                        <option value="bi-phone">📱 إلكترونيات</option>
                                        <option value="bi-laptop">💻 حواسيب</option>
                                        <option value="bi-bag">👜 حقائب</option>
                                        <option value="bi-cart">🛒 بقالة</option>
                                        <option value="bi-heart">❤️ صحة</option>
                                        <option value="bi-book">📚 كتب</option>
                                        <option value="bi-tools">🔧 أدوات</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">اللون</label>
                                    <select class="form-select" id="categoryColor">
                                        <option value="#667eea">🔵 أزرق</option>
                                        <option value="#f093fb">🟣 بنفسجي</option>
                                        <option value="#4facfe">🔷 سماوي</option>
                                        <option value="#43e97b">🟢 أخضر</option>
                                        <option value="#fa709a">🔴 أحمر</option>
                                        <option value="#feca57">🟡 أصفر</option>
                                        <option value="#ff6348">🟠 برتقالي</option>
                                        <option value="#ee5a6f">🌸 وردي</option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle"></i> إلغاء
                        </button>
                        <button type="button" class="btn btn-primary" onclick="saveCategory()">
                            <i class="bi bi-check-circle"></i> حفظ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    loadCategories();
}

async function loadCategories() {
    try {
        const response = await fetch(`${API_URL}/categories`);
        const categories = await response.json();
        
        const grid = document.getElementById('categoriesGrid');
        if (categories.length === 0) {
            grid.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="bi bi-grid-3x3-gap" style="font-size: 3rem; color: #ccc;"></i>
                    <p class="text-muted mt-3">لا توجد فئات. أضف فئة جديدة للبدء.</p>
                </div>
            `;
            return;
        }
        
        grid.innerHTML = categories.map(c => `
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card h-100" style="border-top: 4px solid ${c.color || '#667eea'};">
                    <div class="card-body text-center">
                        <i class="bi ${c.icon || 'bi-box-seam'}" style="font-size: 2.5rem; color: ${c.color || '#667eea'};"></i>
                        <h5 class="mt-3">${c.name}</h5>
                        ${c.description ? `<p class="text-muted small">${c.description}</p>` : ''}
                        <div class="mt-3">
                            <button class="btn btn-sm btn-info" onclick="editCategory(${c.id})" data-permission-module="Categories" data-permission-action="edit">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteCategory(${c.id})" data-permission-module="Categories" data-permission-action="delete">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    } catch (error) {
        console.error('Error loading categories:', error);
    }
}

function showCategoryModal(category = null) {
    const modal = new bootstrap.Modal(document.getElementById('categoryModal'));
    const form = document.getElementById('categoryForm');
    form.reset();
    
    document.getElementById('categoryModalTitle').innerHTML = category ? 
        '<i class="bi bi-pencil"></i> تعديل فئة' : 
        '<i class="bi bi-grid-3x3-gap"></i> فئة جديدة';
    
    if (category) {
        document.getElementById('categoryId').value = category.id;
        document.getElementById('categoryName').value = category.name;
        document.getElementById('categoryDescription').value = category.description || '';
        document.getElementById('categoryIcon').value = category.icon || 'bi-box-seam';
        document.getElementById('categoryColor').value = category.color || '#667eea';
    }
    
    modal.show();
}

async function saveCategory() {
    const id = document.getElementById('categoryId').value;

    const categoryData = {
        name: document.getElementById('categoryName').value,
        description: document.getElementById('categoryDescription').value || null,
        icon: document.getElementById('categoryIcon').value,
        color: document.getElementById('categoryColor').value,
        isActive: true
    };

    try {
        let response;
        if (id) {
            categoryData.id = parseInt(id);
            response = await fetch(`${API_URL}/categories/${id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(categoryData)
            });
        } else {
            response = await fetch(`${API_URL}/categories`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(categoryData)
            });
        }

        if (response.ok) {
            showToast(id ? 'تم تحديث الفئة بنجاح' : 'تم إضافة الفئة بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('categoryModal')).hide();
            loadCategories();
        } else {
            showToast('فشل حفظ الفئة', 'danger');
        }
    } catch (error) {
        console.error('Error saving category:', error);
        showToast('حدث خطأ أثناء حفظ الفئة', 'danger');
    }
}

async function editCategory(id) {
    try {
        const response = await fetch(`${API_URL}/categories/${id}`);
        const category = await response.json();
        showCategoryModal(category);
    } catch (error) {
        console.error('Error loading category:', error);
        showToast('فشل تحميل بيانات الفئة', 'danger');
    }
}

async function deleteCategory(id) {
    if (!confirm('هل أنت متأكد من حذف هذه الفئة؟')) return;

    try {
        const response = await fetch(`${API_URL}/categories/${id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            showToast('تم حذف الفئة بنجاح', 'success');
            loadCategories();
        } else {
            showToast('فشل حذف الفئة', 'danger');
        }
    } catch (error) {
        console.error('Error deleting category:', error);
        showToast('حدث خطأ أثناء حذف الفئة', 'danger');
    }
}

