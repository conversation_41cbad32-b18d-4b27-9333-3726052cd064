namespace Aredoo.Server.Models;

public class Permission
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public string Module { get; set; } = string.Empty; // Sales, Products, Customers, etc.
    public bool CanView { get; set; } = false;
    public bool CanAdd { get; set; } = false;
    public bool CanEdit { get; set; } = false;
    public bool CanDelete { get; set; } = false;
    public bool CanPrint { get; set; } = false;
    public bool CanExport { get; set; } = false;

    // Navigation property
    public User User { get; set; } = null!;
}

