@echo off
chcp 65001 > nul
title Aredoo POS System - Java

echo ═══════════════════════════════════════════════════════
echo   🏪 أريدوو - نظام إدارة المبيعات والمخزون
echo   Aredoo POS System (Java Version)
echo ═══════════════════════════════════════════════════════
echo   📅 تم التحويل من C# إلى Java بنجاح
echo   🚀 جاهز للتشغيل...
echo ═══════════════════════════════════════════════════════

cd /d "%~dp0"

REM Check Java installation
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Java غير مثبت على النظام
    echo.
    echo يرجى تثبيت Java 17 أو أحدث من:
    echo https://adoptium.net/
    echo.
    pause
    exit /b 1
)

echo ✅ Java متوفر ومثبت بشكل صحيح

REM Create necessary directories
if not exist "data" (
    mkdir data
    echo 📁 تم إنشاء مجلد قاعدة البيانات
)

if not exist "logs" (
    mkdir logs
    echo 📁 تم إنشاء مجلد السجلات
)

echo.
echo 🌐 بدء تشغيل الخادم...
echo 📍 العنوان: http://localhost:5000
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: 1234
echo.
echo 💡 نصائح:
echo   - سيتم فتح المتصفح تلقائياً
echo   - اضغط Ctrl+C لإيقاف الخادم
echo   - البيانات محفوظة في مجلد data
echo.
echo ═══════════════════════════════════════════════════════

REM Try different methods to run the application
if exist "target\aredoo-pos-1.0.0.jar" (
    echo 🚀 تشغيل من Maven build...
    java -jar target\aredoo-pos-1.0.0.jar
) else if exist "build\libs\aredoo-pos-1.0.0.jar" (
    echo 🚀 تشغيل من Gradle build...
    java -jar build\libs\aredoo-pos-1.0.0.jar
) else (
    REM Try Maven if available
    mvn -version >nul 2>&1
    if not errorlevel 1 (
        echo 🔨 بناء وتشغيل باستخدام Maven...
        mvn spring-boot:run
    ) else (
        echo ❌ لم يتم العثور على ملف JAR جاهز
        echo.
        echo الحلول المتاحة:
        echo 1. تثبيت Maven وتشغيل: mvn spring-boot:run
        echo 2. بناء التطبيق باستخدام IDE
        echo 3. الحصول على ملف JAR جاهز
        echo.
        pause
        exit /b 1
    )
)

echo.
echo 🛑 تم إيقاف الخادم
pause
