namespace Aredoo.Server.Models;

public class Settings
{
    public int Id { get; set; }
    public string CompanyName { get; set; } = "أريدوو - Aredoo";
    public string CompanyNameAr { get; set; } = "أريدوو";
    public string? CompanyLogo { get; set; }
    public string? Address { get; set; }
    public string? Phone { get; set; }
    public string? Email { get; set; }
    public string Currency { get; set; } = "IQD";
    public string CurrencySymbol { get; set; } = "د.ع";
    public string Language { get; set; } = "ar";
    public string InvoiceFooter { get; set; } = "شكراً لتعاملكم معنا";
    public string PrinterName { get; set; } = "Default";
    public string PaperSize { get; set; } = "80mm";
    public bool AutoPrint { get; set; } = false;
    public decimal TaxRate { get; set; } = 0;
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
}

