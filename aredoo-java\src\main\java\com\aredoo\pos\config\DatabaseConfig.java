package com.aredoo.pos.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import jakarta.annotation.PostConstruct;
import java.io.File;

@Configuration
@EnableJpaRepositories(basePackages = "com.aredoo.pos.repository")
public class DatabaseConfig {

    @PostConstruct
    public void init() {
        // Create data directory if it doesn't exist
        File dataDir = new File("data");
        if (!dataDir.exists()) {
            dataDir.mkdirs();
            System.out.println("✓ تم إنشاء مجلد قاعدة البيانات: data/");
        }
    }
}
