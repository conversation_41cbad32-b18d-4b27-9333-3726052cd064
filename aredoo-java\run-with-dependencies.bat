@echo off
chcp 65001 > nul
title Aredoo POS System - Java

echo ═══════════════════════════════════════════════════════
echo   أريدوو - Aredoo POS System (Java Version)
echo   نظام إدارة المبيعات والمخزون
echo ═══════════════════════════════════════════════════════

cd /d "%~dp0"

REM Check if Java is installed
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ Java غير مثبت على النظام
    echo يرجى تثبيت Java 17 أو أحدث من: https://adoptium.net/
    pause
    exit /b 1
)

echo ✅ Java متوفر
echo 📦 تحضير التطبيق...

REM Create data directory
if not exist "data" mkdir data

echo 🌐 تشغيل الخادم على http://localhost:5000
echo 👤 المستخدم الافتراضي: admin
echo 🔑 كلمة المرور: 1234
echo ═══════════════════════════════════════════════════════
echo   اضغط Ctrl+C للإيقاف
echo ═══════════════════════════════════════════════════════

REM Note: This requires the JAR file to be built first
if exist "build\libs\aredoo-pos-1.0.0.jar" (
    java -jar build\libs\aredoo-pos-1.0.0.jar
) else (
    echo ❌ ملف JAR غير موجود
    echo يرجى بناء التطبيق أولاً باستخدام: gradlew.bat build
    echo أو استخدام: start-aredoo.bat
)

pause
