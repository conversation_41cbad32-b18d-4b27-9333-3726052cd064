# Server Configuration
server.port=5000
server.servlet.context-path=/

# Database Configuration (H2)
spring.datasource.url=jdbc:h2:file:./data/aredoo;DB_CLOSE_ON_EXIT=FALSE;AUTO_RECONNECT=TRUE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true

# JSON Configuration
spring.jackson.serialization.fail-on-empty-beans=false
spring.jackson.default-property-inclusion=non_null

# Static Resources
spring.web.resources.static-locations=classpath:/static/
spring.mvc.static-path-pattern=/**

# Logging
logging.level.com.aredoo=INFO
logging.level.org.springframework.web=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Application Info
app.name=Aredoo POS System
app.version=1.0.0
app.description=نظام إدارة المبيعات والمخزون
