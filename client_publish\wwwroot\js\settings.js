// Get API URL from global scope or use default
function getApiUrl() {
    return (typeof window.API_URL !== 'undefined' && window.API_URL) ? window.API_URL : '/api';
}

async function loadSettingsPage() {
    const page = document.getElementById('settingsPage');
    page.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0"><i class="bi bi-gear"></i> إعدادات النظام</h4>
            </div>
            <div class="card-body">
                <!-- Tabs Navigation -->
                <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button">
                            <i class="bi bi-building"></i> عام
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="printer-tab" data-bs-toggle="tab" data-bs-target="#printer" type="button">
                            <i class="bi bi-printer"></i> الطباعة
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button">
                            <i class="bi bi-people"></i> المستخدمين
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="permissions-tab" data-bs-toggle="tab" data-bs-target="#permissions" type="button">
                            <i class="bi bi-shield-lock"></i> الصلاحيات
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button">
                            <i class="bi bi-database"></i> النسخ الاحتياطي
                        </button>
                    </li>
                </ul>

                <!-- Tabs Content -->
                <div class="tab-content" id="settingsTabContent">
                    <!-- General Settings Tab -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel">
                        <form id="settingsForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="mb-3"><i class="bi bi-info-circle"></i> معلومات الشركة</h5>
                                    <div class="mb-3">
                                        <label class="form-label">اسم الشركة (English)</label>
                                        <input type="text" class="form-control" id="companyName" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">اسم الشركة (عربي)</label>
                                        <input type="text" class="form-control" id="companyNameAr" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">العنوان</label>
                                        <textarea class="form-control" id="address" rows="2"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الهاتف</label>
                                        <input type="text" class="form-control" id="phone">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="email">
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h5 class="mb-3"><i class="bi bi-cash-stack"></i> إعدادات المالية</h5>
                                    <div class="mb-3">
                                        <label class="form-label">العملة</label>
                                        <select class="form-select" id="currency">
                                            <option value="IQD">دينار عراقي (IQD)</option>
                                            <option value="USD">دولار أمريكي (USD)</option>
                                            <option value="EUR">يورو (EUR)</option>
                                            <option value="SAR">ريال سعودي (SAR)</option>
                                            <option value="AED">درهم إماراتي (AED)</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">رمز العملة</label>
                                        <input type="text" class="form-control" id="currencySymbol" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">نسبة الضريبة (%)</label>
                                        <input type="number" class="form-control" id="taxRate" step="0.01" min="0" max="100">
                                    </div>

                                    <h5 class="mb-3 mt-4"><i class="bi bi-receipt"></i> إعدادات الفاتورة</h5>
                                    <div class="mb-3">
                                        <label class="form-label">نص تذييل الفاتورة</label>
                                        <textarea class="form-control" id="invoiceFooter" rows="3"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">اللغة الافتراضية</label>
                                        <select class="form-select" id="language">
                                            <option value="ar">العربية</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="text-end mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-save"></i> حفظ الإعدادات العامة
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Printer Settings Tab -->
                    <div class="tab-pane fade" id="printer" role="tabpanel">
                        <form id="printerForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="mb-3"><i class="bi bi-printer"></i> نوع الطابعة</h5>
                                    <div class="mb-3">
                                        <label class="form-label">نوع الطابعة</label>
                                        <select class="form-select" id="printerType" onchange="updatePrinterFields()">
                                            <option value="browser">طابعة المتصفح (افتراضي)</option>
                                            <option value="thermal">طابعة حرارية (Thermal)</option>
                                            <option value="bluetooth">طابعة بلوتوث</option>
                                            <option value="usb">طابعة USB</option>
                                            <option value="network">طابعة شبكة (IP)</option>
                                        </select>
                                    </div>

                                    <div id="bluetoothSettings" class="printer-settings d-none">
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle"></i>
                                            سيتم البحث عن الطابعات المتاحة عبر البلوتوث عند الطباعة
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">اسم الطابعة (اختياري)</label>
                                            <input type="text" class="form-control" id="bluetoothPrinterName"
                                                   placeholder="مثال: POS-58">
                                        </div>
                                    </div>

                                    <div id="usbSettings" class="printer-settings d-none">
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle"></i>
                                            تأكد من توصيل الطابعة عبر USB وتثبيت التعريفات
                                        </div>

                                        <div class="mb-3">
                                            <button type="button" class="btn btn-primary w-100" onclick="detectUSBPrinters()">
                                                <i class="bi bi-search"></i> البحث عن الطابعات المتصلة
                                            </button>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">الطابعات المكتشفة</label>
                                            <select class="form-select" id="detectedPrinters" onchange="selectDetectedPrinter()">
                                                <option value="">-- اختر طابعة --</option>
                                            </select>
                                        </div>

                                        <hr>

                                        <div class="mb-3">
                                            <label class="form-label">Vendor ID</label>
                                            <input type="text" class="form-control" id="usbVendorId"
                                                   placeholder="مثال: 0x04b8">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Product ID</label>
                                            <input type="text" class="form-control" id="usbProductId"
                                                   placeholder="مثال: 0x0e15">
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">اسم الطابعة</label>
                                            <input type="text" class="form-control" id="usbPrinterName"
                                                   placeholder="مثال: POS-80" readonly>
                                        </div>

                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="testUSBPrinter()">
                                            <i class="bi bi-printer"></i> اختبار الطباعة
                                        </button>
                                    </div>

                                    <div id="networkSettings" class="printer-settings d-none">
                                        <div class="mb-3">
                                            <label class="form-label">عنوان IP الطابعة</label>
                                            <input type="text" class="form-control" id="printerIp"
                                                   placeholder="مثال: *************">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">المنفذ (Port)</label>
                                            <input type="number" class="form-control" id="printerPort"
                                                   placeholder="9100" value="9100">
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="testPrinterConnection()">
                                            <i class="bi bi-wifi"></i> اختبار الاتصال
                                        </button>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h5 class="mb-3"><i class="bi bi-file-earmark-text"></i> إعدادات الورق</h5>
                                    <div class="mb-3">
                                        <label class="form-label">عرض الورق</label>
                                        <select class="form-select" id="paperWidth">
                                            <option value="58">58 مم</option>
                                            <option value="80" selected>80 مم</option>
                                            <option value="A4">A4</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">عدد النسخ</label>
                                        <input type="number" class="form-control" id="printCopies" value="1" min="1" max="5">
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">هوامش الطباعة (مم)</label>
                                        <div class="row g-2">
                                            <div class="col-6">
                                                <input type="number" class="form-control" id="marginTop" placeholder="أعلى" value="5" min="0" max="20">
                                            </div>
                                            <div class="col-6">
                                                <input type="number" class="form-control" id="marginBottom" placeholder="أسفل" value="5" min="0" max="20">
                                            </div>
                                            <div class="col-6">
                                                <input type="number" class="form-control" id="marginLeft" placeholder="يسار" value="5" min="0" max="20">
                                            </div>
                                            <div class="col-6">
                                                <input type="number" class="form-control" id="marginRight" placeholder="يمين" value="5" min="0" max="20">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="autoPrint">
                                        <label class="form-check-label" for="autoPrint">
                                            طباعة تلقائية بعد حفظ الفاتورة
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="printLogo" checked>
                                        <label class="form-check-label" for="printLogo">
                                            طباعة شعار الشركة
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="printBarcode" checked>
                                        <label class="form-check-label" for="printBarcode">
                                            طباعة باركود الفاتورة
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="printHeader" checked>
                                        <label class="form-check-label" for="printHeader">
                                            طباعة رأس الفاتورة
                                        </label>
                                    </div>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="printFooter" checked>
                                        <label class="form-check-label" for="printFooter">
                                            طباعة تذييل الفاتورة
                                        </label>
                                    </div>

                                    <h5 class="mb-3 mt-4"><i class="bi bi-fonts"></i> إعدادات النص</h5>
                                    <div class="mb-3">
                                        <label class="form-label">حجم الخط</label>
                                        <select class="form-select" id="fontSize">
                                            <option value="small">صغير (10px)</option>
                                            <option value="medium" selected>متوسط (12px)</option>
                                            <option value="large">كبير (14px)</option>
                                            <option value="xlarge">كبير جداً (16px)</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">نمط الخط</label>
                                        <select class="form-select" id="fontFamily">
                                            <option value="Cairo">Cairo (افتراضي)</option>
                                            <option value="Tajawal">Tajawal</option>
                                            <option value="Almarai">Almarai</option>
                                            <option value="Arial">Arial</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">لون الرأس</label>
                                        <input type="color" class="form-control form-control-color" id="headerColor" value="#667eea">
                                    </div>
                                </div>
                            </div>

                            <div class="text-end mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-save"></i> حفظ إعدادات الطباعة
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Users Tab -->
                    <div class="tab-pane fade" id="users" role="tabpanel">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0"><i class="bi bi-people"></i> إدارة المستخدمين</h5>
                            <button class="btn btn-primary" onclick="showAddUserModal()">
                                <i class="bi bi-person-plus"></i> مستخدم جديد
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المستخدم</th>
                                        <th>الاسم الكامل</th>
                                        <th>الدور</th>
                                        <th>الحالة</th>
                                        <th>آخر دخول</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <tr>
                                        <td colspan="8" class="text-center">
                                            <div class="spinner-border" role="status">
                                                <span class="visually-hidden">جاري التحميل...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Permissions Tab -->
                    <div class="tab-pane fade" id="permissions" role="tabpanel">
                        <h5 class="mb-4"><i class="bi bi-shield-lock"></i> صلاحيات الأدوار</h5>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="list-group" id="rolesList" role="tablist">
                                    <a class="list-group-item list-group-item-action active" data-bs-toggle="list" href="#admin-permissions">
                                        <i class="bi bi-shield-fill-check text-danger"></i> مدير النظام (Admin)
                                    </a>
                                    <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#manager-permissions">
                                        <i class="bi bi-person-badge text-primary"></i> مدير (Manager)
                                    </a>
                                    <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#cashier-permissions">
                                        <i class="bi bi-cash-coin text-success"></i> كاشير (Cashier)
                                    </a>
                                    <a class="list-group-item list-group-item-action" data-bs-toggle="list" href="#employee-permissions">
                                        <i class="bi bi-person text-secondary"></i> موظف (Employee)
                                    </a>
                                </div>
                            </div>

                            <div class="col-md-9">
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="admin-permissions">
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle"></i>
                                            <strong>مدير النظام</strong> لديه صلاحيات كاملة على جميع أجزاء النظام
                                        </div>
                                        <div id="adminPermissionsContent"></div>
                                    </div>

                                    <div class="tab-pane fade" id="manager-permissions">
                                        <div id="managerPermissionsContent"></div>
                                    </div>

                                    <div class="tab-pane fade" id="cashier-permissions">
                                        <div id="cashierPermissionsContent"></div>
                                    </div>

                                    <div class="tab-pane fade" id="employee-permissions">
                                        <div id="employeePermissionsContent"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Backup Tab -->
                    <div class="tab-pane fade" id="backup" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-success mb-3">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0"><i class="bi bi-download"></i> النسخ الاحتياطي الكامل</h5>
                                    </div>
                                    <div class="card-body">
                                        <p>إنشاء نسخة احتياطية كاملة (قاعدة البيانات + الصور + الإعدادات)</p>
                                        <ul class="list-unstyled">
                                            <li><i class="bi bi-check-circle text-success"></i> حفظ جميع البيانات</li>
                                            <li><i class="bi bi-check-circle text-success"></i> حفظ الصور والملفات</li>
                                            <li><i class="bi bi-check-circle text-success"></i> ملف ZIP قابل للتحميل</li>
                                            <li><i class="bi bi-check-circle text-success"></i> إمكانية الاستعادة الكاملة</li>
                                        </ul>
                                        <button class="btn btn-success w-100 btn-lg mb-2" onclick="createFullBackup()">
                                            <i class="bi bi-archive"></i> إنشاء نسخة احتياطية كاملة
                                        </button>
                                        <button class="btn btn-outline-success w-100" onclick="createBackup()">
                                            <i class="bi bi-database"></i> نسخة احتياطية سريعة (قاعدة البيانات فقط)
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card border-danger">
                                    <div class="card-header bg-danger text-white">
                                        <h5 class="mb-0"><i class="bi bi-exclamation-triangle"></i> تصفير النظام</h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-danger">
                                            <strong>تحذير:</strong> سيتم حذف جميع البيانات التالية:
                                        </p>
                                        <ul class="text-danger">
                                            <li>الفواتير والمبيعات</li>
                                            <li>المنتجات والمخزون</li>
                                            <li>العملاء والموردين</li>
                                            <li>الموظفين والحضور</li>
                                        </ul>
                                        <p class="text-success">
                                            <i class="bi bi-shield-check"></i> سيتم الاحتفاظ بـ: المستخدمين والإعدادات
                                        </p>
                                        <p class="text-info">
                                            <i class="bi bi-info-circle"></i> سيتم إنشاء نسخة احتياطية تلقائياً
                                        </p>
                                        <button class="btn btn-danger w-100 btn-lg" onclick="resetSystem()">
                                            <i class="bi bi-exclamation-triangle"></i> تصفير النظام
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- قسم النسخ الاحتياطية المحفوظة -->
                            <div class="col-12 mt-3">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0"><i class="bi bi-archive"></i> النسخ الاحتياطية المحفوظة</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6><i class="bi bi-cloud-download"></i> تحميل النسخ الاحتياطية</h6>
                                                <div id="fullBackupsList" class="mb-3">
                                                    <div class="text-center">
                                                        <div class="spinner-border text-info" role="status">
                                                            <span class="visually-hidden">جاري التحميل...</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <button class="btn btn-info w-100" onclick="loadFullBackups()">
                                                    <i class="bi bi-arrow-clockwise"></i> تحديث القائمة
                                                </button>
                                            </div>
                                            <div class="col-md-6">
                                                <h6><i class="bi bi-cloud-upload"></i> استعادة نسخة احتياطية</h6>
                                                <div class="mb-3">
                                                    <label for="backupFileInput" class="form-label">اختر ملف النسخة الاحتياطية (.zip)</label>
                                                    <input type="file" class="form-control" id="backupFileInput" accept=".zip">
                                                </div>
                                                <button class="btn btn-warning w-100" onclick="restoreFullBackup()">
                                                    <i class="bi bi-upload"></i> استعادة النسخة الاحتياطية
                                                </button>
                                                <div class="alert alert-warning mt-2 small">
                                                    <i class="bi bi-exclamation-triangle"></i>
                                                    سيتم إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 mt-3">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h5 class="mb-0"><i class="bi bi-power"></i> إيقاف الخادم</h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-3">
                                            عند الضغط على الزر أدناه سيتم إيقاف خادم أريدوو على هذا الجهاز.
                                        </p>
                                        <button class="btn btn-outline-secondary w-100 btn-lg" onclick="shutdownServer()">
                                            <i class="bi bi-power"></i> إيقاف السيرفر الآن
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    loadSettings();
    loadUsers();
    loadPermissions();

    document.getElementById('settingsForm').addEventListener('submit', saveSettings);
    document.getElementById('printerForm').addEventListener('submit', savePrinterSettings);
}

function updatePrinterFields() {
    const printerType = document.getElementById('printerType')?.value || 'browser';

    // إخفاء جميع إعدادات الطابعة
    document.querySelectorAll('.printer-settings').forEach(el => {
        el.classList.add('d-none');
    });

    // إظهار الإعدادات المناسبة
    if (printerType === 'bluetooth') {
        document.getElementById('bluetoothSettings')?.classList.remove('d-none');
    } else if (printerType === 'usb') {
        document.getElementById('usbSettings')?.classList.remove('d-none');
    } else if (printerType === 'network') {
        document.getElementById('networkSettings')?.classList.remove('d-none');
    }
}

async function testPrinterConnection() {
    const ip = document.getElementById('printerIp')?.value;
    const port = document.getElementById('printerPort')?.value || 9100;

    if (!ip) {
        showToast('الرجاء إدخال عنوان IP', 'warning');
        return;
    }

    showToast('جاري اختبار الاتصال...', 'info');

    try {
        // محاولة الاتصال بالطابعة
        const response = await fetch(`http://${ip}:${port}`, {
            method: 'HEAD',
            mode: 'no-cors'
        });

        showToast('✅ تم الاتصال بالطابعة بنجاح', 'success');
    } catch (error) {
        showToast('❌ فشل الاتصال بالطابعة. تحقق من العنوان والمنفذ', 'danger');
    }
}

async function loadSettings() {
    try {
        const response = await fetch(`${getApiUrl()}/settings`);
        const settings = await response.json();

        document.getElementById('companyName').value = settings.companyName;
        document.getElementById('companyNameAr').value = settings.companyNameAr;
        document.getElementById('address').value = settings.address || '';
        document.getElementById('phone').value = settings.phone || '';
        document.getElementById('email').value = settings.email || '';
        document.getElementById('currency').value = settings.currency;
        document.getElementById('currencySymbol').value = settings.currencySymbol;
        document.getElementById('invoiceFooter').value = settings.invoiceFooter;
        document.getElementById('taxRate').value = settings.taxRate;
        document.getElementById('language').value = settings.language || 'ar';

        // تحميل إعدادات الطابعة من localStorage
        const printerSettings = JSON.parse(localStorage.getItem('printerSettings') || '{}');
        document.getElementById('printerType').value = printerSettings.type || 'browser';
        document.getElementById('bluetoothPrinterName').value = printerSettings.bluetoothName || '';
        document.getElementById('usbVendorId').value = printerSettings.usbVendorId || '';

        // تحميل النسخ الاحتياطية
        loadFullBackups();
        document.getElementById('usbProductId').value = printerSettings.usbProductId || '';
        document.getElementById('printerIp').value = printerSettings.ip || '';
        document.getElementById('printerPort').value = printerSettings.port || '9100';
        document.getElementById('paperWidth').value = printerSettings.paperWidth || '80';
        document.getElementById('printCopies').value = printerSettings.copies || '1';
        document.getElementById('autoPrint').checked = printerSettings.autoPrint || false;
        document.getElementById('printLogo').checked = printerSettings.printLogo !== false;
        document.getElementById('printBarcode').checked = printerSettings.printBarcode !== false;
        document.getElementById('fontSize').value = printerSettings.fontSize || 'medium';

        updatePrinterFields();
    } catch (error) {
        console.error('Error loading settings:', error);
    }
}

async function saveSettings(e) {
    e.preventDefault();

    const settings = {
        id: 1,
        companyName: document.getElementById('companyName').value,
        companyNameAr: document.getElementById('companyNameAr').value,
        address: document.getElementById('address').value,
        phone: document.getElementById('phone').value,
        email: document.getElementById('email').value,
        currency: document.getElementById('currency').value,
        currencySymbol: document.getElementById('currencySymbol').value,
        invoiceFooter: document.getElementById('invoiceFooter').value,
        taxRate: parseFloat(document.getElementById('taxRate').value),
        language: document.getElementById('language').value
    };

    try {
        const response = await fetch(`${getApiUrl()}/settings`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(settings)
        });

        if (response.ok) {
            showToast('✅ تم حفظ الإعدادات العامة بنجاح', 'success');
        }
    } catch (error) {
        console.error('Error saving settings:', error);
        showToast('❌ فشل حفظ الإعدادات', 'danger');
    }
}

async function savePrinterSettings(e) {
    e.preventDefault();

    const printerSettings = {
        type: document.getElementById('printerType').value,
        bluetoothName: document.getElementById('bluetoothPrinterName').value,
        usbVendorId: document.getElementById('usbVendorId').value,
        usbProductId: document.getElementById('usbProductId').value,
        usbPrinterName: document.getElementById('usbPrinterName').value,
        ip: document.getElementById('printerIp').value,
        port: document.getElementById('printerPort').value,
        paperWidth: document.getElementById('paperWidth').value,
        copies: document.getElementById('printCopies').value,
        autoPrint: document.getElementById('autoPrint').checked,
        printLogo: document.getElementById('printLogo').checked,
        printBarcode: document.getElementById('printBarcode').checked,
        printHeader: document.getElementById('printHeader').checked,
        printFooter: document.getElementById('printFooter').checked,
        fontSize: document.getElementById('fontSize').value,
        fontFamily: document.getElementById('fontFamily').value,
        headerColor: document.getElementById('headerColor').value,
        marginTop: document.getElementById('marginTop').value,
        marginBottom: document.getElementById('marginBottom').value,
        marginLeft: document.getElementById('marginLeft').value,
        marginRight: document.getElementById('marginRight').value
    };

    localStorage.setItem('printerSettings', JSON.stringify(printerSettings));
    showToast('✅ تم حفظ إعدادات الطباعة بنجاح', 'success');
}

// دالة الكشف عن الطابعات USB
async function detectUSBPrinters() {
    try {
        if (!navigator.usb) {
            showToast('❌ متصفحك لا يدعم USB API. استخدم Chrome أو Edge', 'danger');
            return;
        }

        // طلب الوصول إلى أجهزة USB
        const device = await navigator.usb.requestDevice({
            filters: [
                { classCode: 0x07 } // Printer class
            ]
        });

        if (device) {
            const select = document.getElementById('detectedPrinters');
            const option = document.createElement('option');
            option.value = JSON.stringify({
                vendorId: '0x' + device.vendorId.toString(16).padStart(4, '0'),
                productId: '0x' + device.productId.toString(16).padStart(4, '0'),
                name: device.productName || 'طابعة USB'
            });
            option.textContent = `${device.productName || 'طابعة USB'} (VID: ${device.vendorId.toString(16)}, PID: ${device.productId.toString(16)})`;
            select.appendChild(option);

            showToast('✅ تم اكتشاف الطابعة بنجاح!', 'success');
        }
    } catch (error) {
        console.error('Error detecting USB printers:', error);
        if (error.name === 'NotFoundError') {
            showToast('⚠️ لم يتم العثور على طابعات. تأكد من توصيل الطابعة', 'warning');
        } else {
            showToast('❌ خطأ في الكشف عن الطابعات: ' + error.message, 'danger');
        }
    }
}

// دالة اختيار طابعة مكتشفة
function selectDetectedPrinter() {
    const select = document.getElementById('detectedPrinters');
    if (select.value) {
        const printerInfo = JSON.parse(select.value);
        document.getElementById('usbVendorId').value = printerInfo.vendorId;
        document.getElementById('usbProductId').value = printerInfo.productId;
        document.getElementById('usbPrinterName').value = printerInfo.name;
        showToast('✅ تم تحديد الطابعة', 'success');
    }
}

// دالة اختبار الطابعة USB
async function testUSBPrinter() {
    const vendorId = document.getElementById('usbVendorId').value;
    const productId = document.getElementById('usbProductId').value;

    if (!vendorId || !productId) {
        showToast('⚠️ يرجى إدخال Vendor ID و Product ID', 'warning');
        return;
    }

    try {
        if (!navigator.usb) {
            showToast('❌ متصفحك لا يدعم USB API', 'danger');
            return;
        }

        // محاولة الاتصال بالطابعة
        const devices = await navigator.usb.getDevices();
        const vendorIdNum = parseInt(vendorId);
        const productIdNum = parseInt(productId);

        const device = devices.find(d => d.vendorId === vendorIdNum && d.productId === productIdNum);

        if (device) {
            await device.open();
            showToast('✅ تم الاتصال بالطابعة بنجاح!', 'success');
            await device.close();
        } else {
            showToast('⚠️ لم يتم العثور على الطابعة. جرب "البحث عن الطابعات"', 'warning');
        }
    } catch (error) {
        console.error('Error testing USB printer:', error);
        showToast('❌ خطأ في الاتصال بالطابعة: ' + error.message, 'danger');
    }
}

async function createBackup() {
    try {
        const response = await fetch(`${getApiUrl()}/settings/backup`, {
            method: 'POST'
        });

        if (response.ok) {
            const result = await response.json();
            showToast(result.message, 'success');
            // تحديث قائمة النسخ الاحتياطية
            loadFullBackups();
        }
    } catch (error) {
        console.error('Error creating backup:', error);
        showToast('فشل إنشاء النسخة الاحتياطية', 'danger');
    }
}

async function createFullBackup() {
    try {
        showToast('جاري إنشاء النسخة الاحتياطية الكاملة...', 'info');

        const response = await fetch(`${getApiUrl()}/settings/backup`, {
            method: 'POST'
        });

        if (response.ok) {
            const result = await response.json();
            showToast(result.message + ' - يمكنك تحميل الملف من القائمة أدناه', 'success');
            // تحديث قائمة النسخ الاحتياطية
            loadFullBackups();
        }
    } catch (error) {
        console.error('Error creating full backup:', error);
        showToast('فشل إنشاء النسخة الاحتياطية الكاملة', 'danger');
    }
}

async function loadFullBackups() {
    try {
        const response = await fetch(`${getApiUrl()}/settings/full-backups`);
        const backups = await response.json();

        const container = document.getElementById('fullBackupsList');

        if (backups.length === 0) {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> لا توجد نسخ احتياطية محفوظة
                </div>
            `;
            return;
        }

        container.innerHTML = backups.map(backup => `
            <div class="card mb-2">
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${backup.fileName}</h6>
                            <small class="text-muted">
                                <i class="bi bi-calendar"></i> ${new Date(backup.createdAt).toLocaleString('ar-SA')}
                                <br>
                                <i class="bi bi-file-earmark-zip"></i> ${backup.formattedSize}
                            </small>
                        </div>
                        <button class="btn btn-sm btn-primary" onclick="downloadBackup('${backup.fileName}')">
                            <i class="bi bi-download"></i> تحميل
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

    } catch (error) {
        console.error('Error loading backups:', error);
        document.getElementById('fullBackupsList').innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> فشل تحميل قائمة النسخ الاحتياطية
            </div>
        `;
    }
}

function downloadBackup(fileName) {
    const url = `${getApiUrl()}/settings/download-backup/${encodeURIComponent(fileName)}`;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    showToast('جاري تحميل النسخة الاحتياطية...', 'info');
}

async function restoreFullBackup() {
    const fileInput = document.getElementById('backupFileInput');
    const file = fileInput.files[0];

    if (!file) {
        showToast('يرجى اختيار ملف النسخة الاحتياطية', 'warning');
        return;
    }

    if (!file.name.endsWith('.zip')) {
        showToast('يجب أن يكون الملف من نوع ZIP', 'warning');
        return;
    }

    const confirmation = confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n\nسيتم إنشاء نسخة احتياطية من الحالة الحالية قبل الاستعادة.');
    if (!confirmation) return;

    try {
        showToast('جاري استعادة النسخة الاحتياطية...', 'info');

        const formData = new FormData();
        formData.append('backupFile', file);

        const response = await fetch(`${getApiUrl()}/settings/restore-full`, {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            showToast(result.message, 'success');

            // إعادة تعيين حقل الملف
            fileInput.value = '';

            // إعادة تحميل قائمة النسخ الاحتياطية
            loadFullBackupsList();

            // إعادة تحميل الصفحة تلقائياً لتطبيق التغييرات
            setTimeout(() => {
                showToast('جاري إعادة تحميل الصفحة لتطبيق التغييرات...', 'info');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            }, 2000);
        } else {
            const error = await response.json();
            showToast(error.message || 'فشل استعادة النسخة الاحتياطية', 'danger');
        }
    } catch (error) {
        console.error('Error restoring backup:', error);
        showToast('فشل استعادة النسخة الاحتياطية', 'danger');
    }
}

async function resetSystem() {
    const confirmation = prompt('اكتب "RESET" للتأكيد:');

    if (confirmation === 'RESET') {
        try {
            const response = await fetch(`${getApiUrl()}/settings/reset`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ confirmation: 'RESET' })
            });

            if (response.ok) {
                const result = await response.json();
                showToast(result.message, 'success');
                setTimeout(() => location.reload(), 2000);
            }
        } catch (error) {
            console.error('Error resetting system:', error);
            showToast('فشل تصفير النظام', 'danger');
        }
    }
}

async function shutdownServer() {
    const confirmation = confirm('هل تريد إيقاف الخادم وإغلاق البرنامج الآن؟');
    if (!confirmation) return;

    try {
        const response = await fetch(`${getApiUrl()}/system/shutdown`, {
            method: 'POST'
        });

        if (response.ok) {
            let message = 'جاري إيقاف الخادم...';
            try {
                const result = await response.json();
                if (result && result.message) {
                    message = result.message;
                }
            } catch (_) {
                // إذا لم يرجع JSON نتجاهل الخطأ ونستخدم الرسالة الافتراضية
            }

            showToast(message, 'info');

            // إعطاء وقت قصير لإيقاف الخادم قبل إعادة تحميل الصفحة
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showToast('تعذر إيقاف الخادم (خطأ في الطلب)', 'danger');
        }
    } catch (error) {
        console.error('Error shutting down server:', error);
        showToast('تعذر الاتصال بالخادم لإيقافه', 'danger');
    }
}

// ==================== Users Management ====================

async function loadUsers() {
    try {
        const response = await fetch(`${getApiUrl()}/users`);
        const users = await response.json();

        const tbody = document.getElementById('usersTableBody');
        if (users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">لا يوجد مستخدمين</td></tr>';
            return;
        }

        tbody.innerHTML = users.map((user, index) => `
            <tr>
                <td>${index + 1}</td>
                <td><strong>${user.username}</strong></td>
                <td>${user.fullName}</td>
                <td><span class="badge ${getRoleBadge(user.role)}">${getRoleLabel(user.role)}</span></td>
                <td>
                    <span class="badge ${user.isActive ? 'bg-success' : 'bg-secondary'}">
                        ${user.isActive ? 'نشط' : 'معطل'}
                    </span>
                </td>
                <td>${user.lastLogin ? formatDate(user.lastLogin) : '-'}</td>
                <td>
                    <button class="btn btn-sm btn-warning" onclick="manageUserPermissions(${user.id}, '${user.username}')" title="الصلاحيات">
                        <i class="bi bi-shield-lock"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="editUser(${user.id})" title="تعديل">
                        <i class="bi bi-pencil"></i>
                    </button>
                    ${user.id !== 1 ? `
                        <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})" title="حذف">
                            <i class="bi bi-trash"></i>
                        </button>
                    ` : ''}
                </td>
            </tr>
        `).join('');
    } catch (error) {
        console.error('Error loading users:', error);
        showToast('فشل تحميل المستخدمين', 'danger');
    }
}

function getRoleBadge(role) {
    const badges = {
        'Admin': 'bg-danger',
        'Manager': 'bg-primary',
        'Cashier': 'bg-success',
        'Employee': 'bg-secondary'
    };
    return badges[role] || 'bg-secondary';
}

function getRoleLabel(role) {
    const labels = {
        'Admin': 'مدير النظام',
        'Manager': 'مدير',
        'Cashier': 'كاشير',
        'Employee': 'موظف'
    };
    return labels[role] || role;
}

function showAddUserModal() {
    const modal = new bootstrap.Modal(document.getElementById('userModal') || createUserModal());
    document.getElementById('userModalTitle').textContent = 'إضافة مستخدم جديد';
    document.getElementById('userForm').reset();
    document.getElementById('userId').value = '';
    document.getElementById('passwordGroup').classList.remove('d-none');
    document.getElementById('userPassword').required = true;
    modal.show();
}

async function editUser(id) {
    try {
        const response = await fetch(`${getApiUrl()}/users/${id}`);
        const user = await response.json();

        const modal = new bootstrap.Modal(document.getElementById('userModal') || createUserModal());
        document.getElementById('userModalTitle').textContent = 'تعديل مستخدم';
        document.getElementById('userId').value = user.id;
        document.getElementById('userUsername').value = user.username;
        document.getElementById('userFullName').value = user.fullName;
        document.getElementById('userRole').value = user.role;
        document.getElementById('userIsActive').checked = user.isActive;
        document.getElementById('passwordGroup').classList.add('d-none');
        document.getElementById('userPassword').required = false;

        modal.show();
    } catch (error) {
        console.error('Error loading user:', error);
        showToast('فشل تحميل بيانات المستخدم', 'danger');
    }
}

async function manageUserPermissions(userId, username) {
    try {
        // الوحدات المتاحة
        const modules = [
            { name: 'Sales', nameAr: 'المبيعات' },
            { name: 'Products', nameAr: 'المنتجات' },
            { name: 'Categories', nameAr: 'الفئات' },
            { name: 'Customers', nameAr: 'العملاء' },
            { name: 'Suppliers', nameAr: 'الموردين' },
            { name: 'Invoices', nameAr: 'الفواتير' },
            { name: 'Employees', nameAr: 'الموظفين' },
            { name: 'Reports', nameAr: 'التقارير' },
            { name: 'Settings', nameAr: 'الإعدادات' },
            { name: 'Purchases', nameAr: 'المشتريات' },
            { name: 'Transactions', nameAr: 'المعاملات' }
        ];

        // تحميل صلاحيات المستخدم الحالية
        const permissionsResponse = await fetch(`${getApiUrl()}/permissions/user/${userId}`);

        if (!permissionsResponse.ok) {
            throw new Error(`HTTP error! status: ${permissionsResponse.status}`);
        }

        const userPermissions = await permissionsResponse.json();

        // إنشاء خريطة للصلاحيات الحالية
        const permissionsMap = {};
        if (Array.isArray(userPermissions)) {
            userPermissions.forEach(p => {
                permissionsMap[p.module] = p;
            });
        }

        // إنشاء نافذة الصلاحيات
        showPermissionsModal(userId, username, modules, permissionsMap);
    } catch (error) {
        console.error('Error loading permissions:', error);
        showToast('فشل تحميل الصلاحيات: ' + error.message, 'danger');
    }
}

async function saveUser(e) {
    e.preventDefault();

    const userId = document.getElementById('userId').value;
    const isEdit = userId !== '';

    const userData = {
        username: document.getElementById('userUsername').value,
        fullName: document.getElementById('userFullName').value,
        role: document.getElementById('userRole').value,
        isActive: document.getElementById('userIsActive').checked
    };

    if (!isEdit || document.getElementById('userPassword').value) {
        userData.password = document.getElementById('userPassword').value;
    }

    try {
        const url = isEdit ? `${getApiUrl()}/users/${userId}` : `${getApiUrl()}/users`;
        const method = isEdit ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(userData)
        });

        const result = await response.json();

        if (response.ok) {
            showToast(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
            loadUsers();
        } else {
            showToast(result.message, 'danger');
        }
    } catch (error) {
        console.error('Error saving user:', error);
        showToast('فشل حفظ المستخدم', 'danger');
    }
}


async function deleteUser(id) {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return;

    try {
        const response = await fetch(`${getApiUrl()}/users/${id}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (response.ok) {
            showToast(result.message, 'success');
            loadUsers();
        } else {
            showToast(result.message, 'danger');
        }
    } catch (error) {
        console.error('Error deleting user:', error);
        showToast('فشل حذف المستخدم', 'danger');
    }
}

async function saveUserPermissions(e) {
    e.preventDefault();

    const userId = document.getElementById('permissionsUserId').value;
    const checkboxes = document.querySelectorAll('#permissionsTableBody input[type="checkbox"]');

    // تجميع الصلاحيات حسب الوحدة
    const permissionsMap = {};

    checkboxes.forEach(cb => {
        const module = cb.dataset.module;
        const action = cb.dataset.action;

        if (!permissionsMap[module]) {
            permissionsMap[module] = {
                module: module,
                canView: false,
                canAdd: false,
                canEdit: false,
                canDelete: false,
                canPrint: false,
                canExport: false
            };
        }

        if (action === 'view') permissionsMap[module].canView = cb.checked;
        if (action === 'add') permissionsMap[module].canAdd = cb.checked;
        if (action === 'edit') permissionsMap[module].canEdit = cb.checked;
        if (action === 'delete') permissionsMap[module].canDelete = cb.checked;
        if (action === 'print') permissionsMap[module].canPrint = cb.checked;
        if (action === 'export') permissionsMap[module].canExport = cb.checked;
    });

    const permissions = Object.values(permissionsMap);

    try {
        const response = await fetch(`${getApiUrl()}/permissions/user/${userId}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(permissions)
        });

        const result = await response.json();

        if (response.ok) {
            showToast(result.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('permissionsModal')).hide();
        } else {
            showToast(result.message || 'فشل حفظ الصلاحيات', 'danger');
        }
    } catch (error) {
        console.error('Error saving permissions:', error);
        showToast('فشل حفظ الصلاحيات: ' + error.message, 'danger');
    }
}

function createUserModal() {
    const modalHtml = `
        <div class="modal fade" id="userModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="userModalTitle">إضافة مستخدم</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="userForm" onsubmit="saveUser(event)">
                        <div class="modal-body">
                            <input type="hidden" id="userId">

                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="userUsername" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="userFullName" required>
                            </div>

                            <div class="mb-3" id="passwordGroup">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="userPassword">
                                <small class="text-muted">اتركه فارغاً للاحتفاظ بكلمة المرور الحالية</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الدور</label>
                                <select class="form-select" id="userRole" required>
                                    <option value="Admin">مدير النظام</option>
                                    <option value="Manager">مدير</option>
                                    <option value="Cashier" selected>كاشير</option>
                                    <option value="Employee">موظف</option>
                                </select>
                            </div>

                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="userIsActive" checked>
                                <label class="form-check-label" for="userIsActive">
                                    نشط
                                </label>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="submit" class="btn btn-primary">حفظ</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    return document.getElementById('userModal');
}

function createPermissionsModal() {
    const modalHtml = `
        <div class="modal fade" id="permissionsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="permissionsModalTitle">صلاحيات المستخدم</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="permissionsForm" onsubmit="saveUserPermissions(event)">
                        <div class="modal-body">
                            <input type="hidden" id="permissionsUserId">

                            <div class="mb-3">
                                <div class="d-flex gap-2 mb-2">
                                    <button type="button" class="btn btn-sm btn-success" onclick="selectAllPermissions()">
                                        <i class="bi bi-check-all"></i> تحديد الكل
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger" onclick="clearAllPermissions()">
                                        <i class="bi bi-x-circle"></i> إلغاء الكل
                                    </button>
                                    <button type="button" class="btn btn-sm btn-info" onclick="selectViewOnly()">
                                        <i class="bi bi-eye"></i> عرض فقط
                                    </button>
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="loadDefaultPermissions('Admin')">
                                        <i class="bi bi-shield-fill"></i> صلاحيات مدير
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="loadDefaultPermissions('Manager')">
                                        <i class="bi bi-person-badge"></i> صلاحيات مدير فرع
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="loadDefaultPermissions('Cashier')">
                                        <i class="bi bi-cash-coin"></i> صلاحيات كاشير
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="loadDefaultPermissions('Employee')">
                                        <i class="bi bi-person"></i> صلاحيات موظف
                                    </button>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الوحدة</th>
                                            <th class="text-center">عرض</th>
                                            <th class="text-center">إضافة</th>
                                            <th class="text-center">تعديل</th>
                                            <th class="text-center">حذف</th>
                                            <th class="text-center">طباعة</th>
                                            <th class="text-center">تصدير</th>
                                        </tr>
                                    </thead>
                                    <tbody id="permissionsTableBody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> حفظ الصلاحيات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    return document.getElementById('permissionsModal');
}

function selectAllPermissions() {
    document.querySelectorAll('#permissionsTableBody input[type="checkbox"]').forEach(cb => {
        cb.checked = true;
    });
}

function clearAllPermissions() {
    document.querySelectorAll('#permissionsTableBody input[type="checkbox"]').forEach(cb => {
        cb.checked = false;
    });
}

function selectViewOnly() {
    document.querySelectorAll('#permissionsTableBody input[type="checkbox"]').forEach(cb => {
        cb.checked = cb.dataset.action === 'view';
    });
}

async function loadDefaultPermissions(role) {
    const userId = document.getElementById('permissionsUserId').value;

    if (!confirm(`هل تريد تحميل الصلاحيات الافتراضية لدور "${getRoleLabel(role)}"؟\nسيتم استبدال الصلاحيات الحالية.`)) {
        return;
    }

    try {
        const response = await fetch(`${API_URL}/permissions/create-default/${userId}?role=${role}`, {
            method: 'POST'
        });

        const result = await response.json();

        if (response.ok) {
            showToast(result.message, 'success');
            // إعادة تحميل الصلاحيات
            const username = document.getElementById('permissionsModalTitle').textContent.replace('صلاحيات: ', '');
            manageUserPermissions(userId, username);
        } else {
            showToast(result.message, 'danger');
        }
    } catch (error) {
        console.error('Error loading default permissions:', error);
        showToast('فشل تحميل الصلاحيات الافتراضية', 'danger');
    }
}

// ==================== Permissions Management ====================

function loadPermissions() {
    // هذه الدالة الآن فقط للعرض - الصلاحيات الفعلية تُدار من خلال نافذة المستخدم
    const infoHtml = `
        <div class="alert alert-info">
            <h5><i class="bi bi-info-circle"></i> إدارة الصلاحيات</h5>
            <p class="mb-0">
                لتحديد صلاحيات كل مستخدم، اذهب إلى تبويب <strong>"المستخدمين"</strong>
                واضغط على زر <i class="bi bi-shield-lock"></i> بجانب اسم المستخدم.
            </p>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0"><i class="bi bi-shield-fill"></i> مدير النظام (Admin)</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small">صلاحيات كاملة على جميع الوحدات</p>
                        <ul class="list-unstyled mb-0">
                            <li><i class="bi bi-check-circle text-success"></i> جميع الصلاحيات</li>
                            <li><i class="bi bi-check-circle text-success"></i> إدارة المستخدمين</li>
                            <li><i class="bi bi-check-circle text-success"></i> النسخ الاحتياطي</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="bi bi-person-badge"></i> مدير (Manager)</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small">صلاحيات إدارية محدودة</p>
                        <ul class="list-unstyled mb-0">
                            <li><i class="bi bi-check-circle text-success"></i> المبيعات والمشتريات</li>
                            <li><i class="bi bi-check-circle text-success"></i> إدارة المنتجات</li>
                            <li><i class="bi bi-x-circle text-danger"></i> لا يمكن حذف البيانات</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="bi bi-cash-coin"></i> كاشير (Cashier)</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small">صلاحيات نقاط البيع</p>
                        <ul class="list-unstyled mb-0">
                            <li><i class="bi bi-check-circle text-success"></i> إضافة مبيعات</li>
                            <li><i class="bi bi-check-circle text-success"></i> طباعة الفواتير</li>
                            <li><i class="bi bi-x-circle text-danger"></i> لا يمكن التعديل أو الحذف</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="bi bi-person"></i> موظف (Employee)</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small">صلاحيات عرض فقط</p>
                        <ul class="list-unstyled mb-0">
                            <li><i class="bi bi-check-circle text-success"></i> عرض المبيعات</li>
                            <li><i class="bi bi-check-circle text-success"></i> عرض المنتجات</li>
                            <li><i class="bi bi-x-circle text-danger"></i> لا يمكن الإضافة أو التعديل</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-warning">
            <h6><i class="bi bi-exclamation-triangle"></i> ملاحظة هامة:</h6>
            <p class="mb-0">
                يمكنك تخصيص صلاحيات كل مستخدم بشكل فردي من خلال زر
                <span class="badge bg-warning text-dark"><i class="bi bi-shield-lock"></i></span>
                في جدول المستخدمين.
            </p>
        </div>
    `;

    const container = document.getElementById('permissions');
    if (container) {
        const contentDiv = container.querySelector('.tab-pane') || container;
        contentDiv.innerHTML = infoHtml;
    }
}

// ==================== Permissions Modal ====================

function showPermissionsModal(userId, username, modules, permissionsMap) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('permissionsModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'permissionsModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="permissionsModalTitle">إدارة الصلاحيات</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" id="permissionsUserId">

                        <!-- Quick Actions -->
                        <div class="mb-3">
                            <button class="btn btn-sm btn-success" onclick="selectAllPermissions()">
                                <i class="bi bi-check-all"></i> تحديد الكل
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearAllPermissions()">
                                <i class="bi bi-x-circle"></i> إلغاء الكل
                            </button>
                            <button class="btn btn-sm btn-info" onclick="selectViewOnly()">
                                <i class="bi bi-eye"></i> عرض فقط
                            </button>
                        </div>

                        <!-- Default Role Templates -->
                        <div class="mb-3">
                            <label class="form-label">تحميل صلاحيات افتراضية:</label>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-danger" onclick="loadDefaultPermissions('admin')">
                                    <i class="bi bi-shield-fill"></i> Admin
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="loadDefaultPermissions('manager')">
                                    <i class="bi bi-person-badge"></i> Manager
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="loadDefaultPermissions('cashier')">
                                    <i class="bi bi-cash-coin"></i> Cashier
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="loadDefaultPermissions('employee')">
                                    <i class="bi bi-person"></i> Employee
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الوحدة</th>
                                        <th class="text-center">عرض</th>
                                        <th class="text-center">إضافة</th>
                                        <th class="text-center">تعديل</th>
                                        <th class="text-center">حذف</th>
                                        <th class="text-center">طباعة</th>
                                        <th class="text-center">تصدير</th>
                                    </tr>
                                </thead>
                                <tbody id="permissionsTableBody"></tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="savePermissions()">
                            <i class="bi bi-save"></i> حفظ الصلاحيات
                        </button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Set modal title and user ID
    document.getElementById('permissionsModalTitle').textContent = `صلاحيات: ${username}`;
    document.getElementById('permissionsUserId').value = userId;

    // Fill permissions table
    const tbody = document.getElementById('permissionsTableBody');
    tbody.innerHTML = modules.map(module => {
        const perm = permissionsMap[module.name] || {};
        return `
            <tr>
                <td><strong>${module.nameAr}</strong></td>
                <td class="text-center">
                    <input type="checkbox" class="form-check-input permission-checkbox"
                        data-module="${module.name}" data-action="view"
                        ${perm.canView ? 'checked' : ''}>
                </td>
                <td class="text-center">
                    <input type="checkbox" class="form-check-input permission-checkbox"
                        data-module="${module.name}" data-action="add"
                        ${perm.canAdd ? 'checked' : ''}>
                </td>
                <td class="text-center">
                    <input type="checkbox" class="form-check-input permission-checkbox"
                        data-module="${module.name}" data-action="edit"
                        ${perm.canEdit ? 'checked' : ''}>
                </td>
                <td class="text-center">
                    <input type="checkbox" class="form-check-input permission-checkbox"
                        data-module="${module.name}" data-action="delete"
                        ${perm.canDelete ? 'checked' : ''}>
                </td>
                <td class="text-center">
                    <input type="checkbox" class="form-check-input permission-checkbox"
                        data-module="${module.name}" data-action="print"
                        ${perm.canPrint ? 'checked' : ''}>
                </td>
                <td class="text-center">
                    <input type="checkbox" class="form-check-input permission-checkbox"
                        data-module="${module.name}" data-action="export"
                        ${perm.canExport ? 'checked' : ''}>
                </td>
            </tr>
        `;
    }).join('');

    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

async function savePermissions() {
    const userId = document.getElementById('permissionsUserId').value;
    const checkboxes = document.querySelectorAll('.permission-checkbox');

    // Group permissions by module
    const permissionsMap = {};
    checkboxes.forEach(cb => {
        const module = cb.getAttribute('data-module');
        const action = cb.getAttribute('data-action');

        if (!permissionsMap[module]) {
            permissionsMap[module] = {
                module: module,
                canView: false,
                canAdd: false,
                canEdit: false,
                canDelete: false,
                canPrint: false,
                canExport: false
            };
        }

        if (action === 'view') permissionsMap[module].canView = cb.checked;
        if (action === 'add') permissionsMap[module].canAdd = cb.checked;
        if (action === 'edit') permissionsMap[module].canEdit = cb.checked;
        if (action === 'delete') permissionsMap[module].canDelete = cb.checked;
        if (action === 'print') permissionsMap[module].canPrint = cb.checked;
        if (action === 'export') permissionsMap[module].canExport = cb.checked;
    });

    // Convert to array
    const permissions = Object.values(permissionsMap);

    try {
        const response = await fetch(`${getApiUrl()}/permissions/user/${userId}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(permissions)
        });

        if (response.ok) {
            showToast('تم حفظ الصلاحيات بنجاح', 'success');
            const modalElement = document.getElementById('permissionsModal');
            const modalInstance = bootstrap.Modal.getInstance(modalElement);
            if (modalInstance) {
                modalInstance.hide();
            }
            loadUsers();
        } else {
            const errorData = await response.json();
            showToast('فشل حفظ الصلاحيات: ' + (errorData.message || response.statusText), 'danger');
        }
    } catch (error) {
        console.error('Error saving permissions:', error);
        showToast('حدث خطأ أثناء حفظ الصلاحيات: ' + error.message, 'danger');
    }
}

function selectAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(cb => cb.checked = true);
}

function clearAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(cb => cb.checked = false);
}

function selectViewOnly() {
    document.querySelectorAll('.permission-checkbox').forEach(cb => {
        const action = cb.getAttribute('data-action');
        cb.checked = (action === 'view');
    });
}

async function loadDefaultPermissions(role) {
    const userId = document.getElementById('permissionsUserId').value;

    try {
        const response = await fetch(`${getApiUrl()}/permissions/create-default/${userId}?role=${role}`, {
            method: 'POST'
        });

        if (response.ok) {
            const data = await response.json();
            showToast(`تم تحميل صلاحيات ${role} الافتراضية`, 'success');

            // Update checkboxes
            const permissionsMap = {};
            if (data.permissions && Array.isArray(data.permissions)) {
                data.permissions.forEach(p => {
                    permissionsMap[p.module] = p;
                });
            }

            document.querySelectorAll('.permission-checkbox').forEach(cb => {
                const module = cb.getAttribute('data-module');
                const action = cb.getAttribute('data-action');
                const perm = permissionsMap[module];

                if (perm) {
                    if (action === 'view') cb.checked = perm.canView;
                    if (action === 'add') cb.checked = perm.canAdd;
                    if (action === 'edit') cb.checked = perm.canEdit;
                    if (action === 'delete') cb.checked = perm.canDelete;
                    if (action === 'print') cb.checked = perm.canPrint;
                    if (action === 'export') cb.checked = perm.canExport;
                } else {
                    cb.checked = false;
                }
            });
        } else {
            const errorData = await response.json();
            showToast('فشل تحميل الصلاحيات الافتراضية: ' + (errorData.message || response.statusText), 'danger');
        }
    } catch (error) {
        console.error('Error loading default permissions:', error);
        showToast('حدث خطأ أثناء تحميل الصلاحيات: ' + error.message, 'danger');
    }
}

// تحميل إعدادات الطباعة عند فتح التبويب
function loadPrinterSettings() {
    const printerSettings = JSON.parse(localStorage.getItem('printerSettings') || '{}');

    if (printerSettings.type) document.getElementById('printerType').value = printerSettings.type;
    if (printerSettings.bluetoothName) document.getElementById('bluetoothPrinterName').value = printerSettings.bluetoothName;
    if (printerSettings.usbVendorId) document.getElementById('usbVendorId').value = printerSettings.usbVendorId;
    if (printerSettings.usbProductId) document.getElementById('usbProductId').value = printerSettings.usbProductId;
    if (printerSettings.usbPrinterName) document.getElementById('usbPrinterName').value = printerSettings.usbPrinterName;
    if (printerSettings.ip) document.getElementById('printerIp').value = printerSettings.ip;
    if (printerSettings.port) document.getElementById('printerPort').value = printerSettings.port;
    if (printerSettings.paperWidth) document.getElementById('paperWidth').value = printerSettings.paperWidth;
    if (printerSettings.copies) document.getElementById('printCopies').value = printerSettings.copies;
    if (printerSettings.fontSize) document.getElementById('fontSize').value = printerSettings.fontSize;
    if (printerSettings.fontFamily) document.getElementById('fontFamily').value = printerSettings.fontFamily;
    if (printerSettings.headerColor) document.getElementById('headerColor').value = printerSettings.headerColor;
    if (printerSettings.marginTop) document.getElementById('marginTop').value = printerSettings.marginTop;
    if (printerSettings.marginBottom) document.getElementById('marginBottom').value = printerSettings.marginBottom;
    if (printerSettings.marginLeft) document.getElementById('marginLeft').value = printerSettings.marginLeft;
    if (printerSettings.marginRight) document.getElementById('marginRight').value = printerSettings.marginRight;

    document.getElementById('autoPrint').checked = printerSettings.autoPrint || false;
    document.getElementById('printLogo').checked = printerSettings.printLogo !== false;
    document.getElementById('printBarcode').checked = printerSettings.printBarcode !== false;
    document.getElementById('printHeader').checked = printerSettings.printHeader !== false;
    document.getElementById('printFooter').checked = printerSettings.printFooter !== false;
}

// استدعاء تحميل الإعدادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadPrinterSettings();
});
