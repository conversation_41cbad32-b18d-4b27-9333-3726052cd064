async function loadInvoicesPage() {
    const page = document.getElementById('invoicesPage');
    page.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">الفواتير</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-3">
                        <select class="form-select" id="invoiceType" onchange="loadInvoicesList()">
                            <option value="">جميع الأنواع</option>
                            <option value="Sale">مبيعات</option>
                            <option value="Purchase">مشتريات</option>
                            <option value="SaleReturn">مرتجع مبيعات</option>
                            <option value="PurchaseReturn">مرتجع مشتريات</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" class="form-control" id="fromDate" onchange="loadInvoicesList()">
                    </div>
                    <div class="col-md-3">
                        <input type="date" class="form-control" id="toDate" onchange="loadInvoicesList()">
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>النوع</th>
                                <th>العميل/المورد</th>
                                <th>الإجمالي</th>
                                <th>المدفوع</th>
                                <th>الباقي</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="invoicesTable"></tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
    
    loadInvoicesList();
}

async function loadInvoicesList() {
    try {
        const type = document.getElementById('invoiceType')?.value || '';
        const fromDate = document.getElementById('fromDate')?.value || '';
        const toDate = document.getElementById('toDate')?.value || '';

        let url = `${API_URL}/invoices?`;
        if (type) url += `type=${type}&`;
        if (fromDate) url += `fromDate=${fromDate}&`;
        if (toDate) url += `toDate=${toDate}&`;

        const response = await fetch(url);
        const invoices = await response.json();

        const tbody = document.getElementById('invoicesTable');
        tbody.innerHTML = invoices.map((inv, index) => `
            <tr class="invoice-row" data-invoice-id="${inv.id}">
                <td>
                    <button class="btn btn-sm btn-link p-0" onclick="toggleInvoiceDetails(${inv.id}, ${index})">
                        <i class="bi bi-chevron-left" id="chevron-${index}"></i>
                    </button>
                    <strong>${inv.invoiceNumber}</strong>
                </td>
                <td>${formatDate(inv.invoiceDate)}</td>
                <td><span class="badge ${getInvoiceTypeBadge(inv.type)}">${getInvoiceTypeLabel(inv.type)}</span></td>
                <td>${inv.customer?.name || inv.supplier?.name || '-'}</td>
                <td><strong>${formatCurrency(inv.total)}</strong></td>
                <td class="text-success">${formatCurrency(inv.paid)}</td>
                <td class="${inv.remaining > 0 ? 'text-danger fw-bold' : 'text-muted'}">${formatCurrency(inv.remaining)}</td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="viewInvoiceModal(${inv.id})" title="عرض">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="printInvoice(${inv.id})" title="طباعة">
                        <i class="bi bi-printer"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteInvoice(${inv.id})" title="حذف">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
            <tr id="details-${index}" class="invoice-details-row" style="display: none;">
                <td colspan="8" class="p-0">
                    <div class="invoice-details-container bg-light p-3">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </td>
            </tr>
        `).join('');
    } catch (error) {
        console.error('Error loading invoices:', error);
    }
}

function getInvoiceTypeLabel(type) {
    const labels = {
        'Sale': 'مبيعات',
        'Purchase': 'مشتريات',
        'SaleReturn': 'مرتجع مبيعات',
        'PurchaseReturn': 'مرتجع مشتريات'
    };
    return labels[type] || type;
}

function getInvoiceTypeBadge(type) {
    const badges = {
        'Sale': 'bg-success',
        'Purchase': 'bg-primary',
        'SaleReturn': 'bg-warning text-dark',
        'PurchaseReturn': 'bg-info'
    };
    return badges[type] || 'bg-secondary';
}

async function toggleInvoiceDetails(invoiceId, index) {
    const detailsRow = document.getElementById(`details-${index}`);
    const chevron = document.getElementById(`chevron-${index}`);

    if (detailsRow.style.display === 'none') {
        // Show details
        detailsRow.style.display = 'table-row';
        chevron.classList.remove('bi-chevron-left');
        chevron.classList.add('bi-chevron-down');

        // Load invoice details
        await loadInvoiceDetails(invoiceId, index);
    } else {
        // Hide details
        detailsRow.style.display = 'none';
        chevron.classList.remove('bi-chevron-down');
        chevron.classList.add('bi-chevron-left');
    }
}

async function loadInvoiceDetails(invoiceId, index) {
    try {
        const response = await fetch(`${API_URL}/invoices/${invoiceId}`);
        const invoice = await response.json();

        const container = document.querySelector(`#details-${index} .invoice-details-container`);

        container.innerHTML = `
            <div class="row">
                <!-- معلومات الفاتورة -->
                <div class="col-md-4 mb-3">
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white py-2">
                            <h6 class="mb-0"><i class="bi bi-info-circle"></i> معلومات الفاتورة</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm table-borderless mb-0">
                                <tr>
                                    <td class="text-muted">رقم الفاتورة:</td>
                                    <td><strong>${invoice.invoiceNumber}</strong></td>
                                </tr>
                                <tr>
                                    <td class="text-muted">التاريخ:</td>
                                    <td>${formatDate(invoice.invoiceDate)}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">النوع:</td>
                                    <td><span class="badge ${getInvoiceTypeBadge(invoice.type)}">${getInvoiceTypeLabel(invoice.type)}</span></td>
                                </tr>
                                <tr>
                                    <td class="text-muted">نوع الدفع:</td>
                                    <td>${getPaymentTypeLabel(invoice.paymentType)}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">العميل/المورد:</td>
                                    <td>${invoice.customer?.name || invoice.supplier?.name || '-'}</td>
                                </tr>
                                ${invoice.notes ? `
                                <tr>
                                    <td class="text-muted">ملاحظات:</td>
                                    <td>${invoice.notes}</td>
                                </tr>
                                ` : ''}
                            </table>
                        </div>
                    </div>
                </div>

                <!-- الملخص المالي -->
                <div class="col-md-4 mb-3">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white py-2">
                            <h6 class="mb-0"><i class="bi bi-cash-stack"></i> الملخص المالي</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm table-borderless mb-0">
                                <tr>
                                    <td class="text-muted">المجموع الفرعي:</td>
                                    <td class="text-end">${formatCurrency(invoice.subTotal)}</td>
                                </tr>
                                ${invoice.discount > 0 ? `
                                <tr>
                                    <td class="text-muted">الخصم:</td>
                                    <td class="text-end text-warning">- ${formatCurrency(invoice.discount)}</td>
                                </tr>
                                ` : ''}
                                ${invoice.tax > 0 ? `
                                <tr>
                                    <td class="text-muted">الضريبة:</td>
                                    <td class="text-end">+ ${formatCurrency(invoice.tax)}</td>
                                </tr>
                                ` : ''}
                                <tr class="border-top">
                                    <td class="text-muted"><strong>الإجمالي:</strong></td>
                                    <td class="text-end"><strong class="text-primary fs-5">${formatCurrency(invoice.total)}</strong></td>
                                </tr>
                                <tr>
                                    <td class="text-muted">المدفوع:</td>
                                    <td class="text-end text-success">${formatCurrency(invoice.paid)}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted"><strong>الباقي:</strong></td>
                                    <td class="text-end"><strong class="${invoice.remaining > 0 ? 'text-danger' : 'text-success'}">${formatCurrency(invoice.remaining)}</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات المنتجات -->
                <div class="col-md-4 mb-3">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white py-2">
                            <h6 class="mb-0"><i class="bi bi-box-seam"></i> إحصائيات المنتجات</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm table-borderless mb-0">
                                <tr>
                                    <td class="text-muted">عدد الأصناف:</td>
                                    <td><span class="badge bg-secondary">${invoice.items?.length || 0}</span></td>
                                </tr>
                                <tr>
                                    <td class="text-muted">إجمالي الكمية:</td>
                                    <td><span class="badge bg-primary">${invoice.items?.reduce((sum, item) => sum + item.quantity, 0) || 0}</span></td>
                                </tr>
                                ${invoice.type === 'Sale' ? `
                                <tr>
                                    <td class="text-muted">التكلفة:</td>
                                    <td class="text-danger">${formatCurrency(invoice.items?.reduce((sum, item) => sum + (item.purchasePrice * item.quantity), 0) || 0)}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">الربح:</td>
                                    <td class="text-success"><strong>${formatCurrency(invoice.items?.reduce((sum, item) => sum + item.profit, 0) || 0)}</strong></td>
                                </tr>
                                <tr>
                                    <td class="text-muted">هامش الربح:</td>
                                    <td>
                                        ${(() => {
                                            const totalProfit = invoice.items?.reduce((sum, item) => sum + item.profit, 0) || 0;
                                            const margin = invoice.total > 0 ? (totalProfit / invoice.total * 100) : 0;
                                            return `<span class="badge ${margin > 30 ? 'bg-success' : margin > 15 ? 'bg-warning text-dark' : 'bg-danger'}">${margin.toFixed(1)}%</span>`;
                                        })()}
                                    </td>
                                </tr>
                                ` : ''}
                                <tr>
                                    <td class="text-muted">متوسط السعر:</td>
                                    <td>${formatCurrency((invoice.total / (invoice.items?.reduce((sum, item) => sum + item.quantity, 0) || 1)))}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول المنتجات -->
            <div class="card mt-3">
                <div class="card-header bg-dark text-white py-2">
                    <h6 class="mb-0"><i class="bi bi-list-ul"></i> تفاصيل المنتجات</h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover table-sm mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>سعر الوحدة</th>
                                    ${invoice.type === 'Sale' ? '<th>سعر الشراء</th>' : ''}
                                    ${invoice.discount > 0 || invoice.items?.some(i => i.discount > 0) ? '<th>الخصم</th>' : ''}
                                    <th>الإجمالي</th>
                                    ${invoice.type === 'Sale' ? '<th>الربح</th><th>هامش الربح</th>' : ''}
                                </tr>
                            </thead>
                            <tbody>
                                ${invoice.items?.map((item, idx) => `
                                    <tr>
                                        <td>${idx + 1}</td>
                                        <td>
                                            <strong>${item.productName}</strong>
                                            ${item.productId ? `<br><small class="text-muted">كود: ${item.productId}</small>` : ''}
                                        </td>
                                        <td><span class="badge bg-secondary">${item.quantity}</span></td>
                                        <td>${formatCurrency(item.unitPrice)}</td>
                                        ${invoice.type === 'Sale' ? `<td class="text-muted">${formatCurrency(item.purchasePrice)}</td>` : ''}
                                        ${invoice.discount > 0 || invoice.items?.some(i => i.discount > 0) ? `<td class="text-warning">${item.discount > 0 ? formatCurrency(item.discount) : '-'}</td>` : ''}
                                        <td><strong>${formatCurrency(item.total)}</strong></td>
                                        ${invoice.type === 'Sale' ? `
                                            <td class="text-success"><strong>${formatCurrency(item.profit)}</strong></td>
                                            <td>
                                                ${(() => {
                                                    const margin = item.total > 0 ? (item.profit / item.total * 100) : 0;
                                                    return `<span class="badge ${margin > 30 ? 'bg-success' : margin > 15 ? 'bg-warning text-dark' : 'bg-danger'}">${margin.toFixed(1)}%</span>`;
                                                })()}
                                            </td>
                                        ` : ''}
                                    </tr>
                                `).join('') || '<tr><td colspan="10" class="text-center text-muted">لا توجد منتجات</td></tr>'}
                            </tbody>
                            ${invoice.items?.length > 0 ? `
                            <tfoot class="table-light fw-bold">
                                <tr>
                                    <td colspan="2">الإجمالي</td>
                                    <td><span class="badge bg-primary">${invoice.items.reduce((sum, item) => sum + item.quantity, 0)}</span></td>
                                    <td>-</td>
                                    ${invoice.type === 'Sale' ? '<td>-</td>' : ''}
                                    ${invoice.discount > 0 || invoice.items?.some(i => i.discount > 0) ? `<td class="text-warning">${formatCurrency(invoice.items.reduce((sum, item) => sum + (item.discount || 0), 0))}</td>` : ''}
                                    <td><strong>${formatCurrency(invoice.items.reduce((sum, item) => sum + item.total, 0))}</strong></td>
                                    ${invoice.type === 'Sale' ? `
                                        <td class="text-success"><strong>${formatCurrency(invoice.items.reduce((sum, item) => sum + item.profit, 0))}</strong></td>
                                        <td>-</td>
                                    ` : ''}
                                </tr>
                            </tfoot>
                            ` : ''}
                        </table>
                    </div>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('Error loading invoice details:', error);
        const container = document.querySelector(`#details-${index} .invoice-details-container`);
        container.innerHTML = '<div class="alert alert-danger">فشل تحميل تفاصيل الفاتورة</div>';
    }
}

function getPaymentTypeLabel(type) {
    const labels = {
        'Cash': 'نقدي',
        'Credit': 'آجل',
        'Wholesale': 'جملة',
        'Installment': 'قسط'
    };
    return labels[type] || type;
}

async function viewInvoiceModal(id) {
    try {
        const response = await fetch(`${API_URL}/invoices/${id}`);
        const invoice = await response.json();

        // Create modal
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'invoiceModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">فاتورة رقم: ${invoice.invoiceNumber}</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                        <div id="modalInvoiceDetails"></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="printInvoice(${id})">
                            <i class="bi bi-printer"></i> طباعة
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // Load details into modal
        await loadInvoiceDetailsIntoModal(invoice);

        // Remove modal from DOM when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    } catch (error) {
        console.error('Error viewing invoice:', error);
        showToast('فشل عرض الفاتورة', 'error');
    }
}

async function loadInvoiceDetailsIntoModal(invoice) {
    const container = document.getElementById('modalInvoiceDetails');
    // Use the same HTML structure as loadInvoiceDetails but without the index parameter
    container.innerHTML = `
        <div class="row">
            <!-- Same content as loadInvoiceDetails -->
            <div class="col-md-6 mb-3">
                <h6 class="border-bottom pb-2"><i class="bi bi-info-circle"></i> معلومات الفاتورة</h6>
                <table class="table table-sm">
                    <tr><td class="text-muted">رقم الفاتورة:</td><td><strong>${invoice.invoiceNumber}</strong></td></tr>
                    <tr><td class="text-muted">التاريخ:</td><td>${formatDate(invoice.invoiceDate)}</td></tr>
                    <tr><td class="text-muted">النوع:</td><td><span class="badge ${getInvoiceTypeBadge(invoice.type)}">${getInvoiceTypeLabel(invoice.type)}</span></td></tr>
                    <tr><td class="text-muted">نوع الدفع:</td><td>${getPaymentTypeLabel(invoice.paymentType)}</td></tr>
                    <tr><td class="text-muted">العميل/المورد:</td><td>${invoice.customer?.name || invoice.supplier?.name || '-'}</td></tr>
                </table>
            </div>
            <div class="col-md-6 mb-3">
                <h6 class="border-bottom pb-2"><i class="bi bi-cash-stack"></i> الملخص المالي</h6>
                <table class="table table-sm">
                    <tr><td class="text-muted">المجموع الفرعي:</td><td class="text-end">${formatCurrency(invoice.subTotal)}</td></tr>
                    ${invoice.discount > 0 ? `<tr><td class="text-muted">الخصم:</td><td class="text-end text-warning">- ${formatCurrency(invoice.discount)}</td></tr>` : ''}
                    ${invoice.tax > 0 ? `<tr><td class="text-muted">الضريبة:</td><td class="text-end">+ ${formatCurrency(invoice.tax)}</td></tr>` : ''}
                    <tr class="border-top"><td><strong>الإجمالي:</strong></td><td class="text-end"><strong class="text-primary">${formatCurrency(invoice.total)}</strong></td></tr>
                    <tr><td class="text-muted">المدفوع:</td><td class="text-end text-success">${formatCurrency(invoice.paid)}</td></tr>
                    <tr><td><strong>الباقي:</strong></td><td class="text-end"><strong class="${invoice.remaining > 0 ? 'text-danger' : 'text-success'}">${formatCurrency(invoice.remaining)}</strong></td></tr>
                </table>
            </div>
        </div>

        <h6 class="border-bottom pb-2 mt-3"><i class="bi bi-list-ul"></i> المنتجات</h6>
        <div class="table-responsive">
            <table class="table table-hover table-sm">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>سعر الوحدة</th>
                        ${invoice.type === 'Sale' ? '<th>سعر الشراء</th><th>الربح</th>' : ''}
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${invoice.items?.map((item, idx) => `
                        <tr>
                            <td>${idx + 1}</td>
                            <td><strong>${item.productName}</strong></td>
                            <td><span class="badge bg-secondary">${item.quantity}</span></td>
                            <td>${formatCurrency(item.unitPrice)}</td>
                            ${invoice.type === 'Sale' ? `
                                <td class="text-muted">${formatCurrency(item.purchasePrice)}</td>
                                <td class="text-success">${formatCurrency(item.profit)}</td>
                            ` : ''}
                            <td><strong>${formatCurrency(item.total)}</strong></td>
                        </tr>
                    `).join('') || '<tr><td colspan="7" class="text-center text-muted">لا توجد منتجات</td></tr>'}
                </tbody>
            </table>
        </div>
    `;
}

async function printInvoice(id) {
    try {
        const response = await fetch(`${API_URL}/invoices/${id}`);
        const invoice = await response.json();

        // Open print window
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة ${invoice.invoiceNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; padding: 20px; }
                    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                    th { background-color: #f2f2f2; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .total { font-weight: bold; font-size: 1.2em; }
                    @media print { button { display: none; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>أريدوو - Aredoo POS</h1>
                    <h2>فاتورة ${getInvoiceTypeLabel(invoice.type)}</h2>
                    <p>رقم الفاتورة: ${invoice.invoiceNumber} | التاريخ: ${formatDate(invoice.invoiceDate)}</p>
                </div>

                <table>
                    <tr><td><strong>العميل/المورد:</strong></td><td>${invoice.customer?.name || invoice.supplier?.name || '-'}</td></tr>
                    <tr><td><strong>نوع الدفع:</strong></td><td>${getPaymentTypeLabel(invoice.paymentType)}</td></tr>
                </table>

                <h3>المنتجات:</h3>
                <table>
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>سعر الوحدة</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${invoice.items?.map((item, idx) => `
                            <tr>
                                <td>${idx + 1}</td>
                                <td>${item.productName}</td>
                                <td>${item.quantity}</td>
                                <td>${formatCurrency(item.unitPrice)}</td>
                                <td>${formatCurrency(item.total)}</td>
                            </tr>
                        `).join('') || ''}
                    </tbody>
                </table>

                <table>
                    <tr><td><strong>المجموع الفرعي:</strong></td><td>${formatCurrency(invoice.subTotal)}</td></tr>
                    ${invoice.discount > 0 ? `<tr><td><strong>الخصم:</strong></td><td>${formatCurrency(invoice.discount)}</td></tr>` : ''}
                    ${invoice.tax > 0 ? `<tr><td><strong>الضريبة:</strong></td><td>${formatCurrency(invoice.tax)}</td></tr>` : ''}
                    <tr class="total"><td>الإجمالي:</td><td>${formatCurrency(invoice.total)}</td></tr>
                    <tr><td><strong>المدفوع:</strong></td><td>${formatCurrency(invoice.paid)}</td></tr>
                    <tr><td><strong>الباقي:</strong></td><td>${formatCurrency(invoice.remaining)}</td></tr>
                </table>

                <button onclick="window.print()">طباعة</button>
            </body>
            </html>
        `);
        printWindow.document.close();
    } catch (error) {
        console.error('Error printing invoice:', error);
        showToast('فشل طباعة الفاتورة', 'error');
    }
}

async function deleteInvoice(id) {
    if (confirm('هل تريد حذف هذه الفاتورة؟')) {
        try {
            const response = await fetch(`${API_URL}/invoices/${id}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                showToast('تم حذف الفاتورة بنجاح', 'success');
                loadInvoicesList();
            }
        } catch (error) {
            console.error('Error deleting invoice:', error);
        }
    }
}

